package com.extracme.nevmp.config;

import com.danga.MemCached.SockIOPool;
import com.extracme.nevmp.filter.EncodingFilter;
import com.extracme.nevmp.filter.HealthCheckFilter;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.SSLContext;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;

/**
 * <AUTHOR>
 * @Description
 */
@Configuration
public class NevmpPublicConfiguration {

    /**
     * memcache bean
     * @param address memcache地址
     * @return
     */
    @Bean(initMethod = "initialize", destroyMethod = "shutDown")
    SockIOPool memcachePool(@Value("${memcache.address}") String address){
        SockIOPool pool = SockIOPool.getInstance();
        pool.setServers(new String[]{address});
        return pool;
    }

    @Bean
    public FilterRegistrationBean<HealthCheckFilter> healthCheckFilterRegistrationBean() {
        FilterRegistrationBean<HealthCheckFilter> registrationBean = new FilterRegistrationBean<>(new HealthCheckFilter());
        //定义拦截器优先级
        registrationBean.setOrder(Ordered.HIGHEST_PRECEDENCE + 70);
        return registrationBean;
    }

    @Bean
    public FilterRegistrationBean<EncodingFilter> encodingFilterFilterRegistrationBean(){
        FilterRegistrationBean<EncodingFilter> registrationBean = new FilterRegistrationBean<>(new EncodingFilter());
        registrationBean.setOrder(Ordered.HIGHEST_PRECEDENCE + 60);
        return registrationBean;
    }

    @Bean
    public RestTemplate restTemplate() throws KeyStoreException, NoSuchAlgorithmException, KeyManagementException {
        TrustStrategy acceptingTrustStrategy = (X509Certificate[] chain, String authType) -> true;
        SSLContext sslContext = org.apache.http.ssl.SSLContexts.custom().loadTrustMaterial(null, acceptingTrustStrategy).build();
        SSLConnectionSocketFactory csf = new SSLConnectionSocketFactory(sslContext);
        CloseableHttpClient httpClient = HttpClients.custom().setSSLSocketFactory(csf).build();
        HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
        requestFactory.setHttpClient(httpClient);
        RestTemplate restTemplate = new RestTemplate(requestFactory);
        return restTemplate;
    }
}
