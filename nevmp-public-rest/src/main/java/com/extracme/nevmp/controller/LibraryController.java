package com.extracme.nevmp.controller;

import com.extracme.nevmp.api.BaseResponse;
import com.extracme.nevmp.service.owner.OwnerService;
import com.extracme.nevmp.service.qualification.OwnerQualificationService;
import com.extracme.nevmp.utils.MemcachedUtil;
import com.extracme.nevmp.vo.EventListenerVO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 办件库服务
 * <AUTHOR>
 * @Description
 */
@RestController
@RequestMapping("library")
public class LibraryController {

    Logger logger = LoggerFactory.getLogger(LibraryController.class);


    @Autowired
    private OwnerQualificationService ownerQualificationService;

    @Autowired
    private OwnerService ownerService;

    /**
     * 该接口用于处理用户提交或补正办件后的事件订阅监听
     * 注意:由于系统可能存在重启或者宕机的情况，所以可能出现用户提交后未受理的情况
     * 此时需要由定时任务定期地处理遗漏的办件数据
     * @param eventListenerVO 监听消息
     * @return BaseResponse
     */
    @PostMapping("eventListener")
    public BaseResponse eventListener(@RequestBody EventListenerVO eventListenerVO){
        //针对办件编号添加锁，防止客户重复点击
        try{
            logger.warn(eventListenerVO.getApplyNo() +  "收到处理办件信息:"+eventListenerVO);

            if(MemcachedUtil.add(eventListenerVO.getApplyNo(),"value", 60)){
                final long start = System.currentTimeMillis();
                if(StringUtils.compare("3120W4257000", eventListenerVO.getItemCode()) ==0){
                    if(eventListenerVO.getEventType() == 1){
                        //提交购车资质
                        ownerQualificationService.dealQualificationApply(eventListenerVO.getApplyNo(), eventListenerVO.getUapplyNo());
                    }else if(eventListenerVO.getEventType() == 2){
                        //补正购车资质
                        ownerQualificationService.dealSupplementQualificationApply(eventListenerVO.getApplyNo());
                    }
                }else if(StringUtils.compare("3120W3866000", eventListenerVO.getItemCode()) == 0){
                    if(eventListenerVO.getEventType() == 1){
                        //提交用户申请
                        ownerService.acceptConfirmApply(eventListenerVO.getApplyNo());
                    }
                }
                final long finish = System.currentTimeMillis();
                logger.warn(eventListenerVO.getApplyNo() + "总共耗时:" + (finish-start)/1000 + "s");
            }else{
                logger.error("已有线程正在处理办件:" + eventListenerVO.getApplyNo());
            }
        }finally {
            MemcachedUtil.delete(eventListenerVO.getApplyNo());
        }
        return new BaseResponse();
    }
}
