package com.extracme.nevmp.controller;

import com.extracme.nevmp.api.BaseResponse;
import com.extracme.nevmp.constant.Const;
import com.extracme.nevmp.converter.ConvertUtil;
import com.extracme.nevmp.dto.common.PageInfoBO;
import com.extracme.nevmp.dto.file.FileResponse;
import com.extracme.nevmp.dto.owner.secondhand.*;
import com.extracme.nevmp.dto.qualification.owner.*;
import com.extracme.nevmp.model.OwnerQualification;
import com.extracme.nevmp.service.FileService;
import com.extracme.nevmp.service.owner.OwnerService;
import com.extracme.nevmp.service.qualification.OwnerQualificationService;
import com.extracme.nevmp.utils.MessageUtil;
import com.extracme.nevmp.vo.*;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;

/**
 * 二手车相关接口
 * <AUTHOR>
 * @Description
 */
@RestController
@RequestMapping("/secondHandVehicle")
public class SecondHandVehicleController {

    @Autowired
    private OwnerQualificationService ownerQualificationService;

    @Autowired
    private OwnerService ownerService;

    @Autowired
    private FileService fileService;


    @PostMapping(value = "sendSms")
    @ApiOperation(value = "发送短信", notes = "phone: 手机号, content: 短信内容")
    public BaseResponse sendSms(@RequestBody SendSmsVO sendSmsVO){
        MessageUtil.sendMessage(sendSmsVO.getPhone(), sendSmsVO.getContent());
        return new BaseResponse();
    }



    @PostMapping(value = "upload")
    @ApiOperation(value = "上传文件",
            notes = "fileType: 1:用户资质证明材料, 2:军官证明材料, 3:车辆合格证扫描件, 4:充电桩承诺书, 5:独立计量表扫描件, " +
                    "6:证件扫描件, 7:购车发票扫描件, 8:社会信用代码证扫描件, 9:行驶证扫描件, 10:充电桩照片, 11:经销商授权证明, " +
                    "12: 经销商续期授权证明, 20:车辆信息导入, 21:充电条件确认信息导入, 30:充电桩自查反馈表,  31: 充电桩铭牌 40:充电桩建设信息导入, " +
                    "50:车型申请书, 51:车企车型证明材料, 52:数据中心车型证明材料, 53:车型评审报告, 54:车型质量抽查表, 55:车型参数表")
    public FileResponse upload(@RequestParam("file") MultipartFile file, @RequestParam("type") Integer fileType) {
        return fileService.upload(file, fileType, Const.ADMIN_ID, Const.SMALL_PROGRAM);
    }

    @PostMapping(value = "/savePrivateOwnerQualification")
    @ApiOperation(value = "保存私人意向用户信息",  httpMethod = "POST")
    public BaseResponse savePrivateOwnerQualification(@RequestBody SavePrivateOwnerQualificationVO savePrivateOwnerQualificationVO){
        SavePrivateOwnerQualificationDTO savePrivateOwnerQualificationDTO = ConvertUtil.normalConvert(savePrivateOwnerQualificationVO, SavePrivateOwnerQualificationDTO.class);
        savePrivateOwnerQualificationDTO.setCreatedUserId(Const.ADMIN_ID);
        savePrivateOwnerQualificationDTO.setCreatedUserName(Const.SMALL_PROGRAM);
        return ownerQualificationService.savePrivateOwnerQualification(savePrivateOwnerQualificationDTO);
    }

    @PostMapping(value = "/saveMilitaryQualificationInfo")
    @ApiOperation(value = "保存军官意向用户信息",  httpMethod = "POST")
    public BaseResponse saveMilitaryQualificationInfo(@RequestBody SaveMilitaryQualificationInfoVO saveMilitaryQualificationInfoVO){
        SaveMilitaryQualificationInfoDTO saveMilitaryQualificationInfoDTO = ConvertUtil.convert(saveMilitaryQualificationInfoVO, SaveMilitaryQualificationInfoDTO.class);
        saveMilitaryQualificationInfoDTO.setCreatedUserId(Const.ADMIN_ID);
        saveMilitaryQualificationInfoDTO.setCreatedUserName(Const.SMALL_PROGRAM);
        return ownerQualificationService.saveMilitaryQualificationInfo(saveMilitaryQualificationInfoDTO);
    }

    @PostMapping(value = "/saveBusinessOwnerQualification")
    @ApiOperation(value = "保存法人意向用户信息",  httpMethod = "POST")
    public BaseResponse saveBusinessOwnerQualification(@RequestBody SaveBusinessOwnerQualificationVO saveBusinessOwnerQualificationVO){
        SaveBusinessOwnerQualificationDTO saveBusinessOwnerQualificationDTO = ConvertUtil.normalConvert(saveBusinessOwnerQualificationVO, SaveBusinessOwnerQualificationDTO.class);
        saveBusinessOwnerQualificationDTO.setCreatedUserId(Const.ADMIN_ID);
        saveBusinessOwnerQualificationDTO.setCreatedUserName(Const.SMALL_PROGRAM);
        return ownerQualificationService.saveBusinessOwnerQualification(saveBusinessOwnerQualificationDTO);
    }

    @PostMapping(value = "/invalidOwnerQualification")
    @ApiOperation(value = "失效购车资质（仅限二手车平台录入）",  httpMethod = "POST")
    public BaseResponse invalidOwnerQualification(@RequestBody InvalidOwnerQualificationVO invalidOwnerQualificationVO){
        InvalidOwnerQualificationDTO invalidOwnerQualificationDTO = new InvalidOwnerQualificationDTO();
        invalidOwnerQualificationDTO.setId(invalidOwnerQualificationVO.getId());
        invalidOwnerQualificationDTO.setReason(invalidOwnerQualificationDTO.getReason());
        invalidOwnerQualificationDTO.setOperatorId(Const.ADMIN_ID);
        invalidOwnerQualificationDTO.setOperatorName(Const.SMALL_PROGRAM);
        return ownerQualificationService.invalidOwnerQualification(invalidOwnerQualificationDTO);
    }

    @PostMapping(value = "/cancelReconsiderOwnerQualification")
    @ApiOperation(value = "取消购车资质复核",  httpMethod = "POST")
    public BaseResponse cancelReconsiderOwnerQualification(@RequestBody @Validated CancelReconsiderOwnerQualificationVO cancelReconsiderOwnerQualificationVO){
        String reason = "二手车用户放弃复核";
        ownerQualificationService.cancelReconsiderOwnerQualification(cancelReconsiderOwnerQualificationVO.getId(), reason ,Const.ADMIN_ID, Const.SMALL_PROGRAM);
        return new BaseResponse();
    }

    @PostMapping(value = "/queryCurrentOwnerQualification")
    @ApiOperation(value = "获取当前购车资质信息",  httpMethod = "POST")
    public CurrentOwnerQualificationDTO queryCurrentOwnerQualification(@RequestBody QueryCurrentOwnerQualificationVO queryCurrentOwnerQualificationVO){
        return  ownerQualificationService.queryCurrentOwnerQualification(queryCurrentOwnerQualificationVO.getAuthType(), queryCurrentOwnerQualificationVO.getAuthId());
    }

    @PostMapping("/queryOwnerQualificationDetail")
    @ApiOperation(value = "查询意向用户详细信息",  httpMethod = "POST")
    public OwnerQualificationDetailDTO queryOwnerQualificationDetail(@RequestBody @Valid QueryOwnerQualificationDetailVO queryOwnerQualificationDetailVO) {
        return ownerQualificationService.queryOwnerQualificationDetail(queryOwnerQualificationDetailVO.getAuthType(), queryOwnerQualificationDetailVO.getAuthId());
    }

    @PostMapping("/queryOwnerQualificationDetailHistory")
    @ApiOperation(value = "查询意向用户详细信息（历史）",  httpMethod = "POST")
    public OwnerQualificationDetailHistoryDTO queryOwnerQualificationDetailHistory(@RequestBody @Valid QueryOwnerQualificationDetailHistoryVO queryOwnerQualificationDetailHistoryVO){
        return ownerQualificationService.queryOwnerQualificationDetailHistory(queryOwnerQualificationDetailHistoryVO.getAuthType(), queryOwnerQualificationDetailHistoryVO.getAuthId());

    }

    @PostMapping("/reconsiderOwnerQualification")
    @ApiOperation(value = "复核意向用户信息",  httpMethod = "POST")
    public BaseResponse reconsiderOwnerQualification(@RequestBody @Validated ReconsiderOwnerQualificationVO reconsiderOwnerQualificationVO) {
        ReconsiderOwnerQualificationDTO reconsiderOwnerQualificationDTO = ConvertUtil.normalConvert(reconsiderOwnerQualificationVO, ReconsiderOwnerQualificationDTO.class);
        reconsiderOwnerQualificationDTO.setUpdatedUserId(Const.ADMIN_ID);
        reconsiderOwnerQualificationDTO.setUpdatedUserName(Const.SMALL_PROGRAM);
        reconsiderOwnerQualificationDTO.setOrgId(Const.ADMIN_ORG_ID);
        return ownerQualificationService.reconsiderOwnerQualification(reconsiderOwnerQualificationDTO);
    }


    @PostMapping("/hasApprovedQualification")
    @ApiOperation(value = "复核意向用户信息",  httpMethod = "POST")
    public HasApprovedQualificationResponse hasApprovedQualification(@RequestBody @Validated HasApprovedQualificationVO hasApprovedQualificationVO){
        final OwnerQualification ownerQualification = ownerQualificationService.queryLastApprovedQualification(hasApprovedQualificationVO.getAuthType(), hasApprovedQualificationVO.getAuthId());
        return new HasApprovedQualificationResponse(ownerQualification != null);
    }


    //----------------  二手车充电条件确认 ----------------------------

    @PostMapping("saveSecondHandVehicleOwner")
    @ApiOperation(value = "保存二手车购车人信息",  httpMethod = "POST")
    public BaseResponse saveSecondHandVehicleOwner(@RequestBody SaveSecondHandVehicleOwnerVO saveSecondHandVehicleOwnerVO){
        SaveSecondHandVehicleOwnerDTO saveSecondHandVehicleOwnerDTO = ConvertUtil.convert(saveSecondHandVehicleOwnerVO, SaveSecondHandVehicleOwnerDTO.class);
        saveSecondHandVehicleOwnerDTO.setCreatedUserId(Const.ADMIN_ID);
        saveSecondHandVehicleOwnerDTO.setCreatedUserName(Const.SMALL_PROGRAM);
        return ownerService.saveSecondHandVehicleOwner(saveSecondHandVehicleOwnerDTO);
    }

    @PostMapping("updateSecondHandVehicleOwner")
    @ApiOperation(value = "修改二手车充电桩确认信息",  httpMethod = "POST")
    public BaseResponse updateSecondHandVehicleOwner(@RequestBody UpdateSecondHandVehicleOwnerVO updateSecondHandVehicleOwnerVO){
        UpdateSecondHandVehicleOwnerDTO updateSecondHandVehicleOwnerDTO = ConvertUtil.convert(updateSecondHandVehicleOwnerVO, UpdateSecondHandVehicleOwnerDTO.class);
        updateSecondHandVehicleOwnerDTO.setUpdatedUserId(Const.ADMIN_ID);
        updateSecondHandVehicleOwnerDTO.setUpdatedUserName(Const.SMALL_PROGRAM);
        return ownerService.updateSecondHandVehicleOwner(updateSecondHandVehicleOwnerDTO);
    }

    @PostMapping("querySecondHandVehicleOwnerList")
    @ApiOperation(value = "查询二手车充电桩确认列表",  httpMethod = "POST")
    public PageInfoBO<SecondHandVehicleOwnerListDTO> querySecondHandVehicleOwnerList(@RequestBody @Validated QuerySecondHandVehicleOwnerListVO querySecondHandVehicleOwnerListVO){
        return ownerService.querySecondHandVehicleOwnerList(querySecondHandVehicleOwnerListVO.getOwnerType(), querySecondHandVehicleOwnerListVO.getAuthType(), querySecondHandVehicleOwnerListVO.getAuthId());
    }

    @PostMapping("querySecondHandVehicleOwnerInfo")
    @ApiOperation(value = "查询二手车充电桩确认信息",  httpMethod = "POST")
    public SecondHandVehicleOwnerInfoDTO querySecondHandVehicleOwnerInfo(@RequestBody @Validated QuerySecondHandVehicleOwnerInfoVO querySecondHandVehicleOwnerInfoVO) {
        return ownerService.querySecondHandVehicleOwnerInfo(querySecondHandVehicleOwnerInfoVO.getOwnerSeq(), querySecondHandVehicleOwnerInfoVO.getAuthType(), querySecondHandVehicleOwnerInfoVO.getAuthId());
    }

    @PostMapping("cancelSecondHandVehicleOwnerApply")
    @ApiOperation(value = "取消二手车充电条件确认信息申请",  httpMethod = "POST")
    public BaseResponse cancelSecondHandVehicleOwnerApply(@RequestBody @Validated CancelSecondHandVehicleOwnerApplyVO cancelSecondHandVehicleOwnerApplyVO){
        return ownerService.cancelSecondHandVehicleOwnerApply(cancelSecondHandVehicleOwnerApplyVO.getOwnerSeq(), cancelSecondHandVehicleOwnerApplyVO.getAuthType(), cancelSecondHandVehicleOwnerApplyVO.getAuthId());
    }


    @PostMapping("deleteSecondHandVehicleOwnerInfo")
    @ApiOperation(value = "删除二手车充电桩确认信息",  httpMethod = "POST")
    public BaseResponse deleteSecondHandVehicleOwnerInfo(@RequestBody @Validated DeleteSecondHandVehicleOwnerInfoVO deleteSecondHandVehicleOwnerInfoVO){
        return ownerService.deleteSecondHandVehicleOwnerInfo(deleteSecondHandVehicleOwnerInfoVO.getOwnerSeq(), deleteSecondHandVehicleOwnerInfoVO.getAuthType(),deleteSecondHandVehicleOwnerInfoVO.getAuthId());
    }

    //TODO 申请账号过户


    //TODO 申请账号解绑


    @PostMapping("applySecondHandVehicleOwnerUnbind")
    @ApiOperation(value = "申请二手车充电条件解绑",  httpMethod = "POST")
    public BaseResponse applySecondHandVehicleOwnerUnbind(@RequestBody @Validated ApplySecondHandVehicleOwnerUnbindVO applySecondHandVehicleOwnerUnbindVO){
        ApplySecondHandVehicleOwnerUnbindDTO applySecondHandVehicleOwnerUnbindDTO = ConvertUtil.normalConvert(applySecondHandVehicleOwnerUnbindVO, ApplySecondHandVehicleOwnerUnbindDTO.class);
        applySecondHandVehicleOwnerUnbindDTO.setUpdatedUserId(Const.ADMIN_ID);
        applySecondHandVehicleOwnerUnbindDTO.setUpdatedUserName(Const.SMALL_PROGRAM);
        return ownerService.applySecondHandVehicleOwnerUnbind(applySecondHandVehicleOwnerUnbindDTO);
    }

    @PostMapping("querySecondHandVehicleOwnerUnbindDetail")
    @ApiOperation(value = "查询二手车充电条件解绑信息",  httpMethod = "POST")
    public OwnerUnbindDetailResponse querySecondHandVehicleOwnerUnbindDetail(@RequestBody @Validated QuerySecondHandVehicleOwnerUnbindDetailVO querySecondHandVehicleOwnerUnbindDetailVO){
        return ownerService.queryOwnerUnbindDetail(querySecondHandVehicleOwnerUnbindDetailVO.getOwnerSeq()
                ,querySecondHandVehicleOwnerUnbindDetailVO.getAuthType(),querySecondHandVehicleOwnerUnbindDetailVO.getAuthId());
    }

    @PostMapping("queryOwnerHoldVehicles")
    @ApiOperation(value = "查询用户持有的车辆列表",  httpMethod = "POST")
    public PageInfoBO<OwnerHoldVehicleDTO> queryOwnerHoldVehicles(@RequestBody @Validated QueryOwnerHoldVehiclesVO queryOwnerHoldVehiclesVO){
        return ownerService.queryOwnerHoldVehicles(queryOwnerHoldVehiclesVO.getAuthType(), queryOwnerHoldVehiclesVO.getAuthId());
    }

    @PostMapping("applyOwnerHoldVehicleUnbind")
    @ApiOperation(value = "申请新车充电条件解绑",  httpMethod = "POST")
    public BaseResponse applyOwnerHoldVehicleUnbind(@RequestBody @Validated ApplyOwnerHoldVehicleUnbindVO applyOwnerHoldVehicleUnbindVO){
        ApplyOwnerHoldVehicleUnbindDTO applyOwnerHoldVehicleUnbindDTO = ConvertUtil.normalConvert(applyOwnerHoldVehicleUnbindVO, ApplyOwnerHoldVehicleUnbindDTO.class);
        applyOwnerHoldVehicleUnbindDTO.setUpdatedUserId(Const.ADMIN_ID);
        applyOwnerHoldVehicleUnbindDTO.setUpdatedUserName(Const.SMALL_PROGRAM);
        return ownerService.applyOwnerHoldVehicleUnbind(applyOwnerHoldVehicleUnbindDTO);
    }

    @PostMapping("queryOwnerHoldVehicleUnbindDetail")
    @ApiOperation(value = "查询用户持有车辆（新车）解绑详情",  httpMethod = "POST")
    public OwnerUnbindDetailResponse queryOwnerHoldVehicleUnbindDetail(@RequestBody @Validated QueryOwnerHoldVehicleUnbindDetailVO queryOwnerHoldVehicleUnbindDetailVO){
        return ownerService.queryOwnerHoldVehicleUnbindDetail(queryOwnerHoldVehicleUnbindDetailVO.getVin()
                ,queryOwnerHoldVehicleUnbindDetailVO.getAuthType(), queryOwnerHoldVehicleUnbindDetailVO.getAuthId());
    }


    @PostMapping("ownerAutoUnbind")
    @ApiOperation(value = "二手车主动触发自动解绑充电确认条件",  httpMethod = "POST")
    public BaseResponse ownerAutoUnbind(@RequestBody @Validated OwnerAutoUnbindVO ownerAutoUnbindVO){
        return ownerService.autoUnbindOwnerInfo(ownerAutoUnbindVO.getAuthType(), ownerAutoUnbindVO.getAuthId());
    }




    //----------------  二手车交易 ----------------------------

    @PostMapping("bindSecondHandVehicleVin")
    @ApiOperation(value = "绑定二手车车架号",  httpMethod = "POST")
    public BaseResponse bindSecondHandVehicleVin(@RequestBody @Validated BindSecondHandVehicleVinVO bindSecondHandVehicleVinVO){
        BindSecondHandVehicleVinDTO bindSecondHandVehicleVinDTO = ConvertUtil.normalConvert(bindSecondHandVehicleVinVO, BindSecondHandVehicleVinDTO.class);
        bindSecondHandVehicleVinDTO.setOperatorId(Const.ADMIN_ID);
        bindSecondHandVehicleVinDTO.setOperatorName(Const.SECOND_HAND_AUTOMOBILE_MARKET);
        return ownerService.bindSecondHandVehicleVin(bindSecondHandVehicleVinDTO);
    }

    @PostMapping("querySecondHandVehicleBindList")
    @ApiOperation(value = "查询绑定成功的二手车列表",  httpMethod = "POST")
    public PageInfoBO<SecondHandVehicleOwnerBindDTO> querySecondHandVehicleBindList(@RequestBody @Validated QuerySecondHandVehicleBindListVO querySecondHandVehicleBindListVO){
        return ownerService.querySecondHandVehicleBindList(querySecondHandVehicleBindListVO.getAuthType(), querySecondHandVehicleBindListVO.getAuthId());
    }

    @PostMapping("cancelSecondHandVehicleBind")
    @ApiOperation(value = "撤销二手车绑定",  httpMethod = "POST")
    public BaseResponse cancelSecondHandVehicleBind(@RequestBody @Validated CancelSecondHandVehicleBindVO cancelSecondHandVehicleBindVO){
        return ownerService.cancelSecondHandVehicleBind(cancelSecondHandVehicleBindVO.getOwnerSeq()
                , cancelSecondHandVehicleBindVO.getAuthType(), cancelSecondHandVehicleBindVO.getAuthId());
    }

}
