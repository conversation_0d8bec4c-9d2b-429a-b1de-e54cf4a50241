package com.extracme.nevmp.controller;

import com.extracme.nevmp.dto.gov.traffic.NewEnergyCarAudit;
import com.extracme.nevmp.service.traffic.impl.TrafficPushServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/traffic")
public class TrafficController {

    @Autowired
    private TrafficPushServiceImpl trafficPushService;

    @PostMapping("/saveCar")
    public String saveCar(@RequestBody NewEnergyCarAudit newEnergyCarAudit) {
        Boolean aBoolean = false;
        try {
            aBoolean = trafficPushService.syncNewEnergyCarAuditRstOnlyPush(newEnergyCarAudit);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return aBoolean.toString();
    }


}
