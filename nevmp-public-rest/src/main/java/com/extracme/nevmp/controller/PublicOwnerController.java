package com.extracme.nevmp.controller;

import com.extracme.nevmp.dto.owner.detail.QueryAuctionContractDTO;
import com.extracme.nevmp.dto.owner.detail.VehicleRelationInfoDTO;
import com.extracme.nevmp.service.owner.OwnerService;
import com.extracme.nevmp.service.traffic.TrafficPushService;
import com.extracme.nevmp.vo.QueryAuctionContractVO;
import com.extracme.nevmp.vo.QueryVehicleRelationInfoVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 公共用户接口
 * <AUTHOR>
 * @Description
 */
@RestController
@RequestMapping("publicOwner")
public class PublicOwnerController {

    @Autowired
    private OwnerService ownerService;

    @Autowired
    private TrafficPushService trafficPushService;

    @PostMapping(value = "queryVehicleRelationInfo")
    @ApiOperation(value = "查询车辆相关信息",  httpMethod = "POST")
    public VehicleRelationInfoDTO queryVehicleRelationInfo(@RequestBody @Valid QueryVehicleRelationInfoVO queryVehicleRelationInfoVO){
        return ownerService.queryVehicleRelationInfo(queryVehicleRelationInfoVO.getVin());
    }

    @PostMapping(value = "queryAuctionContract")
    public QueryAuctionContractDTO queryAuctionContract(@RequestBody @Valid QueryAuctionContractVO queryAuctionContractVO){
        return new QueryAuctionContractDTO(trafficPushService.qryAuctionContract(queryAuctionContractVO.getAuthId()));
    }

}
