package com.extracme.nevmp.controller;

import com.extracme.nevmp.dto.qualification.owner.OwnerSupplementInfoDTO;
import com.extracme.nevmp.service.qualification.OwnerQualificationService;
import com.extracme.nevmp.vo.QueryOwnerSupplementInfoVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @Description
 */
@RestController
@RequestMapping("ownerQualification")
public class OwnerQualificationController {

    @Autowired
    private OwnerQualificationService ownerQualificationService;

    @PostMapping("/queryOwnerSupplementInfo")
    @ApiOperation(value = "查询用户补正信息",  httpMethod = "POST")
    public OwnerSupplementInfoDTO queryOwnerSupplementInfo(@RequestBody @Valid QueryOwnerSupplementInfoVO queryOwnerSupplementInfoVO){
       return ownerQualificationService.queryOwnerSupplementInfo(queryOwnerSupplementInfoVO.getApplyNo());
    }
}
