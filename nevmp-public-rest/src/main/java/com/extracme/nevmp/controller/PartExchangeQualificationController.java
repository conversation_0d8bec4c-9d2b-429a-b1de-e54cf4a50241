package com.extracme.nevmp.controller;

import com.extracme.nevmp.api.BaseResponse;
import com.extracme.nevmp.constant.Const;
import com.extracme.nevmp.converter.ConvertUtil;
import com.extracme.nevmp.dto.exchange.*;
import com.extracme.nevmp.service.qualification.OwnerQualificationService;
import com.extracme.nevmp.vo.exchange.*;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping("/partExchangeQualification")
public class PartExchangeQualificationController {

    @Autowired
    private OwnerQualificationService ownerQualificationService;


    @PostMapping("submitPrivateExchangeQualification")
    @ApiOperation(value = "提交私人以旧换新资格申请")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "成功", response = SubmitExchangeQualificationResponse.class)
    })
    public SubmitExchangeQualificationResponse submitPrivateExchangeQualification(
            @RequestBody @Validated @ApiParam(value = "私人以旧换新资格申请信息", required = true)
            SubmitPrivateExchangeQualificationVO submitPrivateExchangeQualificationVO){
        //转换相同字段
        SubmitExchangeQualificationDTO submitExchangeQualificationDTO = ConvertUtil.normalConvert(submitPrivateExchangeQualificationVO, SubmitExchangeQualificationDTO.class);
        submitExchangeQualificationDTO.setIsMilitaryOfficer(0);
        submitExchangeQualificationDTO.setOperatorId(Const.ADMIN_ID);
        submitExchangeQualificationDTO.setOperatorName(Const.SMALL_PROGRAM);
        return ownerQualificationService.submitExchangeQualification(submitExchangeQualificationDTO);
    }

    @PostMapping("submitMilitaryExchangeQualification")
    @ApiOperation(value = "提交军官以旧换新资格申请")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "成功", response = SubmitExchangeQualificationResponse.class)
    })
    public SubmitExchangeQualificationResponse submitMilitaryExchangeQualification(
            @RequestBody @Validated @ApiParam(value = "军人以旧换新资格申请信息", required = true)
            SubmitMilitaryExchangeQualificationVO submitMilitaryExchangeQualificationVO){
        //转换相同字段
        SubmitExchangeQualificationDTO submitExchangeQualificationDTO = ConvertUtil.normalConvert(submitMilitaryExchangeQualificationVO, SubmitExchangeQualificationDTO.class);
        submitExchangeQualificationDTO.setIsMilitaryOfficer(1);
        submitExchangeQualificationDTO.setOperatorId(Const.ADMIN_ID);
        submitExchangeQualificationDTO.setOperatorName(Const.SMALL_PROGRAM);
        return ownerQualificationService.submitExchangeQualification(submitExchangeQualificationDTO);
    }


    @PostMapping("searchExchangeQualificationList")
    @ApiOperation(value = "查询用户以旧换新申请列表")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "成功", response = SearchExchangeQualificationListResponse.class)
    })
    public SearchExchangeQualificationListResponse searchExchangeQualificationList(
            @RequestBody @Validated @ApiParam(value = "私人以旧换新资格申请信息", required = true)
            SearchExchangeQualificationListVO searchExchangeQualificationListVO){
        SearchExchangeQualificationListDTO searchExchangeQualificationListDTO = ConvertUtil.normalConvert(searchExchangeQualificationListVO, SearchExchangeQualificationListDTO.class);
        return ownerQualificationService.searchExchangeQualificationList(searchExchangeQualificationListDTO);
    }

    @PostMapping("getExchangeQualificationDetail")
    @ApiOperation(value = "查询以旧换新资质详情")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "成功", response = GetExchangeQualificationDetailResponse.class)
    })
    public GetExchangeQualificationDetailResponse getExchangeQualificationDetail(
            @RequestBody @Validated @ApiParam(value = "以旧换新明细查询条件", required = true)
            GetExchangeQualificationDetailVO getExchangeQualificationDetailVO ){
        return ownerQualificationService.getExchangeQualificationDetail(getExchangeQualificationDetailVO.getOwnerQualificationId(),
                getExchangeQualificationDetailVO.getAuthType(), getExchangeQualificationDetailVO.getAuthId());
    }


    @PostMapping("reconsiderExchangeQualification")
    @ApiOperation(value = "提交以旧换新复核材料")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "成功", response = BaseResponse.class)
    })
    public BaseResponse reconsiderExchangeQualification(
            @RequestBody @Validated @ApiParam(value = "以旧换新复核信息", required = true)
            ReconsiderExchangeQualificationVO reconsiderExchangeQualificationVO ){
        ReconsiderExchangeQualificationDTO  reconsiderExchangeQualificationDTO = ConvertUtil.normalConvert(reconsiderExchangeQualificationVO, ReconsiderExchangeQualificationDTO.class);
        ownerQualificationService.reconsiderExchangeQualification(reconsiderExchangeQualificationDTO);
        return new BaseResponse();
    }

    @PostMapping("cancelReconsiderExchangeQualification")
    @ApiOperation(value = "取消以旧换新资格复核")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "成功", response = BaseResponse.class)
    })
    public BaseResponse cancelReconsiderExchangeQualification(
            @RequestBody @Validated @ApiParam(value = "取消以旧换新复核信息", required = true)
            CancelReconsiderExchangeQualificationVO cancelReconsiderExchangeQualificationVO ){
        CancelReconsiderExchangeQualificationDTO cancelReconsiderExchangeQualificationDTO = ConvertUtil.normalConvert(cancelReconsiderExchangeQualificationVO, CancelReconsiderExchangeQualificationDTO.class);
        cancelReconsiderExchangeQualificationDTO.setOperatorId(Const.ADMIN_ID);
        cancelReconsiderExchangeQualificationDTO.setOperatorName(Const.SMALL_PROGRAM);
        ownerQualificationService.cancelReconsiderExchangeQualification(cancelReconsiderExchangeQualificationDTO);
        return new BaseResponse();
    }

}

