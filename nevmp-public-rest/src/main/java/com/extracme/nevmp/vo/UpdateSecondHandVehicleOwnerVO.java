package com.extracme.nevmp.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/12/15
 */
@Data
public class UpdateSecondHandVehicleOwnerVO {

    /**
     * 二手车唯一编号
     */
    @NotNull
    @ApiModelProperty(value = "二手车唯一编号", required = true)
    private String ownerSeq;


    /**
     * 申请人性质
     * 1:私人
     * 2:法人
     */
    @ApiModelProperty("申请人性质  1:私人 2:法人")
    private Integer ownerType;

    /**
     * 证件类型
     */
    @NotNull
    @ApiModelProperty(value = "证件类型 1:身份证 5:社会信用统一代码证 6:港澳台居民来往内地通行证 7:台湾居民来往大陆通行证" +
            " 8:中国护照（华侨） 9:外国人护照 10:外国人永久居留身份证 11:港澳台居民居住证", allowableValues = "1,5,6,7,8,9,10,11",required = true)
    private Integer authKind;

    /**
     * 证件号
     */
    @ApiModelProperty(value = "证件号")
    private String authId;

    /**
     * 姓名
     */
    @NotBlank
    @ApiModelProperty(value = "姓名",required = true)
    private String name;

    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    private String mobilePhone;

    /**
     * 充电桩建设运营单位名称
     */
    @ApiModelProperty(value = "充电桩建设运营单位名称")
    private String chargeOperatorName;

    /**
     * 充电桩编号
     */
    @ApiModelProperty(value = "充电桩编号")
    private String chargeNo;

    /**
     * 充电设备类型
     * 220V  : AC220
     * 380V  : DC380
     * 其他  : OTHER
     */
    @ApiModelProperty(value = "充电设备类型：  220V:AC220   380V:DC380   其他:OTHER")
    private String chargeDeviceType;

    /**
     * 充电桩性质
     */
    @ApiModelProperty(value = "充电桩性质： 1：自建  2：专用  3: 公用")
    private Integer chargeDeviceProperty;

    /**
     * 充电桩安装区域
     */
    @ApiModelProperty(value = "充电桩安装区域")
    private String chargeDeviceDistrict;

    /**
     * 充电设备安装街道
     */
    @ApiModelProperty(value = "充电设备安装街道")
    private String chargeDeviceStreet;

    /**
     * 充电设备安装停车位
     */
    @ApiModelProperty(value = "充电设备安装停车位")
    private String chargeDeviceParkNo;

    /**
     * 充电设备投入运营时间
     */
    @ApiModelProperty(value = "充电设备投入运营时间")
    private Date chargeDeviceStartTime;

    /**
     * 充电桩照片
     */
    @ApiModelProperty(value = "充电桩照片")
    private String chargePhotoCopy;

    /**
     * 充电桩证明材料
     */
    @ApiModelProperty(value = "充电桩证明材料")
    private String chargeConfirmCopy;

    /**
     * 充电桩铭牌照片
     */
    @ApiModelProperty(value = "充电桩铭牌照片")
    private String chargeNameplatePhoto;

    /**
     * 单位用户批量购买新能源汽车申请报告
     */
    @ApiModelProperty(value = "单位用户批量购买新能源汽车申请报告")
    private List<String> businessBatchPurchaseReportCopy;

    /**
     * 用户购买车辆燃料类型
     */
    @ApiModelProperty(value = "用户购买车辆燃料类型")
    private Integer ownerVehicleModelType;


}
