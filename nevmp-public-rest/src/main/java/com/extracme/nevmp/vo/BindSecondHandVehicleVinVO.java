package com.extracme.nevmp.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
public class BindSecondHandVehicleVinVO {

    /**
     * 证件类型
     */
    @NotNull
    @ApiModelProperty(value = "证件类型 1:身份证 5:社会信用统一代码证 6:港澳台居民来往内地通行证 7:台湾居民来往大陆通行证" +
            " 8:中国护照（华侨） 9:外国人护照 10:外国人永久居留身份证 11:港澳台居民居住证", allowableValues = "1,5,6,7,8,9,10,11",required = true)
    private Integer authType;

    /**
     * 证件号
     */
    @NotEmpty
    @ApiModelProperty(value = "证件号", required = true)
    private String authId;

    /**
     * 二手车唯一编号
     */
    @NotEmpty
    @ApiModelProperty(value = "二手车唯一编号", required = true)
    private String ownerSeq;

    /**
     * 车架号
     */
    @NotEmpty
    @ApiModelProperty(value = "车架号", required = true)
    private String vin;

    /**
     * 燃料类型
     */
    @NotNull
    @ApiModelProperty(value = "燃料类型", required = true)
    private Integer vehicleModelType;
}
