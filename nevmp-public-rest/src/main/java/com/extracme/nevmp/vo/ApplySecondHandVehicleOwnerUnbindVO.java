package com.extracme.nevmp.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ApplySecondHandVehicleOwnerUnbindVO {

    /**
     * 二手车编号
     */
    @NotBlank
    private String ownerSeq;

    /**
     * 证件号
     */
    @NotBlank
    private String authId;

    /**
     * 证件类型
     */
    @NotNull
    private Integer authType;

    /**
     * 名下无车证明
     */
    private List<String> noVehicleCertificateFile;

    /**
     * 机动车交易发票
     */
    private List<String> vehicleTransactionInvoiceFile;

    /**
     * 机动车注册登记证
     */
    private List<String> vehicleRegistrationCertificateFile;
}
