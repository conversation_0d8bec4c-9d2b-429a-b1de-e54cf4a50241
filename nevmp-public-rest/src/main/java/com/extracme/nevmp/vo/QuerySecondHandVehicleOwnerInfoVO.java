package com.extracme.nevmp.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 查询二手车充电条件明细
 * <AUTHOR>
 */
@Data
public class QuerySecondHandVehicleOwnerInfoVO {

    @NotBlank
    @ApiModelProperty(value = "二手车唯一编号", required = true)
    private String ownerSeq;

    @NotNull
    @ApiModelProperty(value = "证件类型", required =  true)
    private Integer authType;

    @NotBlank
    @ApiModelProperty(value = "证件号", required = true)
    private String authId;
}
