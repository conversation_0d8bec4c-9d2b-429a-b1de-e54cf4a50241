package com.extracme.nevmp.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 */
@Data
public class SaveMilitaryQualificationInfoVO {

    /**
     * 用户姓名
     */
    @NotBlank
    private String name;

    /**
     * 证件类型
     */
    @NotNull
    private Integer authType;

    /**
     * 证件号
     */
    @NotBlank
    private String authId;

    /**
     * 户籍类型
     */
    @NotNull
    private Integer householdRegistrationType;

    /**
     * 出生日期
     */
    @NotNull
    private Date birthDay;

    /**
     * 驾驶证发证地
     */
    @NotBlank
    private String driverLicenseIssuingPlace;

    /**
     * 驾驶证发证机关
     */
    @NotBlank
    private String driverLicenseIssuingOrganization;

    /**
     * 驾驶证号
     */
    @NotBlank
    private String driverLicenseCode;

    /**
     * 手机号（用于接收结果）
     */
    private String mobilePhone;

    /**
     * 军官证照片
     */
    @NotEmpty
    private List<String> file;

    private Integer vehicleModelType;
}
