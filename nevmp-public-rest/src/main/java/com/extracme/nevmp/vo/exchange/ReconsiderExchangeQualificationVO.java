package com.extracme.nevmp.vo.exchange;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class ReconsiderExchangeQualificationVO {

    @NotNull(message = "请选择需复核的数据")
    @ApiModelProperty(value = "以旧换新资质ID", required = true)
    private Long ownerQualificationId;

    @NotEmpty(message = "请上传复核材料")
    @ApiModelProperty(value = "异议扫描件列表", required = true)
    private List<String> files;
}
