package com.extracme.nevmp.vo.exchange;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class GetExchangeQualificationDetailVO {

    @NotNull(message = "请选择想要查看的数据")
    @ApiModelProperty(value = "以旧换新资质ID", required = true)
    private Long ownerQualificationId;

    @ApiModelProperty(value = "证件类型 1:身份证 5:社会信用统一代码证 6:港澳台居民来往内地通行证 7:台湾居民来往大陆通行证" +
            " 8:中国护照（华侨） 9:外国人护照 10:17版外国人永久居留身份证 11:港澳台居民居住证 12:23版外国人永久居留身份证",
            allowableValues = "1,6,7,8,9,10,11,12", example = "1", required = true)
    private Integer authType;

    @ApiModelProperty(value = "证件号", example = "310115199306110418", required = true)
    private String authId;
}
