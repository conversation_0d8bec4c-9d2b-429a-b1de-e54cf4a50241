package com.extracme.nevmp.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 查询二手车充电桩确认列表 请求参数
 * <AUTHOR>
 */
@Data
public class QuerySecondHandVehicleOwnerListVO {

    @NotNull
    @ApiModelProperty(value = "用户性质 1:私人 2:法人", required = true)
    private Integer ownerType;

    /**
     * 证件类型
     */
    @ApiModelProperty(value = "证件类型", required = true)
    private Integer authType;

    /**
     * 证件号
     */
    @ApiModelProperty(value = "证件号", required = true)
    private String authId;
}
