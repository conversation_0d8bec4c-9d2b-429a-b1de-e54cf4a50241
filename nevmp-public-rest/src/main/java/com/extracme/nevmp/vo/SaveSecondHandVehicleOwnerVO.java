package com.extracme.nevmp.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/12/15
 */
@Data
public class SaveSecondHandVehicleOwnerVO {

    /**
     * 小程序ID
     */
    @ApiModelProperty("小程序ID")
    private Integer stackId;

    /**
     * 申请人性质
     * 1:私人
     * 2:法人
     */
    @ApiModelProperty("申请人性质  1:私人 2:法人")
    private Integer ownerType;

    /**
     * 姓名
     */
    @ApiModelProperty("姓名")
    private String name;

    /**
     * 证件类型
     */
    @ApiModelProperty("证件类型")
    private String authKind;

    /**
     * 证件号
     */
    @ApiModelProperty("证件号")
    private String authId;

    /**
     * 手机号
     */
    @ApiModelProperty("手机号")
    private String mobilePhone;

    /**
     * 充电桩建设运营单位名称
     */
    @ApiModelProperty("充电桩建设运营单位名称")
    private String chargeOperatorName;

    /**
     * 充电桩编号
     */
    @ApiModelProperty("充电桩编号")
    private String chargeNo;

    /**
     * 充电设备类型
     * 220V  : AC220
     * 380V  : DC380
     * 其他  : OTHER
     */
    @ApiModelProperty("充电设备类型：  220V:AC220   380V:DC380   其他:OTHER")
    private String chargeDeviceType;
    /**
     * 充电桩性质
     */
    @ApiModelProperty("充电桩性质： 1：私人  2：法人  3: 公用")
    private Integer chargeDeviceProperty;

    /**
     * 充电桩安装区域
     */
    @ApiModelProperty("充电桩安装区域")
    private String chargeDeviceDistrict;

    /**
     * 充电设备安装街道
     */
    @ApiModelProperty("充电设备安装街道")
    private String chargeDeviceStreet;

    /**
     * 充电设备安装停车位
     */
    @ApiModelProperty("充电设备安装停车位")
    private String chargeDeviceParkNo;

    /**
     * 充电设备投入运营时间
     */
    @ApiModelProperty("充电设备投入运营时间")
    private Date chargeDeviceStartTime;

    /**
     * 充电桩照片
     */
    @ApiModelProperty("充电桩照片")
    private String chargePhotoCopy;

    /**
     * 充电桩证明材料
     */
    @ApiModelProperty("充电桩证明材料")
    private String chargeConfirmCopy;

    /**
     * 充电桩铭牌照片
     */
    @ApiModelProperty("充电桩铭牌照片")
    private String chargeNameplatePhoto;

    /**
     * 单位用户批量购买新能源汽车申请报告
     */
    @ApiModelProperty(value = "单位用户批量购买新能源汽车申请报告")
    private List<String> businessBatchPurchaseReportCopy;

    /**
     * 用户购买车辆燃料类型
     */
    @ApiModelProperty(value = "用户购买车辆燃料类型")
    private Integer ownerVehicleModelType;
}
