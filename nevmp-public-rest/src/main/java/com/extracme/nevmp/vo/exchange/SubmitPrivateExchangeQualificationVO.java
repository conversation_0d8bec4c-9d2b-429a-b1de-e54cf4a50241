package com.extracme.nevmp.vo.exchange;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@ApiModel(value = "提交私人以旧换新资格申请")
public class SubmitPrivateExchangeQualificationVO {

    @ApiModelProperty(value = "主键",example = "1")
    private Long id;

    @NotBlank(message = "请填写姓名")
    @ApiModelProperty(value = "姓名",example = "蒋长瑜", required = true)
    private String name;

    @NotNull(message = "请选择证件类型")
    @ApiModelProperty(value = "证件类型 1:身份证 5:社会信用统一代码证 6:港澳台居民来往内地通行证 7:台湾居民来往大陆通行证" +
            " 8:中国护照（华侨） 9:外国人护照 10:17版外国人永久居留身份证 11:港澳台居民居住证 12:23版外国人永久居留身份证",
            allowableValues = "1,6,7,8,9,10,11,12", example = "1", required = true)
    private Integer authType;

    @NotBlank(message = "请填写证件号")
    @ApiModelProperty(value = "证件号", example = "310115199306110418", required = true)
    private String authId;

    @NotBlank(message = "请填写驾驶证发证省市")
    @ApiModelProperty(value = "驾驶证发证省市", example = "上海市", required = true)
    private String driverLicenseIssuingPlace;

    @NotBlank(message = "请填写驾驶证发证机关")
    @ApiModelProperty(value = "驾驶证发证机关", example = "上海市公安交警总队", required = true)
    private String driverLicenseIssuingOrganization;

    @ApiModelProperty(value = "驾驶证号", example = "310115199306110418")
    private String driverLicenseCode;

    @ApiModelProperty(value = "档案编号")
    private String driverFileNo;

    @NotNull(message = "请选择户籍类型")
    @ApiModelProperty(value = "户籍类型 0:上海市 1:外省市 2:其他",allowableValues = "0,1,2", example = "1", required = true)
    private Integer householdRegistrationType;

    @NotNull(message = "请选择要以旧换新的车辆类型")
    @ApiModelProperty(value = "要以旧换新的车辆类型 1:新车 2:二手车", allowableValues = "1,2", example = "1", required = true)
    private Integer exchangeVehicleType;

    @NotBlank(message = "请填写要旧换新的车架号")
    @ApiModelProperty(value = "要以旧换新的车架号", example = "LC0CD4C33F1095404", required = true)
    private String exchangeVin;

    @NotBlank(message = "请填写要旧换新的车牌号")
    @ApiModelProperty(value = "要以旧换新的车牌号", example = "沪KZ5797", required = true)
    private String exchangeVehicleNo;
}
