package com.extracme.nevmp.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
public class QueryOwnerHoldVehiclesVO {

    @NotNull
    @ApiModelProperty(value = "证件类型")
    private Integer authType;

    @NotBlank
    @ApiModelProperty(value = "证件号")
    private String authId;
}