package com.extracme.nevmp.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
public class SavePrivateOwnerQualificationVO {

    @NotBlank
    @ApiModelProperty(value = "姓名", required = true)
    private  String name;

    @NotNull
    @ApiModelProperty(value = "证件类型 1:身份证 5:社会信用统一代码证 6:港澳台居民来往内地通行证 7:台湾居民来往大陆通行证" +
            " 8:中国护照（华侨） 9:外国人护照 10:外国人永久居留身份证 11:港澳台居民居住证", allowableValues = "1,6,7,8,9,10,11",required = true)
    private Integer authType;

    @NotBlank
    @ApiModelProperty(value = "证件号",required = true)
    private String authId;

    @NotBlank
    @ApiModelProperty(value = "驾驶证发证省市",required = true)
    private String driverLicenseIssuingPlace;

    @NotBlank
    @ApiModelProperty(value = "驾驶证发证机关")
    private String driverLicenseIssuingOrganization;

    @ApiModelProperty(value = "驾驶证号")
    private String driverLicenseCode;

    @ApiModelProperty(value = "档案编号")
    private String driverFileNo;

    @NotNull
    @ApiModelProperty(value = "户籍类型 0:上海市 1:外省市 2:其他")
    private Integer householdRegistrationType;

    @NotNull
    @ApiModelProperty(value = "车辆类型 1:纯电动 2:插电式混动 3:燃料电池")
    private Integer vehicleModelType;
}
