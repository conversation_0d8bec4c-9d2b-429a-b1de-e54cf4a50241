spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      web-stat-filter:
        enabled: true
      stat-view-servlet:
        enabled: true
        allow:
        login-username: admin
        login-password: 434757957
      filter:
        stat:
          enabled: true
          db-type: mysql
          log-slow-sql: true
          slow-sql-millis: 2000
        slf4j:
          enabled: true
        wall:
          enabled: true
  profiles:
    active : @profileActive@
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

mybatis:
  mapper-locations: mybatis/mapper/*.xml
  type-aliases-package: com.extracme.nevmp.onething.mapper
