package com.extracme.nevmp.service;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * <AUTHOR>
 * @date 2020/12/10
 */
@FeignClient(name = "nevmp-schedule", url = "${schedule.url}")
public interface ScheduleService {

    @GetMapping("test")
    String test();

    @GetMapping("autoReviewOwnerQualification")
    void autoReviewOwnerQualification();

    @GetMapping("dealExpireReconsiderQualification")
    void dealExpireReconsiderQualification();

    @GetMapping("dealOmitQualificationApply")
    void dealOmitQualificationApply();

    @GetMapping("dealOmitVehicleConfirmTask")
    void dealOmitVehicleConfirmTask();

    @GetMapping("queryOuterDriverHasNewEnergy")
    void queryOuterDriverHasNewEnergy();

    @GetMapping("dealPendingQualificationReview")
    void dealPendingQualificationReview();

    @GetMapping("syncVehicleRegistrationProgress")
    void syncVehicleRegistrationProgress();

    @GetMapping("trafficPushData")
    void trafficPushData();

    @GetMapping("trafficCommitteePush")
    void trafficCommitteePush();

    @GetMapping("trafficCommitteePull")
    void trafficCommitteePull();

    @GetMapping("dealOmitVehicleRegistration")
    void dealOmitVehicleRegistration();

    @GetMapping("autoProcessOwnerReview")
    void autoProcessOwnerReview();

    @GetMapping("autoSyncVehicleModelFirstRegTime")
    void autoSyncVehicleModelFirstRegTime();
}
