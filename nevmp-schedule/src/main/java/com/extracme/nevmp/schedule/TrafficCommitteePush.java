package com.extracme.nevmp.schedule;

import java.util.Date;

import org.apache.shardingsphere.elasticjob.api.ShardingContext;
import org.apache.shardingsphere.elasticjob.simple.job.SimpleJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.extracme.nevmp.service.ScheduleService;

import lombok.extern.slf4j.Slf4j;

/**
 * 交委推送数据
 * <AUTHOR>
 * @date 2020/12/8
 */
@Component
@Slf4j
public class TrafficCommitteePush implements SimpleJob {

    @Autowired
    private ScheduleService scheduleService;

    @Override
    public void execute(ShardingContext shardingContext) {
        log.info(new Date() + "开始定时处理：交委推送数据查非营业性客车额度");
        scheduleService.trafficCommitteePush();
        log.info(new Date() + "结束定时处理：交委推送数据查非营业性客车额度");
    }
}
