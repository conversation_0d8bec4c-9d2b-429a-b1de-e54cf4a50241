package com.extracme.nevmp.dto.vehicle;

import lombok.Data;

/**
 * <AUTHOR>
 * @Description
 */
@Data
public class UpdateVehicleInfoDTO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 车辆型号
     */
    private String vehicleModelId;

    /**
     * 合格证编号
     */
    private String certNo;

    /**
     * 发动机型号
     */
    private String engineId;

    /**
     * 电池组编号
     */
    private String batteryGroupNo;

    /**
     * 合格证扫描件
     */
    private String certNoScanningCopy;

    /**
     * 机构ID
     */
    private String orgId;

    /**
     * 更新人
     */
    private String updatedUser;

    /**
     * 更新人名称
     */
    private String updatedUserName;



}
