package com.extracme.nevmp.mapper.extend;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;

import com.extracme.nevmp.dto.UserFeedbackDTO;
import com.extracme.nevmp.dto.qualification.traffic.UnApproveTrafficQualificationDTO;
import com.extracme.nevmp.mapper.OwnerTrafficQualificationMapper;

/**
 * <AUTHOR>
 */
@Mapper
public interface OwnerTrafficQualificationExtendMapper extends OwnerTrafficQualificationMapper {

    /**
     *
     * @param selectStatement
     * @return
     */
    @Results(id = "UserFeedbackDTOResult", value = {
            @Result(column = "id", property = "id", jdbcType = JdbcType.BIGINT),
            @Result(column = "name", property = "name", jdbcType = JdbcType.VARCHAR),
            @Result(column = "auth_type", property = "authType", jdbcType = JdbcType.INTEGER),
            @Result(column = "auth_id", property = "authId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "driver_license_issuing_place", property = "driverLicenseIssuingPlace", jdbcType = JdbcType.VARCHAR),
            @Result(column = "driver_license_issuing_organization", property = "driverLicenseIssuingOrganization", jdbcType = JdbcType.VARCHAR),
            @Result(column = "driver_license_code", property = "driverLicenseCode", jdbcType = JdbcType.VARCHAR),
            @Result(column = "created_time", property = "endTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "driver_file_no", property = "driverFileNo", jdbcType = JdbcType.VARCHAR)
    })
    @SelectProvider(type= SqlProviderAdapter.class, method="select")
    List<UserFeedbackDTO> getByIds(SelectStatementProvider selectStatement);



    @Results(id = "SelectUnApproveTraffic", value = {
            @Result(column = "auth_id", property = "authId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "auth_type", property = "authType", jdbcType = JdbcType.INTEGER),
            @Result(column = "owner_qualification_id", property = "ownerQualificationId", jdbcType = JdbcType.BIGINT),
            @Result(column = "id", property = "id", jdbcType = JdbcType.BIGINT)
    })
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    List<UnApproveTrafficQualificationDTO> selectUnApproveTraffic(SelectStatementProvider provider);
}
