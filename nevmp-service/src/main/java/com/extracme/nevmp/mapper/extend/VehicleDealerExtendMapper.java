package com.extracme.nevmp.mapper.extend;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;

import com.extracme.nevmp.dto.vehicle.seller.ApproveVehicleDealerDTO;
import com.extracme.nevmp.mapper.VehicleDealerMapper;

/**
 * <AUTHOR>
 * @date 2020/10/22
 */
@Mapper
public interface VehicleDealerExtendMapper extends VehicleDealerMapper {

    @Results(id = "ApproveVehicleDealerDTOResult", value = {
            @Result(column = "dealer_id", property = "dealerId", jdbcType = JdbcType.VARCHAR, id = true),
            @Result(column = "dealer_name", property = "dealerName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "dealer_contacter", property = "dealerContacter", jdbcType = JdbcType.VARCHAR),
            @Result(column = "dealer_address", property = "dealerAddress", jdbcType = JdbcType.VARCHAR),
            @Result(column = "dealer_authorize_copy", property = "dealerAuthorizeCopy", jdbcType = JdbcType.VARCHAR),
            @Result(column = "mobile_phone", property = "mobilePhone", jdbcType = JdbcType.VARCHAR),
            @Result(column = "remark", property = "remark", jdbcType = JdbcType.VARCHAR),
            @Result(column = "status", property = "status", jdbcType = JdbcType.INTEGER),
            @Result(column = "org_name", property = "orgName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "disable", property = "disable", jdbcType = JdbcType.INTEGER),
            @Result(column = "expire_time", property = "expireTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "renew_time", property = "renewTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "renew_authorize_copy", property = "renewAuthorizeCopy", jdbcType = JdbcType.VARCHAR)
    })
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    List<ApproveVehicleDealerDTO> searchDealer(SelectStatementProvider selectStatement);
}
