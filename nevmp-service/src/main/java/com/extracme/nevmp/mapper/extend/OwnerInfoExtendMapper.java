package com.extracme.nevmp.mapper.extend;

import java.util.List;

import javax.annotation.Generated;

import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;

import com.extracme.nevmp.dto.gov.traffic.NewEnergyCarAudit;
import com.extracme.nevmp.dto.gov.traffic.NewEnergyCarAuditCancel;
import com.extracme.nevmp.dto.gov.traffic.UserAuditRst;
import com.extracme.nevmp.dto.owner.OwnerConfirmInfoDTO;
import com.extracme.nevmp.dto.owner.OwnerInfoDTO;
import com.extracme.nevmp.dto.owner.OwnerInfoExportDTO;
import com.extracme.nevmp.dto.owner.SelfCheckingInfoDTO;
import com.extracme.nevmp.dto.owner.secondhand.SecondHandVehicleAppliedOwnerDTO;
import com.extracme.nevmp.dto.owner.selfcheck.SelfCheckBO;
import com.extracme.nevmp.dto.owner.subsidy.OwnerSubsidyInfoDTO;
import com.extracme.nevmp.dto.owner.traffic.TrafficPushOwnerInfoDTO;
import com.extracme.nevmp.dto.receipt.OwnerReceiptInfoDTO;
import com.extracme.nevmp.mapper.OwnerInfoMapper;
import com.extracme.nevmp.model.OwnerInfo;

/**
 * <AUTHOR>
 * @Description
 */
@Mapper
public interface OwnerInfoExtendMapper extends OwnerInfoMapper {

    @Override
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @Options(useGeneratedKeys = true, keyProperty = "record.id")
    int insert(InsertStatementProvider<OwnerInfo> insertStatement);

    @Generated("extend")
    @SelectProvider(type = SqlProviderAdapter.class , method = "select")
    @Results(id = "SearchOwnerInfoResult", value = {
            @Result(column="id", property="id", jdbcType= JdbcType.VARCHAR, id=true),
            @Result(column="owner_id", property="ownerId", jdbcType= JdbcType.VARCHAR),
            @Result(column="reg_no", property="regNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="owner_type", property="ownerType", jdbcType=JdbcType.INTEGER),
            @Result(column="owner_status", property="ownerStatus", jdbcType=JdbcType.INTEGER),
            @Result(column="name", property="name", jdbcType=JdbcType.VARCHAR),
            @Result(column="auth_kind", property="authKind", jdbcType=JdbcType.VARCHAR),
            @Result(column="auth_id", property="authId", jdbcType=JdbcType.VARCHAR),
            @Result(column="vin", property="vin", jdbcType=JdbcType.VARCHAR),
            @Result(column="vehicle_model_id", property="vehicleModelId", jdbcType=JdbcType.VARCHAR),
            @Result(column="apply_time", property="applyTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="created_time", property="createdTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="charge_seq", property="chargeSeq", jdbcType=JdbcType.VARCHAR),
            @Result(column="CHARGE_DEVICE_TYPE", property="chargeDeviceType", jdbcType=JdbcType.VARCHAR),
            @Result(column="remark", property="remark", jdbcType=JdbcType.VARCHAR),
            @Result(column="org_id", property="orgId", jdbcType=JdbcType.VARCHAR),
            @Result(column="org_name", property="orgName", jdbcType=JdbcType.VARCHAR),
            @Result(column="veh_seller", property="vehSeller", jdbcType=JdbcType.VARCHAR),
            @Result(column="apply_to_cancel_status", property="applyToCancelStatus", jdbcType=JdbcType.INTEGER),
            @Result(column="vehicle_no", property="vehicleNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="charge_audit_time", property="chargeAuditTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="disable", property="disable", jdbcType=JdbcType.INTEGER),
            @Result(column="CHARGE_CONFIRM_COPY", property="chargeConfirmCopy", jdbcType=JdbcType.VARCHAR),
            @Result(column="apply_no", property="applyNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="CHARGE_PHOTO_COPY", property="chargePhotoCopy", jdbcType=JdbcType.VARCHAR),
            @Result(column="owner_unbind_status",property="ownerUnbindStatus", jdbcType=JdbcType.INTEGER),
            @Result(column="owner_unbind_apply_time", property="ownerUnbindApplyTime", jdbcType=JdbcType.TIMESTAMP)
    })
    List<OwnerInfoDTO> searchOwnerInfo(SelectStatementProvider selectStatement);

    @Generated("extend")
    @SelectProvider(type = SqlProviderAdapter.class , method = "select")
    @Results(id = "SearchOwnerReceiptInfoResult", value = {
            @Result(column="id", property="id", jdbcType= JdbcType.VARCHAR, id=true),
            @Result(column="owner_id", property="ownerId", jdbcType= JdbcType.VARCHAR),
            @Result(column="owner_type", property="ownerType", jdbcType=JdbcType.INTEGER),
            @Result(column="name", property="name", jdbcType=JdbcType.VARCHAR),
            @Result(column="auth_kind", property="authKind", jdbcType=JdbcType.VARCHAR),
            @Result(column="auth_id", property="authId", jdbcType=JdbcType.VARCHAR),
            @Result(column="charge_audit_time", property="chargeAuditTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="reg_no", property="regNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="vin", property="vin", jdbcType=JdbcType.VARCHAR),
            @Result(column="vehicle_model_id", property="vehicleModelId", jdbcType=JdbcType.VARCHAR),
            @Result(column="org_id", property="orgId", jdbcType=JdbcType.VARCHAR),
            @Result(column="org_name", property="orgName", jdbcType=JdbcType.VARCHAR),
            @Result(column="veh_seller", property="vehSeller", jdbcType=JdbcType.VARCHAR),
            @Result(column="receipt_status", property="receiptStatus", jdbcType=JdbcType.INTEGER),
            @Result(column="receipt_lock_status", property="receiptLockStatus", jdbcType=JdbcType.INTEGER),
            @Result(column="receipt_no",property="receiptNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="receipt_date",property="receiptDate", jdbcType=JdbcType.TIMESTAMP)
    })
    List<OwnerReceiptInfoDTO> searchOwnerReceiptInfo(SelectStatementProvider selectStatement);

    @Generated("extend")
    @SelectProvider(type = SqlProviderAdapter.class , method = "select")
    @Results(id = "ExportOwnerInfoResult", value = {
            @Result(column="id", property="id", jdbcType= JdbcType.VARCHAR, id=true),
            @Result(column="owner_id", property="ownerId", jdbcType= JdbcType.VARCHAR),
            @Result(column="reg_no", property="regNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="owner_type", property="ownerType", jdbcType=JdbcType.INTEGER),
            @Result(column="owner_status", property="ownerStatus", jdbcType=JdbcType.INTEGER),
            @Result(column="name", property="name", jdbcType=JdbcType.VARCHAR),
            @Result(column="auth_kind", property="authKind", jdbcType=JdbcType.VARCHAR),
            @Result(column="auth_id", property="authId", jdbcType=JdbcType.VARCHAR),
            @Result(column="vin", property="vin", jdbcType=JdbcType.VARCHAR),
            @Result(column="vehicle_model_id", property="vehicleModelId", jdbcType=JdbcType.VARCHAR),
            @Result(column="apply_time", property="applyTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="created_time", property="createdTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="CHARGE_OPERATOR_NAME", property="chargeOperatorName", jdbcType=JdbcType.VARCHAR),
            @Result(column="charge_seq", property="chargeSeq", jdbcType=JdbcType.VARCHAR),
            @Result(column="CHARGE_DEVICE_TYPE", property="chargeDeviceType", jdbcType=JdbcType.VARCHAR),
            @Result(column="remark", property="remark", jdbcType=JdbcType.VARCHAR),
            @Result(column="org_id", property="orgId", jdbcType=JdbcType.VARCHAR),
            @Result(column="org_name", property="orgName", jdbcType=JdbcType.VARCHAR),
            @Result(column="veh_seller", property="vehSeller", jdbcType=JdbcType.VARCHAR),
            @Result(column="apply_to_cancel_status", property="applyToCancelStatus", jdbcType=JdbcType.INTEGER),
            @Result(column="vehicle_no", property="vehicleNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="charge_audit_time", property="chargeAuditTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="disable", property="disable", jdbcType=JdbcType.INTEGER),
            @Result(column="CHARGE_CONFIRM_COPY", property="chargeConfirmCopy", jdbcType=JdbcType.VARCHAR),
            @Result(column="apply_no", property="applyNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="CHARGE_PHOTO_COPY", property="chargePhotoCopy", jdbcType=JdbcType.VARCHAR),
            @Result(column="charge_device_property", property="chargeDeviceProperty", jdbcType=JdbcType.INTEGER),
            @Result(column="charge_device_district", property="chargeDeviceDistrict", jdbcType=JdbcType.VARCHAR),
            @Result(column="charge_device_street", property="chargeDeviceStreet", jdbcType=JdbcType.VARCHAR),
            @Result(column="charge_device_park_no", property="chargeDeviceParkNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="charge_device_start_time", property="chargeDeviceStartTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="owner_charge_device_property", property="ownerChargeDeviceProperty", jdbcType=JdbcType.TIMESTAMP)
    })
    List<OwnerInfoExportDTO> exportOwnerInfo(SelectStatementProvider selectStatement);


    @Generated("extend")
    @SelectProvider(type = SqlProviderAdapter.class , method = "select")
    @Results(id = "SearchOwnerConfirmListResult", value = {
            @Result(column="id", property="id", jdbcType= JdbcType.VARCHAR, id=true),
            @Result(column="reg_no", property="regNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="owner_type", property="ownerType", jdbcType=JdbcType.INTEGER),
            @Result(column="name", property="name", jdbcType=JdbcType.VARCHAR),
            @Result(column="auth_kind", property="authKind", jdbcType=JdbcType.VARCHAR),
            @Result(column="auth_id", property="authId", jdbcType=JdbcType.VARCHAR),
            @Result(column="vin", property="vin", jdbcType=JdbcType.VARCHAR),
            @Result(column="vehicle_model_id", property="vehicleModelId", jdbcType=JdbcType.VARCHAR),
            @Result(column="apply_to_owner_confirm_time", property="applyToOwnerConfirmTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="charge_seq", property="chargeSeq", jdbcType=JdbcType.VARCHAR),
            @Result(column="veh_seller", property="vehSeller", jdbcType=JdbcType.VARCHAR),
            @Result(column="CHARGE_CONFIRM_COPY", property="chargeConfirmCopy", jdbcType=JdbcType.VARCHAR),
            @Result(column="uapply_no", property="uapplyNo", jdbcType=JdbcType.VARCHAR)
    })
    List<OwnerConfirmInfoDTO> searchOwnerConfirmList(SelectStatementProvider selectStatementProvider);


    @Generated("extend")
    @SelectProvider(type = SqlProviderAdapter.class , method = "select")
    @Results(id = "SearchSelfCheckingInfoResult", value = {
            @Result(column="id", property="id", jdbcType= JdbcType.VARCHAR, id=true),
            @Result(column="reg_no", property="regNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="owner_type", property="ownerType", jdbcType=JdbcType.INTEGER),
            @Result(column="name", property="name", jdbcType=JdbcType.VARCHAR),
            @Result(column="auth_kind", property="authKind", jdbcType=JdbcType.VARCHAR),
            @Result(column="auth_id", property="authId", jdbcType=JdbcType.VARCHAR),
            @Result(column="vin", property="vin", jdbcType=JdbcType.VARCHAR),
            @Result(column="vehicle_model_id", property="vehicleModelId", jdbcType=JdbcType.VARCHAR),
            @Result(column="charge_seq", property="chargeSeq", jdbcType=JdbcType.VARCHAR),
            @Result(column="veh_seller", property="vehSeller", jdbcType=JdbcType.VARCHAR),
            @Result(column="charge_audit_time", property="chargeAuditTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="self_checking_audit_time", property="selfCheckingAuditTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="self_checking_file", property="selfCheckingFile", jdbcType=JdbcType.VARCHAR),
            @Result(column="self_checking_status", property="selfCheckingStatus", jdbcType=JdbcType.INTEGER),
            @Result(column="self_checking_expire_time", property="selfCheckingExpireTime", jdbcType=JdbcType.TIMESTAMP)
    })
    List<SelfCheckingInfoDTO> searchSelfCheckingInfo(SelectStatementProvider selectStatementProvider);



    @Results(id = "NewEnergyCarAudit", value = {
            @Result(column = "id", property = "id", jdbcType = JdbcType.BIGINT),
            @Result(column = "name", property = "username", jdbcType = JdbcType.VARCHAR),
            @Result(column = "auth_kind", property = "certType", jdbcType = JdbcType.VARCHAR),
            @Result(column = "auth_id", property = "certNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "org_name", property = "carCorpName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "vehicle_brand", property = "carBrand", jdbcType = JdbcType.VARCHAR),
            @Result(column = "vehicle_model_name", property = "carModel", jdbcType = JdbcType.VARCHAR),
            @Result(column = "vin", property = "vin", jdbcType = JdbcType.VARCHAR)
    })
    @SelectProvider(type = SqlProviderAdapter.class , method = "select")
    List<NewEnergyCarAudit> unPushNewCarConfirmAndQuotaAudit(SelectStatementProvider selectStatementProvider);

    @SelectProvider(type = SqlProviderAdapter.class , method = "select")
    @Results(id = "UserAuditRst", value = {
            @Result(column = "id", property = "id", jdbcType = JdbcType.BIGINT),
            @Result(column = "owner_id", property = "uuid", jdbcType = JdbcType.VARCHAR),
            @Result(column = "owner_type", property = "userQuality", jdbcType = JdbcType.INTEGER),
            @Result(column = "name", property = "userName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "auth_kind", property = "certType", jdbcType = JdbcType.VARCHAR),
            @Result(column = "auth_id", property = "certNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "charge_seq", property = "chargeElecPostNo", jdbcType = JdbcType.VARCHAR)
    })
    List<UserAuditRst> unPushOldCarConfirm(SelectStatementProvider secondhand);



    @Results(id = "NewEnergyCarAuditCancel", value = {
            @Result(column = "id", property = "id", jdbcType = JdbcType.BIGINT),
            @Result(column = "name", property = "userName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "auth_kind", property = "certType", jdbcType = JdbcType.VARCHAR),
            @Result(column = "auth_id", property = "certNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "vin", property = "vin", jdbcType = JdbcType.VARCHAR)
    })
    @SelectProvider(type = SqlProviderAdapter.class , method = "select")
    List<NewEnergyCarAuditCancel> unPushNewCarCancel(SelectStatementProvider provider);


    @Generated("extend")
    @SelectProvider(type = SqlProviderAdapter.class , method = "select")
    @Results(id = "SearchOwnerSubsidyInfoResult", value = {
            @Result(column="id", property="id", jdbcType= JdbcType.VARCHAR, id=true),
            @Result(column="reg_no", property="regNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="owner_type", property="ownerType", jdbcType=JdbcType.INTEGER),
            @Result(column="name", property="name", jdbcType=JdbcType.VARCHAR),
            @Result(column="auth_kind", property="authKind", jdbcType=JdbcType.VARCHAR),
            @Result(column="auth_id", property="authId", jdbcType=JdbcType.VARCHAR),
            @Result(column="vin", property="vin", jdbcType=JdbcType.VARCHAR),
            @Result(column="vehicle_model_id", property="vehicleModelId", jdbcType=JdbcType.VARCHAR),
            @Result(column="veh_seller", property="vehSeller", jdbcType=JdbcType.VARCHAR),
            @Result(column="charge_audit_time", property="chargeAuditTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="INVOICE_COPY", property="invoiceCopy", jdbcType=JdbcType.VARCHAR),
            @Result(column="AUTH_COPY", property="authCopy", jdbcType=JdbcType.VARCHAR),
            @Result(column="VEHICLE_LICENSE_COPY", property="vehicleLicenseCopy", jdbcType=JdbcType.VARCHAR),
            @Result(column="ORG_CODE_COPY", property="orgCodeCopy", jdbcType=JdbcType.VARCHAR),
            @Result(column="subsidy_status", property="subsidyStatus", jdbcType=JdbcType.INTEGER),
            @Result(column="INVOICE_NO", property="invoiceNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="INVOICE_DATE", property="invoiceDate", jdbcType=JdbcType.VARCHAR),
            @Result(column="VEHICLE_LICENSE_DATE", property="vehicleLicenseDate", jdbcType=JdbcType.VARCHAR),
            @Result(column="PURCHASE_FIELD", property="purchaseField", jdbcType=JdbcType.VARCHAR),
            @Result(column="PURCHASE_PRICE", property="purchasePrice", jdbcType=JdbcType.DECIMAL),
            @Result(column="VEHICLE_NO", property="vehicleNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="org_name", property="orgName", jdbcType=JdbcType.VARCHAR),
            @Result(column="VEHICLE_TYPE", property="vehicleType", jdbcType=JdbcType.INTEGER),
            @Result(column="SUBSIDY_APPLY_DATE", property="subsidyApplyDate", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="IS_MAKEUP", property = "subsidyMark", jdbcType=JdbcType.VARCHAR),
            @Result(column="org_product_name", property = "orgProductName", jdbcType=JdbcType.VARCHAR)
    })
    List<OwnerSubsidyInfoDTO> searchOwnerSubsidyInfo(SelectStatementProvider selectStatementProvider);

    @Generated("extend")
    @SelectProvider(type = SqlProviderAdapter.class , method = "select")
    @Results(id = "SearchSelfCheckOwnerResult", value = {
            @Result(column = "id", property = "id", jdbcType = JdbcType.VARCHAR, id = true),
            @Result(column = "OWNER_TYPE", property = "ownerType", jdbcType = JdbcType.INTEGER),
            @Result(column = "NAME", property = "name", jdbcType = JdbcType.VARCHAR),
            @Result(column = "AUTH_KIND", property = "authKind", jdbcType = JdbcType.VARCHAR),
            @Result(column = "AUTH_ID", property = "authId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "owner_status", property = "ownerStatus", jdbcType = JdbcType.INTEGER),
            @Result(column = "charge_seq", property = "chargeSeq", jdbcType = JdbcType.VARCHAR),
            @Result(column = "CHARGE_AUDIT_TIME", property = "chargeAuditTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "SELF_CHECKING_STATUS", property = "selfCheckingStatus", jdbcType = JdbcType.INTEGER),
            @Result(column = "self_checking_expire_time", property = "selfCheckingExpireTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "SELF_CHECKING_FILE", property = "selfCheckingFile", jdbcType = JdbcType.VARCHAR),
            @Result(column = "charge_nameplate_photo", property = "chargeNameplatePhoto", jdbcType = JdbcType.VARCHAR),
            @Result(column = "vin", property = "vin", jdbcType = JdbcType.VARCHAR),
            @Result(column = "vehicle_model_id", property = "vehicleModelId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "VEH_SELLER", property = "vehSeller", jdbcType = JdbcType.VARCHAR),
            @Result(column = "disable", property = "disable", jdbcType = JdbcType.INTEGER),
            @Result(column = "REG_NO", property = "regNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "org_id", property = "orgId", jdbcType = JdbcType.VARCHAR)
    })
    List<SelfCheckBO> searchSelfCheckOwner(SelectStatementProvider searchSelfCheckOwner);

    @Generated("extend")
    @SelectProvider(type = SqlProviderAdapter.class , method = "select")
    @Results(id="TrafficPushOwnerInfoResult", value = {
            @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
            @Result(column="OWNER_ID", property="ownerId", jdbcType=JdbcType.VARCHAR),
            @Result(column="PURCHASE_FIELD", property="purchaseField", jdbcType=JdbcType.VARCHAR),
            @Result(column="GROUP_BUYING", property="groupBuying", jdbcType=JdbcType.VARCHAR),
            @Result(column="GROUP_BUYING_SEQ", property="groupBuyingSeq", jdbcType=JdbcType.VARCHAR),
            @Result(column="OWNER_TYPE", property="ownerType", jdbcType=JdbcType.INTEGER),
            @Result(column="NAME", property="name", jdbcType=JdbcType.VARCHAR),
            @Result(column="TEL", property="tel", jdbcType=JdbcType.VARCHAR),
            @Result(column="AUTH_KIND", property="authKind", jdbcType=JdbcType.VARCHAR),
            @Result(column="AUTH_ID", property="authId", jdbcType=JdbcType.VARCHAR),
            @Result(column="PURCHASE_DATE", property="purchaseDate", jdbcType=JdbcType.VARCHAR),
            @Result(column="PURCHASE_PRICE", property="purchasePrice", jdbcType=JdbcType.DECIMAL),
            @Result(column="INVOICE_NO", property="invoiceNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="INVOICE_DATE", property="invoiceDate", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="USAGES", property="usages", jdbcType=JdbcType.INTEGER),
            @Result(column="VEHICLE_LICENSE_DATE", property="vehicleLicenseDate", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="SUBSIDY_AMOUNT", property="subsidyAmount", jdbcType=JdbcType.DECIMAL),
            @Result(column="APPLY_GROUP_NO", property="applyGroupNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="CHARGE_DEVICE_ADDRESS", property="chargeDeviceAddress", jdbcType=JdbcType.VARCHAR),
            @Result(column="CHARGE_DEVICE_TYPE", property="chargeDeviceType", jdbcType=JdbcType.VARCHAR),
            @Result(column="AUTH_COPY", property="authCopy", jdbcType=JdbcType.VARCHAR),
            @Result(column="AUTH_COPY_OSS", property="authCopyOss", jdbcType=JdbcType.VARCHAR),
            @Result(column="CHARGE_CONFIRM_COPY", property="chargeConfirmCopy", jdbcType=JdbcType.VARCHAR),
            @Result(column="CHARGE_CONFIRM_COPY_OSS", property="chargeConfirmCopyOss", jdbcType=JdbcType.VARCHAR),
            @Result(column="INVOICE_COPY", property="invoiceCopy", jdbcType=JdbcType.VARCHAR),
            @Result(column="INVOICE_COPY_OSS", property="invoiceCopyOss", jdbcType=JdbcType.VARCHAR),
            @Result(column="TAX_COPY", property="taxCopy", jdbcType=JdbcType.VARCHAR),
            @Result(column="VEHICLE_LICENSE_COPY", property="vehicleLicenseCopy", jdbcType=JdbcType.VARCHAR),
            @Result(column="VEHICLE_LICENSE_COPY_OSS", property="vehicleLicenseCopyOss", jdbcType=JdbcType.VARCHAR),
            @Result(column="VEHICLE_REG_COPY", property="vehicleRegCopy", jdbcType=JdbcType.VARCHAR),
            @Result(column="ORG_CODE_COPY", property="orgCodeCopy", jdbcType=JdbcType.VARCHAR),
            @Result(column="ORG_CODE_COPY_OSS", property="orgCodeCopyOss", jdbcType=JdbcType.VARCHAR),
            @Result(column="ORG_LICENSE_COPY", property="orgLicenseCopy", jdbcType=JdbcType.VARCHAR),
            @Result(column="OTHER_COPY", property="otherCopy", jdbcType=JdbcType.VARCHAR),
            @Result(column="OTHER_COPY_OSS", property="otherCopyOss", jdbcType=JdbcType.VARCHAR),
            @Result(column="EMP_CERT_COPY", property="empCertCopy", jdbcType=JdbcType.VARCHAR),
            @Result(column="STATUS", property="status", jdbcType=JdbcType.INTEGER),
            @Result(column="REMARK", property="remark", jdbcType=JdbcType.VARCHAR),
            @Result(column="REMARK_SUBSIDY", property="remarkSubsidy", jdbcType=JdbcType.VARCHAR),
            @Result(column="CREATED_TIME", property="createdTime", jdbcType=JdbcType.VARCHAR),
            @Result(column="CREATED_USER", property="createdUser", jdbcType=JdbcType.VARCHAR),
            @Result(column="UPDATED_TIME", property="updatedTime", jdbcType=JdbcType.VARCHAR),
            @Result(column="UPDATED_USER", property="updatedUser", jdbcType=JdbcType.VARCHAR),
            @Result(column="LATITUDE", property="latitude", jdbcType=JdbcType.DECIMAL),
            @Result(column="LONGITUDE", property="longitude", jdbcType=JdbcType.DECIMAL),
            @Result(column="CHARGE_PHOTO_COPY", property="chargePhotoCopy", jdbcType=JdbcType.VARCHAR),
            @Result(column="CHARGE_PHOTO_COPY_OSS", property="chargePhotoCopyOss", jdbcType=JdbcType.VARCHAR),
            @Result(column="BUSINESS_LICENSE_COPY", property="businessLicenseCopy", jdbcType=JdbcType.VARCHAR),
            @Result(column="CHARGE_SEQ", property="chargeSeq", jdbcType=JdbcType.VARCHAR),
            @Result(column="CHARGE_OPERATOR_ID", property="chargeOperatorId", jdbcType=JdbcType.VARCHAR),
            @Result(column="CHARGE_OPERATOR_NAME", property="chargeOperatorName", jdbcType=JdbcType.VARCHAR),
            @Result(column="VEH_SELLER", property="vehSeller", jdbcType=JdbcType.VARCHAR),
            @Result(column="CHARGE_BUILD_ID", property="chargeBuildId", jdbcType=JdbcType.VARCHAR),
            @Result(column="CHECK_STATUS", property="checkStatus", jdbcType=JdbcType.VARCHAR),
            @Result(column="CHECK_PIC1", property="checkPic1", jdbcType=JdbcType.VARCHAR),
            @Result(column="CHECK_PIC_OSS1", property="checkPicOss1", jdbcType=JdbcType.VARCHAR),
            @Result(column="CHECK_PIC2", property="checkPic2", jdbcType=JdbcType.VARCHAR),
            @Result(column="CHECK_PIC_OSS2", property="checkPicOss2", jdbcType=JdbcType.VARCHAR),
            @Result(column="CHECK_PIC3", property="checkPic3", jdbcType=JdbcType.VARCHAR),
            @Result(column="CHECK_PIC_OSS3", property="checkPicOss3", jdbcType=JdbcType.VARCHAR),
            @Result(column="CHECK_UPDATED_TIME", property="checkUpdatedTime", jdbcType=JdbcType.VARCHAR),
            @Result(column="CHECK_COUNT", property="checkCount", jdbcType=JdbcType.VARCHAR),
            @Result(column="IMPORT_TIME", property="importTime", jdbcType=JdbcType.INTEGER),
            @Result(column="APPROVE_TIME", property="approveTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="CHECK_PIC_copy1", property="checkPicCopy1", jdbcType=JdbcType.VARCHAR),
            @Result(column="CHECK_PIC_COPY2", property="checkPicCopy2", jdbcType=JdbcType.VARCHAR),
            @Result(column="CHECK_PIC_COPY3", property="checkPicCopy3", jdbcType=JdbcType.VARCHAR),
            @Result(column="SUBSIDY_APPLY_DATE", property="subsidyApplyDate", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="SIGNED_COMMITMENT", property="signedCommitment", jdbcType=JdbcType.INTEGER),
            @Result(column="STAKE_ID", property="stakeId", jdbcType=JdbcType.INTEGER),
            @Result(column="IS_BUSINESS", property="isBusiness", jdbcType=JdbcType.INTEGER),
            @Result(column="STRAPPING_TABLE_COPY", property="strappingTableCopy", jdbcType=JdbcType.VARCHAR),
            @Result(column="CHARGE_AUDIT_TIME", property="chargeAuditTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="SELF_CHECKING_AUDIT_TIME", property="selfCheckingAuditTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="SELF_CHECKING_FILE", property="selfCheckingFile", jdbcType=JdbcType.VARCHAR),
            @Result(column="charge_nameplate_photo", property="chargeNameplatePhoto", jdbcType=JdbcType.VARCHAR),
            @Result(column="SELF_CHECKING_STATUS", property="selfCheckingStatus", jdbcType=JdbcType.INTEGER),
            @Result(column="self_checking_expire_time", property="selfCheckingExpireTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="owner_status", property="ownerStatus", jdbcType=JdbcType.INTEGER),
            @Result(column="subsidy_status", property="subsidyStatus", jdbcType=JdbcType.INTEGER),
            @Result(column="flag", property="flag", jdbcType=JdbcType.INTEGER),
            @Result(column="apply_time", property="applyTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="apply_to_owner_confirm_time", property="applyToOwnerConfirmTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="apply_to_cancel_time", property="applyToCancelTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="apply_to_cancel_reason", property="applyToCancelReason", jdbcType=JdbcType.VARCHAR),
            @Result(column="apply_to_cancel_status", property="applyToCancelStatus", jdbcType=JdbcType.INTEGER),
            @Result(column="disable", property="disable", jdbcType=JdbcType.INTEGER),
            @Result(column="apply_no", property="applyNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="vehicle_apply_no", property="vehicleApplyNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="uapply_no", property="uapplyNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="user_id", property="userId", jdbcType=JdbcType.VARCHAR),
            @Result(column="traffic_sync_status", property="trafficSyncStatus", jdbcType=JdbcType.INTEGER),
            @Result(column="vehicle_id", property="vehicleId", jdbcType=JdbcType.BIGINT),
            @Result(column="vin", property="vin", jdbcType=JdbcType.VARCHAR),
            @Result(column="org_id", property="orgId", jdbcType=JdbcType.VARCHAR),
            @Result(column="vehicle_model_id", property="vehicleModelId", jdbcType=JdbcType.VARCHAR),
            @Result(column="vehicle_no", property="vehicleNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="vehicle_auth_id", property="vehicleAuthId", jdbcType=JdbcType.VARCHAR),
            @Result(column="cert_no", property="certNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="engine_id", property="engineId", jdbcType=JdbcType.VARCHAR),
            @Result(column="battery_group_no", property="batteryGroupNo", jdbcType=JdbcType.VARCHAR)
    })
    List<TrafficPushOwnerInfoDTO> trafficPushOwnerInfo(SelectStatementProvider trafficPushOwnerInfo);

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="SecondVehicleOwnerInfoResult", value = {
            @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
            @Result(column="OWNER_TYPE", property="ownerType", jdbcType=JdbcType.INTEGER),
            @Result(column="NAME", property="name", jdbcType=JdbcType.VARCHAR),
            @Result(column="AUTH_KIND", property="authKind", jdbcType=JdbcType.VARCHAR),
            @Result(column="AUTH_ID", property="authId", jdbcType=JdbcType.VARCHAR),
            @Result(column="owner_status", property="ownerStatus", jdbcType=JdbcType.INTEGER),
            @Result(column="CHARGE_SEQ", property="chargeSeq", jdbcType=JdbcType.VARCHAR),
            @Result(column="CHARGE_DEVICE_ADDRESS", property="chargeDeviceAddress", jdbcType=JdbcType.VARCHAR),
            @Result(column="CHARGE_CONFIRM_COPY", property="chargeConfirmCopy", jdbcType=JdbcType.VARCHAR),
            @Result(column="CHARGE_PHOTO_COPY", property="chargePhotoCopy", jdbcType=JdbcType.VARCHAR),
            @Result(column="charge_nameplate_photo", property="chargeNameplatePhoto", jdbcType=JdbcType.VARCHAR),
            @Result(column="business_batch_purchase_report_copy", property="businessBatchPurchaseReportCopy", jdbcType=JdbcType.VARCHAR),
            @Result(column="apply_time", property="applyTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="remark", property="remark", jdbcType=JdbcType.VARCHAR),
            @Result(column="disable", property="disable", jdbcType=JdbcType.INTEGER),
            @Result(column="owner_unbind_status",property="ownerUnbindStatus", jdbcType=JdbcType.INTEGER),
            @Result(column="owner_vehicle_model_type", property="ownerVehicleModelType", jdbcType=JdbcType.INTEGER),
            @Result(column="GROUP_BUYING", property="groupBuying", jdbcType=JdbcType.VARCHAR),
            @Result(column="owner_unbind_apply_time", property="ownerUnbindApplyTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="vin", property="vin", jdbcType=JdbcType.VARCHAR),
            @Result(column="VEHICLE_TYPE", property="vehicleType", jdbcType=JdbcType.INTEGER)
    })
    List<SecondHandVehicleAppliedOwnerDTO> searchSecondHandVehicleOwner(SelectStatementProvider selectStatement);
}
