package com.extracme.nevmp.mapper.extend;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;

import com.extracme.nevmp.dto.authority.UserInfoDetailDTO;
import com.extracme.nevmp.mapper.SyuserMapper;

/**
 * <AUTHOR>
 * @date 2020/9/1
 */
@Mapper
public interface SyuserExtendMapper extends SyuserMapper {



    @SelectProvider(type= SqlProviderAdapter.class, method="select")
    @Results(id="UserInfoDetailDTOResult", value = {
            @Result(column="ID", property="userId", jdbcType= JdbcType.VARCHAR, id=true),
            @Result(column="LOGINNAME", property="loginname", jdbcType=JdbcType.VARCHAR),
            @Result(column="NAME", property="name", jdbcType=JdbcType.VARCHAR),
            @Result(column = "ORG_KIND", property = "orgKind", jdbcType = JdbcType.VARCHAR),
            @Result(column="ORG_NAME", property="orgName", jdbcType=JdbcType.VARCHAR),
            @Result(column = "OPERATOR_NAME", property = "operatorName", jdbcType = JdbcType.VARCHAR),
            @Result(column="TEL", property="tel", jdbcType=JdbcType.VARCHAR),
            @Result(column = "roleName", property = "roleName", jdbcType = JdbcType.VARCHAR)
    })
    List<UserInfoDetailDTO> queryUserRoles(SelectStatementProvider statementProvider);


}
