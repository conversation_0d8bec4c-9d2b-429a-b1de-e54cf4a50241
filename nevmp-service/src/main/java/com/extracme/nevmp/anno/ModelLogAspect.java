package com.extracme.nevmp.anno;

import java.lang.reflect.Method;
import java.util.Date;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.expression.AnnotatedElementKey;
import org.springframework.expression.EvaluationContext;
import org.springframework.stereotype.Component;

import com.extracme.nevmp.mapper.VehicleModelOperateLogMapper;
import com.extracme.nevmp.model.VehicleModelOperateLog;

/**
 * <AUTHOR>
 * @date 2020/11/10
 */
@Aspect
@Component
public class ModelLogAspect {

    @Autowired
    private VehicleModelOperateLogMapper vehicleModelOperateLogMapper;

    private final ExpressionEvaluator<String> evaluator = new ExpressionEvaluator<>();

    @Pointcut(value = "@annotation(com.extracme.nevmp.anno.ModelLog)")
    public void modelLogPointCut() {}


    @AfterReturning(value = "modelLogPointCut() && @annotation(modelLog)", argNames = "joinPoint, modelLog")
    public void after(JoinPoint joinPoint, ModelLog modelLog) {
        String userId = getValue(joinPoint, modelLog.uId());
        String userName = getValue(joinPoint, modelLog.uName());
        Date date = new Date();
        VehicleModelOperateLog operateLog = new VehicleModelOperateLog();
        operateLog.setOrgId(getValue(joinPoint, modelLog.org()));
        operateLog.setContent(modelLog.type().getContent());
        operateLog.setVehicleModelId(getValue(joinPoint, modelLog.model()));
        operateLog.setCreatedUserId(userId);
        operateLog.setCreatedUserName(userName);
        operateLog.setCreatedTime(date);
        operateLog.setUpdatedUserId(userId);
        operateLog.setUpdatedUserName(userName);
        operateLog.setCreatedTime(date);
        vehicleModelOperateLogMapper.insertSelective(operateLog);
    }

    private String getValue(JoinPoint joinPoint, String condition) {
        return getValue(joinPoint.getTarget(), joinPoint.getArgs(),
                joinPoint.getTarget().getClass(),
                ((MethodSignature) joinPoint.getSignature()).getMethod(), condition);
    }

    private String getValue(Object object, Object[] args, Class clazz, Method method, String condition) {
        if (args == null) {
            return null;
        }
        EvaluationContext evaluationContext = evaluator.createEvaluationContext(object, clazz, method, args);
        AnnotatedElementKey methodKey = new AnnotatedElementKey(method, clazz);
        return evaluator.condition(condition, methodKey, evaluationContext, String.class);
    }
}
