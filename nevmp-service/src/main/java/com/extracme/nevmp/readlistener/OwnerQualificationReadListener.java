package com.extracme.nevmp.readlistener;

import java.util.List;
import java.util.Objects;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.extracme.nevmp.dto.UserFeedbackDTO;
import com.extracme.nevmp.error.ServiceException;
import com.extracme.nevmp.service.qualification.DriverService;
import com.extracme.nevmp.utils.TransUtil;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class OwnerQualificationReadListener extends AnalysisEventListener<UserFeedbackDTO> {

    private List<UserFeedbackDTO> list;

    private List error;

    private DriverService driverService;

    public OwnerQualificationReadListener(List<UserFeedbackDTO> list, DriverService driverService) {
        this.list = list;
        this.driverService = driverService;
    }

    @Override
    public void onException(Exception exception, AnalysisContext context) throws Exception {
        throw exception;
    }

    @Override
    public void invoke(UserFeedbackDTO data, AnalysisContext context) {
        if (data != null) {
            data.setHasDriverLicense(transHasDriverLicense(data.getHasDriverLicenseStr()));
            data.setAuthType(transAuthType(data.getIdCardName()));
            if (Integer.valueOf(1).equals(data.getHasDriverLicense())) {
                data.setDriverLicenseNoExpire(transDriverLicenseNoExpire(data.getDriverLicenseNoExpireStr()));
                data.setDriverLicenseIsValidity(transDriverLicenseIsValidity(data.getDriverLicenseIsValidityStr()));
                data.setDriverLicenseHasViolation(transDriverLicenseHasViolation(data.getDriverLicenseHasViolationStr()));
                data.setHasPunish(transHasPunish(data.getHasPunishStr()));
            }
            list.add(data);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        log.info("excel 处理结束");
    }

    /**
     * 有无驾照
     *
     * @param driverLicenseStr
     * @return
     */
    private Integer transHasDriverLicense(String driverLicenseStr) {
        Integer result = null;
        if (driverLicenseStr != null) {
            switch (driverLicenseStr) {
                case "有":
                    result = 1;
                    break;
                case "无":
                    result = 0;
                    break;
                default:
            }
        }
        if (Objects.isNull(result)) {
            throw new ServiceException("有无驾照必填（有或无）， 请检查Excel。");
        }
        return result;
    }


    private Integer tranHasNewEnergyVehicle(String hasNewEnergyVehicleStr) {
        Integer result = null;
        if (hasNewEnergyVehicleStr != null) {
            switch (hasNewEnergyVehicleStr) {
                case "有":
                    result = 0;
                    break;
                case "无":
                    result = 1;
                    break;
                default:
            }
        }
        if (Objects.isNull(result)) {
            throw new ServiceException("有驾照，名下有无新能源车必填（有或无），请检查Excel。");
        }
        return result;
    }
    private Integer tranHasNewEnergyVehicle(Boolean hasNewEnergyVehicle) {
        if (hasNewEnergyVehicle) {
            return 0;
        }
        return 1;
    }

        /**
         * 有无处罚
         *
         * @param hasPunishStr
         * @return
         */
    private Integer transHasPunish(String hasPunishStr) {
        Integer result = null;
        if (hasPunishStr != null) {
            switch (hasPunishStr) {
                case "无":
                    result = 1;
                    break;
                case "有":
                    result = 0;
                    break;
                default:
            }
        }
        if (Objects.isNull(result)) {
            throw new ServiceException("有驾照，有无处罚必填（有或无），请检查Excel。");
        }
        return result;
    }

    /**
     * 违法次数
     *
     * @param driverLicenseHasViolationStr
     * @return
     */
    private Integer transDriverLicenseHasViolation(String driverLicenseHasViolationStr) {
        Integer result = null;
        if (driverLicenseHasViolationStr != null) {
            switch (driverLicenseHasViolationStr) {
                case "小于5次":
                    result = 1;
                    break;
                case "大于等于5次":
                    result = 0;
                    break;
                default:
            }
        }
        if (Objects.isNull(result)) {
            throw new ServiceException("有驾照，违法次数必填（小于5次或大于等于5次），请检查Excel。");
        }
        return result;
    }

    /**
     * 驾驶证状态
     *
     * @param driverLicenseIsValidityStr
     * @return
     */
    private Integer transDriverLicenseIsValidity(String driverLicenseIsValidityStr) {
        Integer result = null;
        if (driverLicenseIsValidityStr != null) {
            switch (driverLicenseIsValidityStr) {
                case "有效":
                    result = 1;
                    break;
                case "无效":
                    result = 2;
                    break;
                default:
            }
        }
        if (Objects.isNull(result)) {
            throw new ServiceException("有驾照，驾驶证状态必填（有效或无效），请检查Excel。");
        }
        return result;
    }

    /**
     * 驾驶证有效期
     *
     * @param driverLicenseNoExpireStr
     * @return
     */
    private Integer transDriverLicenseNoExpire(String driverLicenseNoExpireStr) {
        Integer result = null;
        if (driverLicenseNoExpireStr != null) {
            switch (driverLicenseNoExpireStr) {
                case "未过期":
                    result = 1;
                    break;
                case "过期":
                    result = 0;
                    break;
                default:
            }
        }
        if (Objects.isNull(result)) {
            throw new ServiceException("有驾照，驾驶证有效期必填（未过期或过期），请检查Excel。");
        }
        return result;
    }

    /**
     * 证件名称
     *
     * @param idCardName
     * @return
     */
    private Integer transAuthType(String idCardName) {
        return TransUtil.transAuthType(idCardName);
    }
}

