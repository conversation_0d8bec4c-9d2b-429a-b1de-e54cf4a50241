package com.extracme.nevmp.readlistener;

import java.math.BigDecimal;
import java.util.Date;

import javax.validation.constraints.NotBlank;

import com.alibaba.excel.annotation.ExcelProperty;
import com.extracme.nevmp.utils.TransUtil;

import lombok.Data;

/**
 * <AUTHOR>
 * @Description
 * 由于该类部分字段需要进行转化，所以不建议采用lombock
 */
@Data
public class ImportOwnerDTO {

    @ExcelProperty(index = 0)
    private Integer index;
    /**
     * 车辆识别代码
     */
    @ExcelProperty(index = 1)
    private String vin;

    /**
     * 申请人姓名
     */
    @NotBlank(message = "申请人姓名为空")
    @ExcelProperty(index = 2)
    private String name;

    /**
     * 申请人性质（中文）
     */
    @NotBlank(message = "申请人性质为空")
    @ExcelProperty(index = 3)
    private String ownerTypeStr;


    /**
     * 证件类型（中文）
     */
    @NotBlank(message = "证件类型为空")
    @ExcelProperty(index = 4)
    private String authKindStr;

    /**
     * 证件号
     */
    @NotBlank(message = "证件号为空")
    @ExcelProperty(index = 5)
    private String authId;

    /**
     * 联系方式
     */
    @NotBlank(message = "联系方式为空")
    @ExcelProperty(index = 6)
    private String tel;

    /**
     * 汽车销售商
     */
    @NotBlank(message = "汽车销售商为空")
    @ExcelProperty(index = 7)
    private String vehSeller;

    /**
     * 车辆类型（中文）
     */
    @ExcelProperty(index = 8)
    private String ownerVehicleModelTypeStr;

    private Integer ownerVehicleModelType;

    /**
     * 充电桩类型
     */
    @ExcelProperty(index = 9)
    private String chargeDeviceTypeStr;

    private String chargeDeviceType;

    @NotBlank(message = "充电设备性质为空")
    @ExcelProperty(index = 10)
    private String chargeDevicePropertyStr;

    private Integer chargeDeviceProperty;

    @ExcelProperty(index = 11)
    private String chargeDeviceDistrictStr;


    private String chargeDeviceDistrict;

    /**
     * 充电设备安装街道
     */
    @ExcelProperty(index = 12)
    private String chargeDeviceStreet;


    @ExcelProperty(index = 13)
    private String chargeDeviceParkNo;

    /**
     * 充电桩编号
     */
    @ExcelProperty(index = 14)
    private String chargeSeq;

    /**
     * 充电桩建设运营单位名称
     */
    @ExcelProperty(index = 15)
    private String chargeOperatorName;

    /**
     * 投入运营时间
     */
    @ExcelProperty(index = 16)
    private Date chargeDeviceStartTime;

    /**
     * 经度
     */
    @ExcelProperty(index = 17)
    private BigDecimal longitude;

    /**
     * 纬度
     */
    @ExcelProperty(index = 18)
    private BigDecimal latitude;



    

    /**
     * 获取证件类型 数据库值
     * @return
     */
    public String getAuthKind() {
        return String.valueOf(TransUtil.transAuthType(authKindStr));
    }

    /**
     * 获取用户性质 数据库之
     * @return
     */
    public Integer getOwnerType() {
        return TransUtil.transOwnerType(ownerTypeStr);
    }


}
