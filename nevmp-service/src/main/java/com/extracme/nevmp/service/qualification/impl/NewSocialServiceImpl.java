package com.extracme.nevmp.service.qualification.impl;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TimeZone;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.extracme.nevmp.constant.ApiConst;
import com.extracme.nevmp.database.TableOwnerQualificationReviewDetailService;
import com.extracme.nevmp.database.TableOwnerResidencePermitPointQualificationService;
import com.extracme.nevmp.dto.gov.social.ResidencePermitBaseResponse;
import com.extracme.nevmp.dto.gov.social.ResidencePermitPointVO;
import com.extracme.nevmp.dto.gov.social.Social2025Data;
import com.extracme.nevmp.dto.gov.social.SocialApprove2025DTO;
import com.extracme.nevmp.dto.gov.social.SocialApproveDTO;
import com.extracme.nevmp.dto.gov.social.SocialBase2025Response;
import com.extracme.nevmp.dto.gov.social.SocialBaseRequest;
import com.extracme.nevmp.dto.gov.social.SocialBaseResponse;
import com.extracme.nevmp.dto.gov.social.SocialData;
import com.extracme.nevmp.dto.gov.social.SocialPayTaxCountResponse;
import com.extracme.nevmp.dto.gov.social.TaxationContinuousPaymentCompareQuery2024Response;
import com.extracme.nevmp.dto.gov.social.TaxationContinuousPaymentCompareQueryResponse;
import com.extracme.nevmp.dto.gov.social.TaxationData;
import com.extracme.nevmp.dto.gov.social.TaxationPayTaxCountResponse;
import com.extracme.nevmp.enums.AuthTypeEnum;
import com.extracme.nevmp.enums.OwnerQualificationReviewStatusEnum;
import com.extracme.nevmp.enums.OwnerQualificationReviewTypeEnum;
import com.extracme.nevmp.error.ServiceException;
import com.extracme.nevmp.model.OwnerQualificationReviewDetail;
import com.extracme.nevmp.model.OwnerResidencePermitPointQualification;
import com.extracme.nevmp.service.qualification.NewSocialService;
import com.extracme.nevmp.utils.DateUtil;
import com.extracme.nevmp.utils.JsonUtil;
import com.extracme.nevmp.utils.RestTemplateUtil;
import com.extracme.nevmp.utils.ShaUtil;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class NewSocialServiceImpl implements NewSocialService {

    @Autowired
    private TableOwnerQualificationReviewDetailService tableOwnerQualificationReviewDetailService;

    @Autowired
    private TableOwnerResidencePermitPointQualificationService tableOwnerResidencePermitPointQualificationService;


    @Override
    public void review(Long ownerQualificationId, String name, Integer authType, String authId) {
        //是否是企业
        boolean isBusiness = AuthTypeEnum.ORGANIZATION_CODE_CERTIFICATE.getType().equals(authType) || AuthTypeEnum.CERTIFICATE_FOR_UNIFORM_SOCIAL_CREDIT_CODE.getType().equals(authType);
        //是否是外籍
        boolean isForeigner = authType >= 6 && authType <= 12;
        //针对企业社保信息进行校验
        if(isBusiness){
            reviewBusinessSocial(ownerQualificationId, authId);
        }
        //针对外籍认识进行校验
        else if(isForeigner){
            reviewForeignerSocial(ownerQualificationId, name, authType, authId);
        }
        //针对非上海户口进行校验
        else{
            reviewNormalSocial(ownerQualificationId, name, authType ,authId);
        }
    }


    /**
     * 审核企业社保
     * @param ownerQualificationId 意向用户ID
     * @param authId 企业社会信用代码证
     */
    private void reviewBusinessSocial(Long ownerQualificationId, String authId){
        //查询上个月企业社保缴纳人数
        Calendar instance = Calendar.getInstance();
        instance.add(Calendar.MONTH, -1);
        SocialResponse response = reviewBusinessPayTaxCount(authId,DateUtil.format(instance.getTime(), DateUtil.DATE_TYPE6));
        //查询上上个月企业社保缴纳人数
        if(!OwnerQualificationReviewStatusEnum.APPROVE.getStatus().equals(response.getResult())){
            instance = Calendar.getInstance();
            instance.add(Calendar.MONTH, -2);
            response = reviewBusinessPayTaxCount(authId,DateUtil.format(instance.getTime(), DateUtil.DATE_TYPE6));
        }
        String reason = StringUtils.EMPTY;
        if(!OwnerQualificationReviewStatusEnum.APPROVE.getStatus().equals(response.getResult())){
            reason = "企业单位缴纳职工社会保险人数未超过5人（需有6人或6人以上，且需实际缴纳完成满3个工作日）";
        }
        log.warn("社保审核结果,{}:{}", authId, JSON.toJSONString(response));
        upsertOwnerQualificationReviewDetail(ownerQualificationId, response.getResult(), reason, response.getRemark());
    }


    /**
     * 审核外籍人士社保
     * @param ownerQualificationId 意向用户ID
     * @param name 姓名
     * @param authType 证件类型
     * @param authId 证件号
     */
    private void reviewForeignerSocial(Long ownerQualificationId, String name, Integer authType, String authId){
        //查询用户是否缴纳社保满6个月
        SocialResponse socialResponse = reviewSocial(name, authType, authId, 6);
        //如何审核不通过，则查询上个月开始前6个月是否连续缴纳社保
        if(OwnerQualificationReviewStatusEnum.DENY.getStatus().equals(socialResponse.getResult())){
            socialResponse = reviewSocialPreMonth(name, authType, authId, 6);
        }
        String reason = StringUtils.EMPTY;
        if(OwnerQualificationReviewStatusEnum.DENY.getStatus().equals(socialResponse.getResult())) {
            reason = "前6个月未连续缴纳社保（补缴不属于连续缴纳）";
        }
        //记录最终结果
        upsertOwnerQualificationReviewDetail(ownerQualificationId, socialResponse.getResult(), reason, socialResponse.getRemark());
    }


    /**
     * 查询社保信息（非上海户籍）
     * @param ownerQualificationId 意向用户ID
     * @param name 姓名
     * @param authType 证件类型
     * @param authId 证件号
     */
    private void reviewNormalSocial(Long ownerQualificationId, String name, Integer authType, String authId){
        //判断个人居住证积分是否满120分
        boolean hasResidencePoint = false;
        String remark = StringUtils.EMPTY;
        try{
            hasResidencePoint = has120ResidencePermitPoints(authId);
        }catch (Exception e){
            remark = e.getMessage();
            log.error("居住证积分数据处理异常:" + e.getMessage(), e);
        }finally {
            //记录居住证积分结果
            upsertOwnerResidencePermitPointQualification(ownerQualificationId, hasResidencePoint?1:0, remark);
        }

        //居住证积分已满
        if(hasResidencePoint){
            //查询用户是否缴纳社保满6个月
            SocialResponse socialResponse = reviewSocial(name, authType, authId, 6);
            //如何审核不通过，则查询上个月开始前6个月是否连续缴纳社保
            if(OwnerQualificationReviewStatusEnum.DENY.getStatus().equals(socialResponse.getResult())){
                socialResponse = reviewSocialPreMonth(name, authType, authId, 6);
            }
            //如何审核不通过，则查询前48个月是否累计缴纳36个月
            if(OwnerQualificationReviewStatusEnum.DENY.getStatus().equals(socialResponse.getResult())){
                socialResponse = reviewSocial2025(name, authType, authId, 36, 48);
            }
            //如何审核不通过，则查询上个月开始前48个月是否累计缴纳36个月
            if(OwnerQualificationReviewStatusEnum.DENY.getStatus().equals(socialResponse.getResult())) {
                socialResponse = reviewSocial2025PreMonth(name, authType, authId, 36, 48);
            }
            //如果经过4轮查询都未通过，则返回失败
            String reason = StringUtils.EMPTY;
            if(OwnerQualificationReviewStatusEnum.DENY.getStatus().equals(socialResponse.getResult())) {
                reason = "前6个月未连续缴纳社保（补缴不属于连续缴纳）并且前48个月未累计缴纳社保满36个月（补缴不计入累计）";
            }

            //记录最终结果
            upsertOwnerQualificationReviewDetail(ownerQualificationId, socialResponse.getResult(), reason, socialResponse.getRemark());
        }
        //居住证积分未满
        else{
            //由于2025年政策调整，所以需要根据用户申请时间，采用不同的逻辑判断
            String reason = StringUtils.EMPTY;
            //如果2025年之前，则维持原政策，需要连续缴纳36个月
            if(new Date().before(DateUtil.parse("2025-01-01 00:00:00", DateUtil.DATE_TYPE1))) {
                //查询用户是否缴纳社保满36个月
                SocialResponse socialResponse = reviewSocial(name, authType, authId, 36);
                //如果当月不满足条件
                if (OwnerQualificationReviewStatusEnum.DENY.getStatus().equals(socialResponse.getResult())) {
                    //则顺移往前查询一个月，避免部分用户当月缴费未录入的情况
                    socialResponse = reviewSocialPreMonth(name, authType, authId, 36);
                }
                if (OwnerQualificationReviewStatusEnum.DENY.getStatus().equals(socialResponse.getResult())) {
                    reason = "前36个月未连续缴纳社保（补缴不属于连续缴纳）";
                }

                //保存查询结果
                upsertOwnerQualificationReviewDetail(ownerQualificationId, socialResponse.getResult(), reason, socialResponse.getRemark());
            }else{
                //查询用户48个月内，是否累计满缴36个月
                SocialResponse socialResponse = reviewSocial2025(name, authType, authId, 36, 48);
                //如果当月不满足条件
                if (OwnerQualificationReviewStatusEnum.DENY.getStatus().equals(socialResponse.getResult())) {
                    //则顺移往前查询一个月，避免部分用户当月缴费未录入的情况
                    socialResponse = reviewSocial2025PreMonth(name, authType, authId, 36, 48);
                }
                if (OwnerQualificationReviewStatusEnum.DENY.getStatus().equals(socialResponse.getResult())) {
                    reason = "前48个月未累计缴纳社保满36个月（补缴不计入累计）；如您居住证刚达到标准分值，请在居住证积分审核通过3个工作日后，终止当前办件（可拨打咨询电话），重新发起购车资格查询";
                }
                upsertOwnerQualificationReviewDetail(ownerQualificationId, socialResponse.getResult(), reason, socialResponse.getRemark());
            }
        }
    }


    /**
     * 保存或更新居住证积分信息
     * @param ownerQualificationId 意向用户ID
     * @param result 1:居住证满120分 0:居住证不满120分
     * @param remark 异常原因
     */
    private void upsertOwnerResidencePermitPointQualification(Long ownerQualificationId, Integer result, String remark){
        OwnerResidencePermitPointQualification ownerResidencePermitPoint = tableOwnerResidencePermitPointQualificationService.getByOwnerQualificationId(ownerQualificationId);
        if(ownerResidencePermitPoint == null){
            OwnerResidencePermitPointQualification saveOwnerResidencePermitPointQualification = new OwnerResidencePermitPointQualification();
            saveOwnerResidencePermitPointQualification.setOwnerQualificationId(ownerQualificationId);
            saveOwnerResidencePermitPointQualification.setHasEnoughPoint(result);
            saveOwnerResidencePermitPointQualification.setRemark(remark);
            tableOwnerResidencePermitPointQualificationService.insert(saveOwnerResidencePermitPointQualification, "-1", "system");
        } else{
            OwnerResidencePermitPointQualification updateOwnerResidencePermitPointQualification = new OwnerResidencePermitPointQualification();
            updateOwnerResidencePermitPointQualification.setHasEnoughPoint(result);
            updateOwnerResidencePermitPointQualification.setRemark(remark);
            tableOwnerResidencePermitPointQualificationService.update(ownerResidencePermitPoint.getId(), updateOwnerResidencePermitPointQualification, "-1", "system");
        }
    }

    /**
     * 保存或更新社保审核明细
     * @param ownerQualificationId 意向用户ID
     * @param result 审核结果 {@link OwnerQualificationReviewStatusEnum}
     * @param reason 失败原因
     * @param remark 实际系统报错
     */
    private void upsertOwnerQualificationReviewDetail(Long ownerQualificationId, Integer result, String reason, String remark){
        OwnerQualificationReviewDetail socialReviewDetail = tableOwnerQualificationReviewDetailService.getByOwnerQualificationId(ownerQualificationId, OwnerQualificationReviewTypeEnum.SOCIAL);
        //审核结果信息不存在，新增审核记录
        if(socialReviewDetail == null){
            OwnerQualificationReviewDetail saveOwnerQualificationReviewDetail = new OwnerQualificationReviewDetail();
            saveOwnerQualificationReviewDetail.setOwnerQulificationId(ownerQualificationId);
            saveOwnerQualificationReviewDetail.setReviewType(OwnerQualificationReviewTypeEnum.SOCIAL.getType());
            saveOwnerQualificationReviewDetail.setReviewStatus(result);
            saveOwnerQualificationReviewDetail.setReason(reason);
            saveOwnerQualificationReviewDetail.setRemark(remark);
            tableOwnerQualificationReviewDetailService.insert(saveOwnerQualificationReviewDetail, "-1", "system");
        }
        //审核结果信息已存在，若请求成功，则更新审核结果
        else if(!OwnerQualificationReviewStatusEnum.PENDING.getStatus().equals(result)){
            OwnerQualificationReviewDetail updateOwnerQualificationReviewDetail = new OwnerQualificationReviewDetail();
            updateOwnerQualificationReviewDetail.setReviewStatus(result);
            updateOwnerQualificationReviewDetail.setReason(reason);
            updateOwnerQualificationReviewDetail.setRemark(remark);
            tableOwnerQualificationReviewDetailService.update(socialReviewDetail.getId(), updateOwnerQualificationReviewDetail, "-1", "system");
        }
    }


    /**
     * 查询社保信息
     * @param name 用户姓名
     * @param authType 证件类型
     * @param authId 证件号
     * @param month 连续缴纳月份
     * @return
     */
    @Override
    public SocialResponse reviewSocial(String name, Integer authType, String authId, Integer month){
        //当前日期月份减一
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, -1);
        int taxationMonth  = monthsUntilTargetMonth(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH) + 1, 2023, 12);
        if(taxationMonth > month){
            taxationMonth = month;
        }

        SocialResponse response = reviewSocialFromTaxation(name, authType, authId, taxationMonth, 0);
        log.info("查询税务个人连续缴纳月份：{},结果{}" , taxationMonth,  JSON.toJSONString(response));

        if(Objects.equals(response.getResult(), OwnerQualificationReviewStatusEnum.APPROVE.getStatus())){
            response = reviewSocialFromSocialAPI(name, authType, authId, month - taxationMonth);
            log.info("查询社保个人连续缴纳月份：{}, 结果:{}" ,  (month - taxationMonth), JSON.toJSONString(response));
        }
        return response;
    }



    @Override
    public SocialResponse reviewSocial2025(String name, Integer authType, String authId, int month, int totalMonth) {
        //当前日期月份减一
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, -1);
        //计算当前时间到2023年12月份经历的月数
        int taxationMonth  = monthsUntilTargetMonth(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH) + 1, 2023, 12);
        if(taxationMonth > totalMonth){
            //当前时间如果距离2023年超过48个月，则最多只查询48个月
            taxationMonth = totalMonth;
        }

        //查询税务累计缴纳月数
        SocialResponse taxationResponse = reviewSocialFromTaxation2025(name, authType, authId, taxationMonth, 0);
        //查询社保累计缴纳月数
        SocialResponse socialResponse = reviewSocialFromSocial2025(name, authType, authId, totalMonth - taxationMonth);
        //根据税务和社保结算最终结果
        return calculateSocialResponse(taxationResponse, socialResponse);
    }

    public SocialResponse reviewSocial2025PreMonth(String name, Integer authType, String authId, int month, int totalMonth) {
        //当前日期月份减二
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, -2);
        int taxationMonth  = monthsUntilTargetMonth(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH) + 1, 2023, 12);
        if(taxationMonth > month){
            taxationMonth = month;
        }

        //查询税务累计缴纳月数
        SocialResponse taxationResponse = reviewSocialFromTaxation2025(name, authType, authId, taxationMonth, 1);
        //查询社保累计缴纳月数
        SocialResponse socialResponse = reviewSocialFromSocial2025(name, authType, authId, totalMonth - taxationMonth);
        //根据税务和社保结算最终结果
        return calculateSocialResponse(taxationResponse, socialResponse);
    }

    /**
     * 计算统计最终社保结果
     * @param taxationResponse
     * @param socialResponse
     * @return
     */
    private SocialResponse calculateSocialResponse(SocialResponse taxationResponse, SocialResponse socialResponse){
        int socialMonth = 0 ;
        String remark = StringUtils.EMPTY;
        //如果有任何一个部门请求失败，则不进行审核，等待下次重审
        if (OwnerQualificationReviewStatusEnum.APPROVE.getStatus().equals(taxationResponse.getResult())) {
            socialMonth += taxationResponse.getSocialMonth();
            remark += "税务：" + taxationResponse.getRemark();
        }else{
            remark += taxationResponse.getRemark();
        }
        remark += "  ";
        if (OwnerQualificationReviewStatusEnum.APPROVE.getStatus().equals(socialResponse.getResult())) {
            socialMonth += socialResponse.getSocialMonth();
            remark += "社保：" + socialResponse.getRemark();
        }else{
            remark += socialResponse.getRemark();
        }

        //判断是否有审核异常的，则等下次重新审核
        if(OwnerQualificationReviewStatusEnum.PENDING.getStatus().equals(taxationResponse.getResult()) ||
                OwnerQualificationReviewStatusEnum.PENDING.getStatus().equals(socialResponse.getResult())){
            return new SocialResponse(OwnerQualificationReviewStatusEnum.PENDING.getStatus(), socialMonth, remark);
        }

        //判断是否满足条件
        if(socialMonth >= 36){
            return new SocialResponse(OwnerQualificationReviewStatusEnum.APPROVE.getStatus(), socialMonth, remark);
        }else{
            return new SocialResponse(OwnerQualificationReviewStatusEnum.DENY.getStatus(), socialMonth, remark);
        }
    }


    /**
     * 查询社保信息
     * @param name 用户姓名
     * @param authType 证件类型
     * @param authId 证件号
     * @param month 连续缴纳月份
     * @return
     */
    private SocialResponse reviewSocialPreMonth(String name, Integer authType, String authId, Integer month){
        //当前日期月份减二
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, -2);
        int taxationMonth  = monthsUntilTargetMonth(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH) + 1, 2023, 12);
        if(taxationMonth > month){
            taxationMonth = month;
        }

        SocialResponse response = reviewSocialFromTaxation(name, authType, authId, taxationMonth, 1);
        log.info("查询税务上月个人连续缴纳月份：{},结果{}" , taxationMonth,  JSON.toJSONString(response));

        if(Objects.equals(response.getResult(), OwnerQualificationReviewStatusEnum.APPROVE.getStatus())){
            response = reviewSocialFromSocialAPI(name, authType, authId, month - taxationMonth);
            log.info("查询社保上月个人连续缴纳月份：{}, 结果:{}" ,  (month - taxationMonth), JSON.toJSONString(response));
        }
        return response;
    }


    /**
     * 根据日期税务数据
     * @param name
     * @param authType
     * @param authId
     * @param dateStart
     * @param dateEnd
     * @return
     */
    public SocialResponse reviewSocialFromTaxation2025(String name, Integer authType, String authId, String dateStart, String dateEnd){
        String remark = StringUtils.EMPTY;
        Integer socialMonth = 0;
        Integer result = OwnerQualificationReviewStatusEnum.PENDING.getStatus();
        String taxationAuthType = transTaxationAuthType(authType);
        List<TaxationData> dataList = new ArrayList<>();

        if(StringUtils.isBlank(dateEnd)){
            //查询截至日期为上个月
            Calendar instanceEnd = Calendar.getInstance();
            instanceEnd.add(Calendar.MONTH, -1);
            dateEnd = DateUtil.format(instanceEnd.getTime(), DateUtil.DATE_TYPE6);
        }

        if(StringUtils.isBlank(dateStart)){
            Calendar instanceStart = Calendar.getInstance();
            instanceStart.setTime(DateUtil.parse(dateEnd, DateUtil.DATE_TYPE6));
            instanceStart.add(Calendar.MONTH, -5);
            dateStart = DateUtil.format(instanceStart.getTime(), DateUtil.DATE_TYPE6);
        }

        if(StringUtils.compare("202312", dateStart) > 0){
            dateStart = "202312";
        }

        if(StringUtils.compare("202312", dateEnd) > 0){
            return null;
        }

        try{
            Map<String, String> uriMap = new HashMap<>();
            uriMap.put("appKey", "98625141753466e54");
            uriMap.put("timestamp", String.valueOf(System.currentTimeMillis()));
            uriMap.put("signature", ShaUtil.shaEncode(uriMap.get("appKey") + "8HXTeWHk" + uriMap.get("timestamp")));
            uriMap.put("serviceCode", "4EmafmU7x8STY4MY");

            Map<String, String> header = new HashMap<>();
            header.put("Content-Type", "application/json");
            Map<String, String> params = new HashMap<>();
            //证件类型
            params.put("sfzjlx", taxationAuthType);
            //证件号码
            params.put("sfzjhm", authId);
            //姓名
            params.put("xm", name);
            //费款所属期起
            params.put("fkssqq", dateStart);
            //费款所属期止
            params.put("fkssqz", dateEnd);

            //请求税务查询个人连续交税接口
            String response = RestTemplateUtil.post(ApiConst.Social.TAXATION_CONTINUOUS_PAYMENT_COMPARE_QUERY, header, params, String.class, uriMap);
            System.out.println(JSON.toJSONString(response));

            TaxationContinuousPaymentCompareQueryResponse compareQueryResponse = JsonUtil.readStr(response, TaxationContinuousPaymentCompareQueryResponse.class);
            if (compareQueryResponse != null && compareQueryResponse.getSuccess()) {
                result = OwnerQualificationReviewStatusEnum.APPROVE.getStatus();
                socialMonth= getTaxationMont(compareQueryResponse.getValue().getDetailList());
                dataList = compareQueryResponse.getValue().getDetailList();
                remark = dateStart  + "至" + dateEnd + "期间累计缴纳" + socialMonth + "个月";
            }else{
                //如果是港澳台居住证，则试着尝试台湾居住证
                if(StringUtils.equals("237",taxationAuthType)){
                    params.put("sfzjlx", "238");
                }
                //二次查询税务接口
                response = RestTemplateUtil.post(ApiConst.Social.TAXATION_CONTINUOUS_PAYMENT_COMPARE_QUERY, header, params, String.class, uriMap);
                compareQueryResponse = JsonUtil.readStr(response, TaxationContinuousPaymentCompareQueryResponse.class);
                if (compareQueryResponse != null && compareQueryResponse.getSuccess()) {
                    result = OwnerQualificationReviewStatusEnum.APPROVE.getStatus();
                    socialMonth= getTaxationMont(compareQueryResponse.getValue().getDetailList());
                    dataList = compareQueryResponse.getValue().getDetailList();
                    remark = dateStart  + "至" + dateEnd + "期间累计缴纳" + socialMonth + "个月";
                }
                else{
                    //如果是外籍认识，则尝试其他证件类型
                    boolean isForeigner = authType >= 6 && authType <= 12;
                    if(isForeigner){
                        //299	其他个人证件
                        params.put("sfzjlx", "299");
                    }
                    //三次查询税务接口
                    response = RestTemplateUtil.post(ApiConst.Social.TAXATION_CONTINUOUS_PAYMENT_COMPARE_QUERY, header, params, String.class, uriMap);
                    compareQueryResponse = JsonUtil.readStr(response, TaxationContinuousPaymentCompareQueryResponse.class);
                    if (compareQueryResponse != null && compareQueryResponse.getSuccess()) {
                        result = OwnerQualificationReviewStatusEnum.APPROVE.getStatus();
                        socialMonth= getTaxationMont(compareQueryResponse.getValue().getDetailList());
                        dataList = compareQueryResponse.getValue().getDetailList();
                        remark = dateStart  + "至" + dateEnd + "期间连续缴纳" + socialMonth + "个月";
                    }else{
                        result = OwnerQualificationReviewStatusEnum.DENY.getStatus();
                        if(compareQueryResponse != null){
                            remark = "查询税务接口失败，失败原因:" + compareQueryResponse.getMessage() + " 开始时间:" + dateStart + " 结束时间:" + dateEnd
                                    + " 返回结果：" + response +" 请求参数：" + JSON.toJSONString(params);
                        }
                    }
                }
            }
        }catch (Exception e){
            log.error("税务局查询个人连续缴纳社保异常,{}", e.getMessage(), e);
            remark = "查询税务接口异常,异常信息:" + e.getMessage();
        }
        SocialResponse response = new SocialResponse(result, socialMonth, remark);
        if(!dataList.isEmpty()){
            dataList = dealTaxationData(dataList);
        }
        response.setDataList(dataList);
        return response;
    }

    /**
     *
     * @param name
     * @param authType
     * @param authId
     * @param month
     * @param preMonth
     * @return
     */
    private SocialResponse reviewSocialFromTaxation2025(String name, Integer authType, String authId, Integer month, Integer preMonth){
        String remark = StringUtils.EMPTY;
        Integer socialMonth = 0;
        Integer result = OwnerQualificationReviewStatusEnum.PENDING.getStatus();
        String taxationAuthType = transTaxationAuthType(authType);

        //查询截至日期为上个月
        Calendar instanceEnd = Calendar.getInstance();
        instanceEnd.add(Calendar.MONTH, -1 - preMonth);
        String dateEnd = DateUtil.format(instanceEnd.getTime(), DateUtil.DATE_TYPE6);

        Calendar instanceStart = Calendar.getInstance();
        instanceStart.add(Calendar.MONTH, -month - preMonth);
        String dateStart = DateUtil.format(instanceStart.getTime(), DateUtil.DATE_TYPE6);

        if(StringUtils.isBlank(taxationAuthType)){
            remark = "证件类型异常";
            return new SocialResponse(result, remark);
        }

        try{
            Map<String, String> uriMap = new HashMap<>();
            uriMap.put("appKey", "98625141753466e54");
            uriMap.put("timestamp", String.valueOf(System.currentTimeMillis()));
            uriMap.put("signature", ShaUtil.shaEncode(uriMap.get("appKey") + "8HXTeWHk" + uriMap.get("timestamp")));
            uriMap.put("serviceCode", "4EmafmU7x8STY4MY");

            Map<String, String> header = new HashMap<>();
            header.put("Content-Type", "application/json");
            Map<String, String> params = new HashMap<>();
            //证件类型
            params.put("sfzjlx", taxationAuthType);
            //证件号码
            params.put("sfzjhm", authId);
            //姓名
            params.put("xm", name);
            //费款所属期起
            params.put("fkssqq", dateStart);
            //费款所属期止
            params.put("fkssqz", dateEnd);


            //请求税务查询个人连续交税接口
            String response = RestTemplateUtil.post(ApiConst.Social.TAXATION_CONTINUOUS_PAYMENT_COMPARE_QUERY, header, params, String.class, uriMap);
            TaxationContinuousPaymentCompareQueryResponse compareQueryResponse = JsonUtil.readStr(response, TaxationContinuousPaymentCompareQueryResponse.class);
            if (compareQueryResponse != null && compareQueryResponse.getSuccess()) {
                result = OwnerQualificationReviewStatusEnum.APPROVE.getStatus();
                socialMonth= getTaxationMont(compareQueryResponse.getValue().getDetailList());
                remark = dateStart  + "至" + dateEnd + "期间累计缴纳" + socialMonth + "个月";
            }else{
                //如果是港澳台居住证，则试着尝试台湾居住证
                if(StringUtils.equals("237",taxationAuthType)){
                    params.put("sfzjlx", "238");
                }
                //二次查询税务接口
                response = RestTemplateUtil.post(ApiConst.Social.TAXATION_CONTINUOUS_PAYMENT_COMPARE_QUERY, header, params, String.class, uriMap);
                compareQueryResponse = JsonUtil.readStr(response, TaxationContinuousPaymentCompareQueryResponse.class);
                if (compareQueryResponse != null && compareQueryResponse.getSuccess()) {
                    result = OwnerQualificationReviewStatusEnum.APPROVE.getStatus();
                    socialMonth= getTaxationMont(compareQueryResponse.getValue().getDetailList());
                    remark = dateStart  + "至" + dateEnd + "期间累计缴纳" + socialMonth + "个月";
                }
                else{
                    //如果是外籍认识，则尝试其他证件类型
                    boolean isForeigner = authType >= 6 && authType <= 12;
                    if(isForeigner){
                        //299	其他个人证件
                        params.put("sfzjlx", "299");
                    }
                    //三次查询税务接口
                    response = RestTemplateUtil.post(ApiConst.Social.TAXATION_CONTINUOUS_PAYMENT_COMPARE_QUERY, header, params, String.class, uriMap);
                    compareQueryResponse = JsonUtil.readStr(response, TaxationContinuousPaymentCompareQueryResponse.class);
                    if (compareQueryResponse != null && compareQueryResponse.getSuccess()) {
                        result = OwnerQualificationReviewStatusEnum.APPROVE.getStatus();
                        socialMonth= getTaxationMont(compareQueryResponse.getValue().getDetailList());
                        remark = dateStart  + "至" + dateEnd + "期间累计缴纳" + socialMonth + "个月";
                    }else{
                        //判断接口是否是超时
                        if(compareQueryResponse != null && compareQueryResponse.getValue() == null && "00999001".equals(compareQueryResponse.getMsgCode())){
                            throw new ServiceException("请求税务接口超时");
                        }


                        result = OwnerQualificationReviewStatusEnum.DENY.getStatus();
                        if(compareQueryResponse != null){
                            remark = "查询税务接口失败，失败原因:" + compareQueryResponse.getMessage() + " 开始时间:" + dateStart + " 结束时间:" + dateEnd
                                    + " 返回结果：" + response +" 请求参数：" + JSON.toJSONString(params);
                        }
                    }
                }
            }
        }catch (Exception e){
            log.error("税务局查询个人连续缴纳社保异常,{}", e.getMessage(), e);
            remark = "查询税务接口异常,异常信息:" + e.getMessage();
        }
        return new SocialResponse(result, socialMonth, remark);
    }




    /**
     * 查询税务个人连续缴纳社保信息
     * @param name 用户姓名
     * @param authType 证件类型
     * @param authId 证件号
     * @param month 连续缴纳月份
     * @return
     */
    private SocialResponse reviewSocialFromTaxation(String name, Integer authType, String authId, Integer month, Integer preMonth){
        String remark = StringUtils.EMPTY;
        Integer result = OwnerQualificationReviewStatusEnum.PENDING.getStatus();
        String taxationAuthType = transTaxationAuthType(authType);

        //查询截至日期为上个月
        Calendar instanceEnd = Calendar.getInstance();
        instanceEnd.add(Calendar.MONTH, -1 - preMonth);
        String dateEnd = DateUtil.format(instanceEnd.getTime(), DateUtil.DATE_TYPE6);

        Calendar instanceStart = Calendar.getInstance();
        instanceStart.add(Calendar.MONTH, -month - preMonth);
        String dateStart = DateUtil.format(instanceStart.getTime(), DateUtil.DATE_TYPE6);

        if(StringUtils.isBlank(taxationAuthType)){
            remark = "证件类型异常";
            return new SocialResponse(result, remark);
        }

        try{
            Map<String, String> uriMap = new HashMap<>();
            uriMap.put("appKey", "98625141753466e54");
            uriMap.put("timestamp", String.valueOf(System.currentTimeMillis()));
            uriMap.put("signature", ShaUtil.shaEncode(uriMap.get("appKey") + "8HXTeWHk" + uriMap.get("timestamp")));
            uriMap.put("serviceCode", "4EmafmU7x8STY4MY");

            Map<String, String> header = new HashMap<>();
            header.put("Content-Type", "application/json");
            Map<String, String> params = new HashMap<>();
            //证件类型
            params.put("sfzjlx", taxationAuthType);
            //证件号码
            params.put("sfzjhm", authId);
            //姓名
            params.put("xm", name);
            //费款所属期起
            params.put("fkssqq", dateStart);
            //费款所属期止
            params.put("fkssqz", dateEnd);


            //请求税务查询个人连续交税接口
            String response = RestTemplateUtil.post(ApiConst.Social.TAXATION_CONTINUOUS_PAYMENT_COMPARE_QUERY, header, params, String.class, uriMap);

            //由于接口格式变动（接口地址本身没变），需要兼容2024年及2025年税务接口格式
            if(JSON.parseObject(response).get("value") instanceof String){
                //解析2024年的接口格式
                TaxationContinuousPaymentCompareQuery2024Response compareQuery2024Response = JsonUtil.readStr(response, TaxationContinuousPaymentCompareQuery2024Response.class);
                if(compareQuery2024Response != null){
                    if (compareQuery2024Response.getSuccess() &&  "Y".equals(compareQuery2024Response.getValue())) {
                        result = OwnerQualificationReviewStatusEnum.APPROVE.getStatus();
                    }else{
                        //如果是港澳台居住证，则试着尝试台湾居住证
                        if(StringUtils.equals("237",taxationAuthType)){
                            params.put("sfzjlx", "238");
                        }
                        //二次查询税务接口
                        response = RestTemplateUtil.post(ApiConst.Social.TAXATION_CONTINUOUS_PAYMENT_COMPARE_QUERY, header, params, String.class, uriMap);
                        compareQuery2024Response = JsonUtil.readStr(response, TaxationContinuousPaymentCompareQuery2024Response.class);
                        if (compareQuery2024Response != null && compareQuery2024Response.getSuccess() && "Y".equals(compareQuery2024Response.getValue())) {
                            result = OwnerQualificationReviewStatusEnum.APPROVE.getStatus();
                        }
                        else{
                            //如果是外籍认识，则尝试其他证件类型
                            boolean isForeigner = authType >= 6 && authType <= 12;
                            if(isForeigner){
                                //299	其他个人证件
                                params.put("sfzjlx", "299");
                            }
                            //三次查询税务接口
                            response = RestTemplateUtil.post(ApiConst.Social.TAXATION_CONTINUOUS_PAYMENT_COMPARE_QUERY, header, params, String.class, uriMap);
                            compareQuery2024Response = JsonUtil.readStr(response, TaxationContinuousPaymentCompareQuery2024Response.class);
                            if (compareQuery2024Response != null && compareQuery2024Response.getSuccess() && "Y".equals(compareQuery2024Response.getValue())) {
                                result = OwnerQualificationReviewStatusEnum.APPROVE.getStatus();
                            }else{
                                result = OwnerQualificationReviewStatusEnum.DENY.getStatus();
                                if(compareQuery2024Response != null){
                                    remark = "查询税务接口失败，失败原因:" + compareQuery2024Response.getMessage() + " 开始时间:" + dateStart + " 结束时间:" + dateEnd
                                            + " 返回结果：" + response;
                                }
                            }
                        }
                    }
                }
            }else{
                //解析2025年的接口格式
                TaxationContinuousPaymentCompareQueryResponse compareQueryResponse = JsonUtil.readStr(response, TaxationContinuousPaymentCompareQueryResponse.class);
                if(compareQueryResponse != null){
                    if (compareQueryResponse.getSuccess() &&  "Y".equals(compareQueryResponse.getValue().getSflxjn())) {
                        result = OwnerQualificationReviewStatusEnum.APPROVE.getStatus();
                    }else{
                        //如果是港澳台居住证，则试着尝试台湾居住证
                        if(StringUtils.equals("237",taxationAuthType)){
                            params.put("sfzjlx", "238");
                        }
                        //二次查询税务接口
                        response = RestTemplateUtil.post(ApiConst.Social.TAXATION_CONTINUOUS_PAYMENT_COMPARE_QUERY, header, params, String.class, uriMap);
                        compareQueryResponse = JsonUtil.readStr(response, TaxationContinuousPaymentCompareQueryResponse.class);
                        if (compareQueryResponse != null && compareQueryResponse.getSuccess() && "Y".equals(compareQueryResponse.getValue().getSflxjn())) {
                            result = OwnerQualificationReviewStatusEnum.APPROVE.getStatus();
                        }
                        else{
                            //如果是外籍认识，则尝试其他证件类型
                            boolean isForeigner = authType >= 6 && authType <= 12;
                            if(isForeigner){
                                //299	其他个人证件
                                params.put("sfzjlx", "299");
                            }
                            //三次查询税务接口
                            response = RestTemplateUtil.post(ApiConst.Social.TAXATION_CONTINUOUS_PAYMENT_COMPARE_QUERY, header, params, String.class, uriMap);
                            compareQueryResponse = JsonUtil.readStr(response, TaxationContinuousPaymentCompareQueryResponse.class);
                            if (compareQueryResponse != null && compareQueryResponse.getSuccess() && "Y".equals(compareQueryResponse.getValue().getSflxjn())) {
                                result = OwnerQualificationReviewStatusEnum.APPROVE.getStatus();
                            }else{
                                result = OwnerQualificationReviewStatusEnum.DENY.getStatus();
                                if(compareQueryResponse != null){
                                    remark = "查询税务接口失败，失败原因:" + compareQueryResponse.getMessage() + " 开始时间:" + dateStart + " 结束时间:" + dateEnd
                                            + " 返回结果：" + response;
                                }
                            }
                        }
                    }
                }
            }
        }catch (Exception e){
            log.error("税务局查询个人连续缴纳社保异常,{}", e.getMessage(), e);
            remark = "查询税务接口异常,异常信息:" + e.getMessage();
        }
        return new SocialResponse(result, remark);
    }

    private Integer getTaxationMont(List<TaxationData> taxationDataList){
        List<TaxationData> taxationData = dealTaxationData(taxationDataList);
        //统计未补缴的记录数
        return taxationData.stream().filter(data -> !data.isSupplement()).collect(Collectors.toList()).size();
    }

    /**
     * 处理税务数据，标注那些数据是补缴的
     * @param taxationDataList 原始缴纳数据
     * @return 处理后的税务数据
     */
    private List<TaxationData> dealTaxationData(List<TaxationData> taxationDataList){
        if(taxationDataList == null || taxationDataList.isEmpty()){
            return new ArrayList<>();
        }

        for (TaxationData taxationData : taxationDataList) {
            String fkssqq = taxationData.getFkssqq();
            Date startDate = DateUtil.parse(fkssqq, DateUtil.DATE_TYPE6);
            LocalDate startLocalDate = startDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();


            String sjsj = taxationData.getSjsj();
            Date payDate = DateUtil.parse(sjsj, DateUtil.DATE_TYPE3);
            LocalDate payLocalDate = payDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

            //判断实缴日期是否超过社保日期一个月
            int monthsDifference = (payLocalDate.getYear() * 12 + payLocalDate.getMonthValue())
                    -  (startLocalDate.getYear() * 12 + startLocalDate.getMonthValue());
            taxationData.setSupplement(monthsDifference  > 1);
        }
        return taxationDataList;
    }


    /**
     * 转换税务对应证件类型
     * 227	中国护照
     * 228	城镇退役士兵自谋职业证
     * 101	组织机构代码证
     * 199	其他单位证件
     * 201	居民身份证
     * 202	军官证
     * 203	武警警官证
     * 204	士兵证
     * 205	军队离退休干部证
     * 206	残疾人证
     * 207	残疾军人证（1-8级）
     * 208	外国护照
     * 210	港澳居民来往内地通行证
     * 212	中华人民共和国往来港澳通行证
     * 213	台湾居民来往大陆通行证
     * 214	大陆居民往来台湾通行证
     * 215	外国人居留证
     * 216	外交官证
     * 217	使（领事）馆证
     * 218	海员证
     * 219	香港永久性居民身份证
     * 220	台湾身份证
     * 221	澳门特别行政区永久性居民身份证
     * 222	外国人身份证件
     * 224	就业失业登记证
     * 225	退休证
     * 226	离休证
     * 299	其他个人证件
     * 233	外国人永久居留身份证（外国人永久居留证）
     * 234	就业创业证
     * 291	出生医学证明
     * 102	营业执照
     * 229	随军家属身份证明
     * 230	中国人民解放军军官转业证书
     * 231	中国人民解放军义务兵退出现役证
     * 232	中国人民解放军士官退出现役证
     * 239	《中华人民共和国外国人工作许可证》（A类）
     * 240	《中华人民共和国外国人工作许可证》（B类）
     * 241	《中华人民共和国外国人工作许可证》（C类）
     * 103	税务登记证
     * 237	中华人民共和国港澳居民居住证
     * 238	中华人民共和国台湾居民居住证
     * 235	香港特别行政区护照
     * 236	澳门特别行政区护照
     * @param authType 证件类型
     * @return
     */
    private String transTaxationAuthType(Integer authType){
        switch (authType){
            //身份证
            case 1:
                return "201";
            //港澳居民来往内地通行证
            case 6:
                return "210";
            //台湾居民来往大陆通行证
            case 7:
                return "213";
            //中国护照（华侨）
            case 8:
                return "227";
            //外国人护照
            case 9:
                return "208";
            //17版外国人永久居留身份证
            case 10:
                return "233";
            //港澳台居民居住证
            case 11:
                return "237";
            //23版本外国人永久居留身份证
            case 12:
                return "233";
        }
        return StringUtils.EMPTY;
    }


    /**
     * 转换社保对应证件类型
     * @param authType 证件类型
     * @return 转换后证件类型
     */
    private String transSocialAuthType(Integer authType){
        switch (authType){
            //身份证
            case 1:
                return "01";
            //港澳居民来往内地通行证
            case 6:
                return "04";
            //台湾居民来往大陆通行证
            case 7:
                return "06";
            //中国护照（华侨）
            case 8:
                return "08";
            //外国人护照
            case 9:
                return "08";
            //17版外国人永久居留身份证
            case 10:
                return "07";
            //港澳台居民居住证
            case 11:
                return "18";
            //23版本外国人永久居留身份证
            case 12:
                return "07";
        }
        return "99";
    }

    @Override
    public SocialResponse reviewSocialFromSocialAPI2025(String name, Integer authType, String authId, String dateStart, String dateEnd){
        if(StringUtils.isBlank(dateStart) && StringUtils.isBlank(dateEnd)){
            return null;
        }
        if(StringUtils.isBlank(dateEnd)){
            dateEnd = "202311";
        }

        if(StringUtils.compare(dateEnd, "202311") > 0){
            dateEnd = "202311";
        }

        if(StringUtils.compare(dateStart, dateEnd) > 0){
            return null;
        }

        String remark = StringUtils.EMPTY;
        try{
            SocialApprove2025DTO socialApproveDTO = new SocialApprove2025DTO();
            socialApproveDTO.setAae003(name);
            socialApproveDTO.setAae030(dateStart);
            socialApproveDTO.setAae031("202311");
            socialApproveDTO.setAac058(transSocialAuthType(authType));
            socialApproveDTO.setAac147(authId);
            SocialBase2025Response response = reviewSocial2025(socialApproveDTO);
            if (!response.getIsSuccess()) {
                //审核拒绝
                return new SocialResponse(OwnerQualificationReviewStatusEnum.DENY.getStatus(), "接口请求失败："+ JSON.toJSONString(response));
            }

            Social2025Data socialData = response.getData();
            if (socialData == null) {
                //返回格式异常
                return new SocialResponse(OwnerQualificationReviewStatusEnum.PENDING.getStatus(), "查询社保接口异常:" + JSON.toJSONString(response));
            }
            if(StringUtils.equals("否", socialData.getAae100())){
                remark = "查询社保接口失败 开始时间:" + dateStart + " 结束时间:202311 请求参数:" + JSON.toJSONString(socialApproveDTO);
                return new SocialResponse(OwnerQualificationReviewStatusEnum.DENY.getStatus(), remark);
            }
            //返回累计缴纳月份
            remark = dateStart  + "至" + 202311 + "期间累计缴纳" + socialData.getAae202() + "个月";
            return new SocialResponse(OwnerQualificationReviewStatusEnum.APPROVE.getStatus(), Integer.valueOf(socialData.getAae202()), remark);
        }catch (Exception e){
            //适合结果异常，等到下次轮询再次调用接口
            log.error("社保数据处理异常:" + e.getMessage(), e);
            remark = "查询社保接口异常,异常信息:" + e.getMessage();
            return new SocialResponse(OwnerQualificationReviewStatusEnum.PENDING.getStatus(), remark);
        }
    }

    private SocialResponse reviewSocialFromSocial2025(String name, Integer authType, String authId, Integer month){
        if(month <= 0){
            return new SocialResponse(OwnerQualificationReviewStatusEnum.APPROVE.getStatus());
        }

        String remark = StringUtils.EMPTY;
        try{
            SocialApprove2025DTO socialApproveDTO = new SocialApprove2025DTO();
            socialApproveDTO.setAae003(name);
            Calendar instance = Calendar.getInstance(TimeZone.getTimeZone("Asia/Shanghai"));
            //从2023年11月开始算起-往前倒推开始月份
            instance.set(Calendar.YEAR, 2023);
            instance.set(Calendar.MONTH, Calendar.NOVEMBER);
            instance.set(Calendar.DATE, 1);
            instance.add(Calendar.MONTH, -month +1);
            String dateStart = DateUtil.format(instance.getTime(), DateUtil.DATE_TYPE6);
            socialApproveDTO.setAae030(dateStart);

            socialApproveDTO.setAae031("202311");
            socialApproveDTO.setAac058(transSocialAuthType(authType));
            socialApproveDTO.setAac147(authId);
            SocialBase2025Response response = reviewSocial2025(socialApproveDTO);
            if (!response.getIsSuccess()) {
                //审核拒绝
                return new SocialResponse(OwnerQualificationReviewStatusEnum.DENY.getStatus(), "接口请求失败："+ JSON.toJSONString(response));
            }

            Social2025Data socialData = response.getData();
            if (socialData == null) {
                //返回格式异常
                return new SocialResponse(OwnerQualificationReviewStatusEnum.PENDING.getStatus(), "查询社保接口异常:" + JSON.toJSONString(response));
            }
            if(StringUtils.equals("否", socialData.getAae100())){
                remark = "查询社保接口失败 开始时间:" + dateStart + " 结束时间:202311 请求参数:" + JSON.toJSONString(socialApproveDTO);
                return new SocialResponse(OwnerQualificationReviewStatusEnum.DENY.getStatus(), remark);
            }
            //返回累计缴纳月份
            remark = dateStart  + "至" + 202311 + "期间累计缴纳" + socialData.getAae202() + "个月";
            return new SocialResponse(OwnerQualificationReviewStatusEnum.APPROVE.getStatus(), Integer.valueOf(socialData.getAae202()), remark);
        }catch (Exception e){
            //适合结果异常，等到下次轮询再次调用接口
            log.error("社保数据处理异常:" + e.getMessage(), e);
            remark = "查询社保接口异常,异常信息:" + e.getMessage();
            return new SocialResponse(OwnerQualificationReviewStatusEnum.PENDING.getStatus(), remark);
        }
    }



    @Override
    public SocialResponse reviewSocialFromSocialAPI(String name, Integer authType, String authId, Integer month){
        if(month <= 0){
            return new SocialResponse(OwnerQualificationReviewStatusEnum.APPROVE.getStatus());
        }

        String remark;

        try{
            SocialApproveDTO socialApproveDTO = new SocialApproveDTO();
            socialApproveDTO.setXm(name);
            Calendar instance = Calendar.getInstance(TimeZone.getTimeZone("Asia/Shanghai"));
            //从2023年11月开始算起
            instance.set(Calendar.YEAR, 2023);
            instance.set(Calendar.MONTH, Calendar.NOVEMBER);
            instance.set(Calendar.DATE, 1);

            instance.add(Calendar.MONTH, -month +1);

            String yyyyMM = DateUtil.format(instance.getTime(), DateUtil.DATE_TYPE6);
            socialApproveDTO.setQsny(yyyyMM);
            socialApproveDTO.setZzny("202311");
            socialApproveDTO.setBdlxys(month.toString());
            socialApproveDTO.setZjlx(transSocialAuthType(authType));
            socialApproveDTO.setZjhm(authId);


            System.out.println(JSON.toJSONString(socialApproveDTO));

            SocialBaseResponse response = reviewSocial(socialApproveDTO);
            if (response.getIsSuccess().equals(true)) {
                SocialData socialData = response.getData();
                if (socialData != null && socialData.getLxBdjg() != null) {
                    switch (socialData.getLxBdjg()) {
                        case "是":
                            //审核通过
                            return new SocialResponse(OwnerQualificationReviewStatusEnum.APPROVE.getStatus());
                        case "否":
                        case "无":
                            remark = "查询社保接口失败 开始时间:" + yyyyMM + " 结束时间:202311 请求参数:" + JSON.toJSONString(socialApproveDTO);
                            //审核拒绝
                            return new SocialResponse(OwnerQualificationReviewStatusEnum.DENY.getStatus(), remark);
                        default:
                            //适合结果异常，等到下次轮询再次调用接口
                            return new SocialResponse(OwnerQualificationReviewStatusEnum.PENDING.getStatus(), "查询社保接口异常:" + socialData.getLxBdjg());
                    }
                }else{
                    //返回格式异常
                    return new SocialResponse(OwnerQualificationReviewStatusEnum.PENDING.getStatus(), "查询社保接口异常:" +JSON.toJSONString(response));
                }
            }else{
                //审核拒绝
                return new SocialResponse(OwnerQualificationReviewStatusEnum.DENY.getStatus());
            }
        }catch (Exception e){
            //适合结果异常，等到下次轮询再次调用接口
            log.error("社保数据处理异常:" + e.getMessage(), e);
            remark = "查询社保接口异常,异常信息:" + e.getMessage();
            return new SocialResponse(OwnerQualificationReviewStatusEnum.PENDING.getStatus(), remark);
        }
    }


    /**
     * 计算当前时间距离目标月份的月份数
     * @param sourceYear 当前时间的年份
     * @param sourceMonth 当前时间的月份
     * @param targetYear 目标时间的年份
     * @param targetMonth 目标时间的月份
     * @return 当前时间距离目标月份的月份数
     */
    public static int monthsUntilTargetMonth(int sourceYear, int sourceMonth, int targetYear, int targetMonth) {
        return (sourceMonth + (sourceYear * 12)) - (targetMonth + (targetYear * 12)) + 1;
    }




    /**
     * 审核社保信息
     * @param approveDto 社保审核人信息
     * @return 社保审核结果
     */
    private SocialBaseResponse reviewSocial(SocialApproveDTO approveDto) {
        SocialBaseRequest<SocialApproveDTO> request = new SocialBaseRequest<>();
        request.setType("lxjfbd");
        request.setData(approveDto);
        String response = RestTemplateUtil.post(ApiConst.Social.CONTINUOUS_PAYMENT_COMPARE_QUERY, request, String.class);
        return JsonUtil.readStr(response, SocialBaseResponse.class);
    }


    private SocialBase2025Response reviewSocial2025(SocialApprove2025DTO approveDto){
        SocialBaseRequest<SocialApprove2025DTO> request = new SocialBaseRequest<>();
        request.setType("xnyljjfys");
        request.setData(approveDto);
        String response = RestTemplateUtil.post(ApiConst.Social.CONTINUOUS_PAYMENT_COMPARE_QUERY, request, String.class);
        return JsonUtil.readStr(response, SocialBase2025Response.class);

    }




    /**
     * 校验企业纳税人数
     * @param authId 企业社会信用代码
     * @param date 日期
     * @return true:大于5人 false:企业信息不存在或小于等于5人
     */
    public SocialResponse reviewBusinessPayTaxCount(String authId, String date){
        //根据请求日期分别查询税务、社保接口
        if(date.compareTo("202311") <= 0){
            return reviewSocialBusinessPayTaxCount(authId, date);
        }else{
            //查询税务方面 企业缴纳社保人数
            return reviewTaxationBusinessPayTaxCount(authId, date);
        }
    }

    /**
     * 校验税务局企业纳税人数
     * @param authId 企业社会信用代码
     * @param date 日期  不得小于 202311
     * @return true:大于5人 false:企业信息不存在或小于等于5人
     */
    public SocialResponse reviewTaxationBusinessPayTaxCount(String authId, String date){
        String remark = StringUtils.EMPTY;
        Integer result = OwnerQualificationReviewStatusEnum.PENDING.getStatus();
        try{
            Map<String, String> uriMap = new HashMap<>();
            uriMap.put("appKey", "98625141753466e54");
            uriMap.put("timestamp", String.valueOf(System.currentTimeMillis()));
            uriMap.put("signature", ShaUtil.shaEncode(uriMap.get("appKey") + "8HXTeWHk" + uriMap.get("timestamp")));
            uriMap.put("serviceCode", "4EmafmU7x8STY4MY");

            Map<String, String> header = new HashMap<>();
            header.put("Content-Type", "application/json");

            Map<String, String> params = new HashMap<>();
            params.put("shxydm", authId);
            params.put("fkssq", date);
            String response = RestTemplateUtil.post(ApiConst.Social.TAXATION_PAY_TAX_COUNT, header, params, String.class, uriMap);
            System.out.println(response);
            TaxationPayTaxCountResponse taxationPayTaxCountResponse = JsonUtil.readStr(response, TaxationPayTaxCountResponse.class);
            if (taxationPayTaxCountResponse != null && taxationPayTaxCountResponse.getSuccess() && "Y".equals(taxationPayTaxCountResponse.getValue())) {
                result = OwnerQualificationReviewStatusEnum.APPROVE.getStatus();
            }else{
                result = OwnerQualificationReviewStatusEnum.DENY.getStatus();
                if(taxationPayTaxCountResponse != null){
                    remark = taxationPayTaxCountResponse.getMessage();
                }
            }
        }catch (Exception e){
            log.error("税务局查询企业纳税人数异常,{}", e.getMessage(), e);
            remark = e.getMessage();
        }
        return new SocialResponse(result, remark);
    }

    /**
     * 校验社保企业纳税人数
     * @param authId 企业社会信用代码
     * @param date 日期  不得大于 202311
     * @return true:大于5人 false:企业信息不存在或小于等于5人
     */
    public SocialResponse reviewSocialBusinessPayTaxCount(String authId, String date){
        String remark = StringUtils.EMPTY;
        Integer result = OwnerQualificationReviewStatusEnum.PENDING.getStatus();
        try{
            Map<String, String> uriMap = new HashMap<>();
            uriMap.put("appKey", "98625141753466e54");
            uriMap.put("timestamp", String.valueOf(System.currentTimeMillis()));
            uriMap.put("signature", ShaUtil.shaEncode(uriMap.get("appKey") + "8HXTeWHk" + uriMap.get("timestamp")));
            uriMap.put("serviceCode", "4EmafmU7x8STY4MY");

            Map<String, String> header = new HashMap<>();
            header.put("Content-Type", "application/json");

            Map<String, String> params = new HashMap<>();
            params.put("tyshxydm", authId);
            params.put("ny", date);

            String response = RestTemplateUtil.post(ApiConst.Social.SOCIAL_PAY_TAX_COUNT, header, params, String.class, uriMap);
            SocialPayTaxCountResponse socialPayTaxCountResponse = JsonUtil.readStr(response, SocialPayTaxCountResponse.class);
            if (socialPayTaxCountResponse != null && "200".equals(socialPayTaxCountResponse.getCode())) {
                //判断人数是否大于5人
                result = Integer.parseInt(socialPayTaxCountResponse.getRs()) > 5 ? OwnerQualificationReviewStatusEnum.APPROVE.getStatus():OwnerQualificationReviewStatusEnum.DENY.getStatus();
            }else{
                result = OwnerQualificationReviewStatusEnum.DENY.getStatus();
                if(socialPayTaxCountResponse != null){
                    remark = socialPayTaxCountResponse.getMsg();
                }
            }
        }catch (Exception e){
            log.error("社保查询企业纳税人数异常,{}", e.getMessage(), e);
            remark = e.getMessage();
        }
        return new SocialResponse(result, remark);
    }


    /**
     * 查询用户是否居住证积分满120分
     * @param authId 证件号
     * @return true:满120分 false:不满120分
     */
    public boolean has120ResidencePermitPoints(String authId) {
        Map<String, String> uriMap = new HashMap<>();
        uriMap.put("appKey", "98625141753466e54");
        uriMap.put("timestamp", String.valueOf(System.currentTimeMillis()));
        uriMap.put("signature", ShaUtil.shaEncode(uriMap.get("appKey") + "8HXTeWHk" + uriMap.get("timestamp")));
        uriMap.put("serviceCode", "pEmo1EeLgwzqPnlF");
        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json");
        Map<String, String> params = new HashMap<>();
        params.put("IDCARD", authId);
        String response = RestTemplateUtil.post(ApiConst.Social.RESIDENCE_PERMIT_POINT, header, params, String.class, uriMap);
        log.info("has120ResidencePermitPoints response|{}", response);
        ResidencePermitBaseResponse<String> residencePermitBaseResponse = JSON.parseObject(response, new TypeReference<ResidencePermitBaseResponse<String>>(){});
        List<ResidencePermitPointVO> residencePermitVOs = JSON.parseArray(residencePermitBaseResponse.getData(), ResidencePermitPointVO.class);
        if (!residencePermitVOs.isEmpty()
                && residencePermitVOs.get(0).getIdcard()!=null
                && residencePermitVOs.get(0).getIdcard().equals(authId)) {
            return true;
        }
        return false;
    }

    /**
     * 社保结果组装类
     */
    public class SocialResponse{
        /**
         * 0:未处理完成 1:成功 2:失败
         */
        private Integer result;

        /**
         * 累计缴纳社保月份
         */
        private Integer socialMonth;

        /**
         * 备注：请求失败原因
         */
        private String remark;

        /**
         * 纳税明细
         */
        private List<TaxationData> dataList;




        public SocialResponse(Integer result) {
            this.result = result;
        }

        public SocialResponse(Integer result, String remark) {
            this.result = result;
            this.remark = remark;
        }

        public SocialResponse(Integer result, Integer socialMonth, String remark){
            this.result = result;
            this.remark = remark;
            this.socialMonth = socialMonth;
        }

        public Integer getResult() {
            return result;
        }

        public void setResult(Integer result) {
            this.result = result;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }

        public Integer getSocialMonth() {
            return socialMonth;
        }

        public void setSocialMonth(Integer socialMonth) {
            this.socialMonth = socialMonth;
        }

        public List<TaxationData> getDataList() {
            return dataList;
        }

        public void setDataList(List<TaxationData> dataList) {
            this.dataList = dataList;
        }
    }
}
