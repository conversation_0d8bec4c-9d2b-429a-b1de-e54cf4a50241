package com.extracme.nevmp.service.qualification.impl;

import static com.extracme.nevmp.mapper.OwnerQualificationDynamicSqlSupport.ownerQualification;
import static com.extracme.nevmp.mapper.OwnerQualificationReviewDetailDynamicSqlSupport.ownerQualificationReviewDetail;
import static com.extracme.nevmp.mapper.OwnerQualificationSpecialDynamicSqlSupport.ownerQualificationSpecial;
import static com.extracme.nevmp.mapper.OwnerTrafficQualificationDynamicSqlSupport.ownerTrafficQualification;
import static org.mybatis.dynamic.sql.SqlBuilder.equalTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isGreaterThan;
import static org.mybatis.dynamic.sql.SqlBuilder.isIn;
import static org.mybatis.dynamic.sql.SqlBuilder.isNotNull;
import static org.mybatis.dynamic.sql.SqlBuilder.select;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.mybatis.dynamic.sql.SqlBuilder;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.extracme.nevmp.api.BaseResponse;
import com.extracme.nevmp.constant.ApiConst;
import com.extracme.nevmp.dto.gov.traffic.DriverIllegalInfo;
import com.extracme.nevmp.dto.gov.traffic.DriverLicenseInfo;
import com.extracme.nevmp.dto.gov.traffic.DriverVehicleInfo;
import com.extracme.nevmp.dto.gov.traffic.OwnerVehicleInfoDTO;
import com.extracme.nevmp.dto.gov.traffic.QueryOwnerVehicleListResponse;
import com.extracme.nevmp.dto.gov.traffic.TrafficBaseResponse;
import com.extracme.nevmp.dto.owner.DriverLicenseDTO;
import com.extracme.nevmp.enums.OwnerQualificationReviewStatusEnum;
import com.extracme.nevmp.enums.OwnerQualificationTypeEnum;
import com.extracme.nevmp.error.ServiceException;
import com.extracme.nevmp.mapper.OwnerQualificationMapper;
import com.extracme.nevmp.mapper.OwnerQualificationReviewDetailMapper;
import com.extracme.nevmp.mapper.OwnerTrafficQualificationMapper;
import com.extracme.nevmp.model.OwnerQualification;
import com.extracme.nevmp.model.OwnerQualificationReviewDetail;
import com.extracme.nevmp.model.OwnerTrafficQualification;
import com.extracme.nevmp.service.qualification.DriverService;
import com.extracme.nevmp.service.qualification.OwnerQualificationService;
import com.extracme.nevmp.service.traffic.TrafficPushService;
import com.extracme.nevmp.utils.DateUtil;
import com.extracme.nevmp.utils.JsonUtil;
import com.extracme.nevmp.utils.RestTemplateUtil;
import com.extracme.nevmp.utils.ShaUtil;
import com.fasterxml.jackson.core.type.TypeReference;


/**
 * <AUTHOR>
@Service
public class DriverServiceImpl implements DriverService {

    private static final Logger logger = LoggerFactory.getLogger(DriverServiceImpl.class);

    // 审核状态常量
    private static final int REVIEW_STATUS_PENDING = 0;        // 待审核
    private static final int REVIEW_STATUS_APPROVED = 1;       // 审核通过
    private static final int REVIEW_STATUS_REJECTED = 2;       // 审核拒绝
    private static final int REVIEW_STATUS_NOT_FOUND = 3;      // 未查询到相关信息

    // 审核类型常量
    private static final int REVIEW_TYPE_DRIVER = 0;           // 公安-驾照审核

    // 驾照类型常量
    private static final int DRIVER_LICENSE_TYPE_LOCAL = 0;    // 本地驾照
    private static final int DRIVER_LICENSE_TYPE_OUTER = 1;    // 外地驾照

    // 状态常量
    private static final int STATUS_VALID = 1;                 // 有效状态
    private static final int STATUS_INVALID = 0;              // 无效状态

    // 时间常量
    private static final long ONE_DAY_MILLIS = TimeUnit.DAYS.toMillis(1);     // 一天毫秒数
    private static final long ONE_YEAR_DAYS = 365L;                           // 一年天数
    private static final int VIOLATION_THRESHOLD = 5;                         // 违章次数阈值

    // 车辆额度类型常量
    private static final int CAR_QUOTA_NON_NEW_ENERGY = 2;     // 非新能源车专用额度

    // 地区常量
    private static final String SHANGHAI_CITY = "上海市";       // 上海市

    // 系统用户常量
    private static final String SYSTEM_USER_ID = "-1";         // 系统用户ID
    private static final String SYSTEM_USER_NAME = "system";   // 系统用户名
    private static final String SCHEDULE_USER_NAME = "schedule"; // 定时任务用户名

    // 驾照无效状态数组
    private static final String[] INVALID_DRIVER_LICENSE_STATUS = {
        "B", "C", "D", "E", "F", "G", "J", "K", "M", "R", "S", "U"
    };

    // 车辆无效状态数组
    private static final String[] INVALID_VEHICLE_STATUS = {"B", "M", "P", "E"};

    // API相关常量
    private static final String APP_KEY_MAIN = "98625141753466e54";           // 主库AppKey
    private static final String APP_SECRET_MAIN = "8HXTeWHk";                 // 主库AppSecret
    private static final String APP_KEY_HOUR = "7096b9f7dcfc455a9dc415b7afb685b3"; // 小时库AppKey
    private static final String APP_SECRET_HOUR = "IeXc1UtI";                 // 小时库AppSecret

    // 错误提示信息常量
    private static final String ERROR_NO_DRIVER_LICENSE = "未查询到驾照信息（驾驶证发证单位及核发地需保持一致。如您刚更新驾照信息，请在3个工作日后再查询，如您刚考取驾照，请在7个工作日后再查询。）";
    private static final String ERROR_LICENSE_INVALID = "驾驶证已失效";
    private static final String ERROR_LICENSE_EXPIRED = "驾驶证已过期";
    private static final String ERROR_LICENSE_UPDATE_NOTICE = "（驾驶证发证单位及核发地需保持一致。如您刚更新驾照信息，请在3个工作日后再查询）;";
    private static final String ERROR_VIOLATION_EXCEED = "近一年违章数量大于等于5次;";
    private static final String ERROR_HAS_PUNISHMENT = "近一年产生过处罚;";
    private static final String ERROR_HAS_NEW_ENERGY_VEHICLE = "名下有新能源汽车（如您刚将新能源汽车转出，请在3个工作日后再查询）;";
    private static final String ERROR_QUERY_VEHICLE_INFO_FAILED = "调用查询车辆信息接口（同步上牌进度）请求失败";
    private static final String ERROR_QUERY_VEHICLE_LIST_FAILED = "查询名下车辆列表失败";
    private static final String ERROR_QUERY_NEW_ENERGY_VEHICLE_FAILED = "查询名下有无新能源车失败";
    private static final String ERROR_NO_DRIVER_LICENSE_INFO = "未查询到相关驾照信息";

    // 特殊标识常量
    private static final String SPECIAL_TALENT_REMARK = "特殊人才";            // 特殊人才备注

    // HTTP相关常量
    private static final String CONTENT_TYPE_JSON = "application/json";       // JSON内容类型
    private static final String SUCCESS_CODE = "0";                           // 成功响应码


    @Autowired
    private OwnerQualificationReviewDetailMapper ownerQualificationReviewDetailMapper;

    @Autowired
    private OwnerTrafficQualificationMapper ownerTrafficQualificationMapper;

    @Autowired
    private OwnerQualificationMapper ownerQualificationMapper;

    @Autowired
    private ModelNationalCatalogueServiceImpl modelNationalCatalogueService;

    @Autowired
    private OwnerQualificationService ownerQualificationService;

    @Autowired
    private TrafficPushService trafficPushService;

    @Override
    public List<OwnerQualificationReviewDetail> queryPendingReview() {
        //只主动审核上海本地的驾照
        SelectStatementProvider selectStatement = SqlBuilder.select(ownerQualificationReviewDetail.allColumns())
                .from(ownerQualificationReviewDetail)
                .leftJoin(ownerTrafficQualification).on(ownerQualificationReviewDetail.ownerQulificationId, equalTo(ownerTrafficQualification.ownerQualificationId))
                .where(ownerQualificationReviewDetail.reviewStatus, isEqualTo(OwnerQualificationReviewStatusEnum.PENDING.getStatus()))
                .and(ownerQualificationReviewDetail.reviewType,isEqualTo(0))
                .and(ownerTrafficQualification.driverLicenseType, isEqualTo(0))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return ownerQualificationReviewDetailMapper.selectMany(selectStatement);
    }

    @Override
    public List<OwnerTrafficQualification> queryOuterDriverPendingReview() {
        // 查询未审批的、外地驾照、公安已审核、有效的记录
        SelectStatementProvider selectUnApproveTraffic = select(ownerTrafficQualification.allColumns())
                .from(ownerTrafficQualification)
                .where(ownerTrafficQualification.driverLicenseType, isEqualTo(1))
                .and(ownerTrafficQualification.hasDriverLicense, isNotNull())
                .and(ownerTrafficQualification.reviewStatus, isEqualTo(0))
                .and(ownerTrafficQualification.status, isEqualTo(1))
                .build().render(RenderingStrategies.MYBATIS3);
        List<OwnerTrafficQualification> ownerTrafficQualifications = ownerTrafficQualificationMapper.selectMany(selectUnApproveTraffic);
        if (ownerTrafficQualifications.isEmpty()) {
            return ownerTrafficQualifications;
        }

        SelectStatementProvider selectAuthId = select(ownerQualification.id, ownerQualification.authId)
                .from(ownerQualification)
                .where(ownerQualification.id,
                 isIn(ownerTrafficQualifications.stream().map(OwnerTrafficQualification::getOwnerQualificationId).collect(Collectors.toList())))
                .build().render(RenderingStrategies.MYBATIS3);
        List<OwnerQualification> ownerQualifications  = ownerQualificationMapper.selectMany(selectAuthId);
        Map<Long, String> authIdMap = ownerQualifications.stream().collect(Collectors.toMap(OwnerQualification::getId, OwnerQualification::getAuthId, (newV, oldV) -> newV));
        ownerTrafficQualifications.forEach(qualification -> {
            qualification.setDriverLicenseCode(authIdMap.get(qualification.getOwnerQualificationId()));
        });
        return ownerTrafficQualifications;
    }

    @Override
    public BaseResponse review(Long ownerQualificationId, DriverLicenseDTO driverLicense) {
        return driverReview(ownerQualificationId, driverLicense, false);
    }

    @Override
    public BaseResponse review(Long ownerQualificationId, DriverLicenseDTO driverLicense, Boolean isSpecial) {
        return driverReview(ownerQualificationId, driverLicense, isSpecial);
    }

    @Override
    public BaseResponse review(Long ownerQualificationId) {
        return review(ownerQualificationId, false);
    }

    @Override
    public BaseResponse review(Long ownerQualificationId, Boolean isSpecial) {
        //根据购车资质ID 查询对应的驾照信息
        OwnerTrafficQualification trafficQualification = queryOwnerTrafficQualification(ownerQualificationId);
        if(trafficQualification == null){
            throw new ServiceException("未查询到相关驾照信息");
        }

        //组装驾照信息参数
        DriverLicenseDTO driverLicense = DriverLicenseDTO.builder()
                .driverLicenseCode(trafficQualification.getDriverLicenseCode())
                .driverLicenseIssuingPlace(trafficQualification.getDriverLicenseIssuingPlace())
                .driverLicenseIssuingOrganization(trafficQualification.getDriverLicenseIssuingOrganization())
                .driverFileNo(trafficQualification.getDriverFileNo())
                .build();
        return review(ownerQualificationId, driverLicense, isSpecial);
    }

    /**
     * 审核驾照信息
     * @param ownerQualificationId 购车资质ID
     * @param driverLicense 驾照信息
     * @param isSpecial 是否特殊人才
     * @return 驾照审核结果 若审核失败，则抛出异常
     */
    private BaseResponse driverReview(Long ownerQualificationId, DriverLicenseDTO driverLicense, boolean isSpecial){
        logger.info("开始审核驾照信息，购车资质ID：{}，是否特殊人才：{}", ownerQualificationId, isSpecial);

        // 若没有保存驾照信息，则优先保存驾照信息，防止接口调用超时保存失败
        if(!ownerQualificationService.hasReview(ownerQualificationId, REVIEW_TYPE_DRIVER)) {
            logger.info("初始化驾照审核信息，购车资质ID：{}", ownerQualificationId);
            initOwnerTrafficQualification(ownerQualificationId, driverLicense);
        }

        // 只有申请时间超过一天后，才开始审核 - 用于减少大数据中心数据未及时更新的情况
        OwnerQualification theOwnerQualification = ownerQualificationService.getOwnerQualificationById(ownerQualificationId);
        if(theOwnerQualification != null && (new Date().getTime() - theOwnerQualification.getApplyTime().getTime() < ONE_DAY_MILLIS)) {
            logger.info("申请时间未超过一天，暂不审核，购车资质ID：{}", ownerQualificationId);
            return new BaseResponse();
        }

        if(theOwnerQualification == null) {
            logger.error("未查询到购车资质信息，购车资质ID：{}", ownerQualificationId);
            throw new ServiceException(ERROR_NO_DRIVER_LICENSE_INFO);
        }

        // 上海驾照或特殊人才则自动审批
        if(SHANGHAI_CITY.equals(driverLicense.getDriverLicenseIssuingPlace()) || isSpecial){
            logger.info("符合自动审批条件，购车资质ID：{}，发证地：{}，是否特殊人才：{}",
                ownerQualificationId, driverLicense.getDriverLicenseIssuingPlace(), isSpecial);

            int result = REVIEW_STATUS_PENDING;
            String remark = StringUtils.EMPTY;
            String reason = StringUtils.EMPTY;

            // 驾照信息
            OwnerTrafficQualification trafficQualification = new OwnerTrafficQualification();

            // 如果是特殊人才，则不对驾照信息进行校验
            if(isSpecial){
                logger.info("开始审核特殊人才信息，证件号：{}", theOwnerQualification.getAuthId());
                result = REVIEW_STATUS_APPROVED;
                remark = SPECIAL_TALENT_REMARK;
            }
            // 非特殊人才，按照常规进行校验
            else{
                try{
                    logger.info("开始常规驾照校验，购车资质ID：{}", ownerQualificationId);

                    // 根据档案编号、驾照号获取驾照信息
                    DriverLicenseInfo driverLicenseInfo = queryDriverLicenseInfoWithFallback(driverLicense);

                    // 审核驾照信息并设置审核结果
                    result = validateDriverLicenseAndSetResult(driverLicenseInfo, trafficQualification, theOwnerQualification);
                    reason = trafficQualification.getReason() != null ? trafficQualification.getReason() : StringUtils.EMPTY;

                }catch (ServiceException e){
                    logger.error("驾照审核业务异常，购车资质ID：{}，错误信息：{}", ownerQualificationId, e.getMessage());
                    result = REVIEW_STATUS_PENDING;
                    reason = e.getMessage();
                }catch (Exception e){
                    logger.error("驾照审核系统异常，购车资质ID：{}", ownerQualificationId, e);
                    result = REVIEW_STATUS_PENDING;
                    remark = e.getMessage();
                }
            }

            // 更新审核结果到数据库
            updateReviewResult(ownerQualificationId, result, reason, remark, trafficQualification);

            logger.info("驾照审核完成，购车资质ID：{}，审核结果：{}", ownerQualificationId, result);
        }
        return new BaseResponse();
    }

    /**
     * 查询驾照信息，支持主库和小时库回退查询
     * @param driverLicense 驾照信息
     * @return 驾照信息
     */
    private DriverLicenseInfo queryDriverLicenseInfoWithFallback(DriverLicenseDTO driverLicense) {
        DriverLicenseInfo driverLicenseInfo = null;

        // 本市驾照采用接口自动查询
        if(StringUtils.isNotBlank(driverLicense.getDriverFileNo())){
            logger.info("根据档案编号查询驾照信息：{}", driverLicense.getDriverFileNo());
            // 根据档案编号进行查询
            driverLicenseInfo = queryDriverLicenseInfoByFileNo(driverLicense.getDriverFileNo());
            // 如果常用库查询不到，则查询小时库进行补充查询
            if(driverLicenseInfo == null){
                driverLicenseInfo = queryDriverLicenseInfoByFileNoHour(driverLicense.getDriverFileNo());
                if(driverLicenseInfo != null){
                    // 记录小时库查询日志，用于分析切换库是否有效
                    logger.warn("档案编号{}：成功从小时库中查询到数据", driverLicense.getDriverFileNo());
                }
            }
        }else if(StringUtils.isNotBlank(driverLicense.getDriverLicenseCode())){
            logger.info("根据驾照号查询驾照信息：{}", driverLicense.getDriverLicenseCode());
            // 根据驾照号进行查询
            driverLicenseInfo = queryDriverLicenseInfoByDriverLicenseCode(driverLicense.getDriverLicenseCode());
            // 如果常用库查询不到，则查询小时库进行补充查询
            if(driverLicenseInfo == null){
                driverLicenseInfo = queryDriverLicenseInfoByDriverLicenseCodeHour(driverLicense.getDriverLicenseCode());
                if(driverLicenseInfo != null){
                    // 记录小时库查询日志，用于分析切换库是否有效
                    logger.warn("驾照号{}：成功从小时库中查询到数据", driverLicense.getDriverLicenseCode());
                }
            }
        }

        return driverLicenseInfo;
    }

                    //审核有无该驾照信息
                    if(driverLicenseInfo == null){
                        result = 3;
                        reason = "未查询到驾照信息（驾驶证发证单位及核发地需保持一致。如您刚更新驾照信息，请在3个工作日后再查询，如您刚考取驾照，请在7个工作日后再查询。）";
                        trafficQualification.setHasDriverLicense(0);
                    }else{
                        trafficQualification.setHasDriverLicense(1);
                        //驾驶证号
                        String driverLicenseCode = driverLicenseInfo.getSfzmhm();

                        //驾照是否异常
                        boolean isDriverLicenseAbnormal = false;

                        //审核驾照状态是否有效
                        String[] invalidStatusArray = new String[]{"B","C","D","E","F","G","J","K","M","R","S","U"};
                        boolean isLicenseValidity = true;
                        for (String invalidStatus : invalidStatusArray){
                            if(driverLicenseInfo.getZt().contains(invalidStatus)){
                                isLicenseValidity = false;
                                break;
                            }
                        }
                        if(!isLicenseValidity){
                            reason += "驾驶证已失效";
                            trafficQualification.setDriverLicenseIsValidity(0);
                            isDriverLicenseAbnormal = true;
                        }
                        trafficQualification.setDriverLicenseIsValidity(1);


                        //审核驾照有效期
                        if(StringUtils.isNotBlank(driverLicenseInfo.getYxqz())&& (DateUtil.parse(driverLicenseInfo.getYxqz(), DateUtil.DATE_TYPE1).getTime() < System.currentTimeMillis())){
                            if(isDriverLicenseAbnormal){
                                reason +="，";
                            }
                            reason += "驾驶证已过期";
                            trafficQualification.setDriverLicenseNoExpire(0);
                            isDriverLicenseAbnormal = true;
                        }else{
                            trafficQualification.setDriverLicenseNoExpire(1);
                        }

                        if(isDriverLicenseAbnormal){
                            reason += "（驾驶证发证单位及核发地需保持一致。如您刚更新驾照信息，请在3个工作日后再查询）;";
                        }


                        //查询违章次数
                        if(queryViolationCount(driverLicenseCode) >= 5){
                            reason += "近一年违章数量大于等于5次;";
                            trafficQualification.setDriverLicenseHasViolation(0);
                        }else{
                            trafficQualification.setDriverLicenseHasViolation(1);
                        }

                        //查询有无处罚
                        if(queryPunishCount(driverLicenseCode) > 0){
                            reason += "近一年产生过处罚;";
                            trafficQualification.setHasPunish(0);
                        }else{
                            trafficQualification.setHasPunish(1);
                        }

                        //以旧换新不用查询名下有无新能源车
                        if(Objects.equals(OwnerQualificationTypeEnum.NORMAL.getType(), theOwnerQualification.getQualificationType())){
                            //查询名下有无新能源汽车
                            List<String> driverVehicleList = queryDriverVehicleList(driverLicenseCode);
                            if(!driverVehicleList.isEmpty()) {
                                driverVehicleList = queryDriverVehicleListHour(driverLicenseCode);
                            }
                            if(!driverVehicleList.isEmpty()){
                                reason +="名下有新能源汽车（如您刚将新能源汽车转出，请在3个工作日后再查询）;";
                                trafficQualification.setHasNewEnergyVehicle(0);
                            }else{
                                trafficQualification.setHasNewEnergyVehicle(1);
                            }
                        }

                        //给定最终审核结果
                        if(StringUtils.isBlank(reason)){
                            result = 1;
                        }else{
                            result = 2;
                            //优化提示语，把最后一个';'去除，方便失败原因拼接
                            reason = reason.substring(0,reason.length()-1);
                        }
                    }
                }catch (ServiceException e){
                    result = 0;
                    reason = e.getMessage();
                }catch (Exception e){
                    result = 0;
                    remark = e.getMessage();
                    logger.error(e.getMessage(),e);
                }
            }


            Date now = new Date();
            //更新最终审核结果
            UpdateStatementProvider updateStatement = SqlBuilder.update(ownerQualificationReviewDetail)
                    .set(ownerQualificationReviewDetail.reviewStatus).equalTo(result)
                    .set(ownerQualificationReviewDetail.reason).equalTo(reason)
                    .set(ownerQualificationReviewDetail.updatedTime).equalTo(now)
                    .set(ownerQualificationReviewDetail.updatedUserId).equalTo("-1")
                    .set(ownerQualificationReviewDetail.updatedUserName).equalTo("schedule")
                    .set(ownerQualificationReviewDetail.remark).equalTo(remark)
                    .where(ownerQualificationReviewDetail.reviewType, isEqualTo(0))
                    .and(ownerQualificationReviewDetail.ownerQulificationId, isEqualTo(ownerQualificationId))
                    .and(ownerQualificationReviewDetail.reviewStatus, isEqualTo(0))
                    .build()
                    .render(RenderingStrategies.MYBATIS3);
            ownerQualificationReviewDetailMapper.update(updateStatement);


            //更新驾驶人信息审核结果
            UpdateStatementProvider updateDriverStatement = SqlBuilder.update(ownerTrafficQualification)
                    //有无驾照
                    .set(ownerTrafficQualification.hasDriverLicense).equalToWhenPresent(trafficQualification.getHasDriverLicense())
                    //驾照是否有效
                    .set(ownerTrafficQualification.driverLicenseIsValidity).equalToWhenPresent(trafficQualification.getDriverLicenseIsValidity())
                    //驾照是否过期
                    .set(ownerTrafficQualification.driverLicenseNoExpire).equalToWhenPresent(trafficQualification.getDriverLicenseNoExpire())
                    //违章次数
                    .set(ownerTrafficQualification.driverLicenseHasViolation).equalToWhenPresent(trafficQualification.getDriverLicenseHasViolation())
                    //是否有处罚
                    .set(ownerTrafficQualification.hasPunish).equalToWhenPresent(trafficQualification.getHasPunish())
                    //名下有新能汽车
                    .set(ownerTrafficQualification.hasNewEnergyVehicle).equalToWhenPresent(trafficQualification.getHasNewEnergyVehicle())
                    .set(ownerTrafficQualification.reason).equalTo(reason)
                    .set(ownerTrafficQualification.reviewStatus).equalTo(result)
                    .set(ownerTrafficQualification.updatedTime).equalTo(now)
                    .set(ownerTrafficQualification.updatedUserId).equalTo("-1")
                    .set(ownerTrafficQualification.updatedUserName).equalTo("schedule")
                    .set(ownerTrafficQualification.reviewTime).equalTo(now)
                    .where(ownerTrafficQualification.ownerQualificationId, isEqualTo(ownerQualificationId))
                    .build()
                    .render(RenderingStrategies.MYBATIS3);
            ownerTrafficQualificationMapper.update(updateDriverStatement);
        }
        return new BaseResponse();
    }

    /**
     * 初始化驾照信息
     * @param ownerQualificationId 用户购车资质ID
     * @param driverLicense 驾照信息
     */
    private void initOwnerTrafficQualification(Long ownerQualificationId, DriverLicenseDTO driverLicense){
        Date now = new Date();
        //初始化审核信息
        OwnerQualificationReviewDetail ownerQualificationReviewDetail = new OwnerQualificationReviewDetail();
        ownerQualificationReviewDetail.setOwnerQulificationId(ownerQualificationId);
        ownerQualificationReviewDetail.setReviewType(0);
        ownerQualificationReviewDetail.setReviewStatus(0);
        ownerQualificationReviewDetail.setCreatedTime(now);
        ownerQualificationReviewDetail.setCreatedUserId("-1");
        ownerQualificationReviewDetail.setCreatedUserName("system");
        ownerQualificationReviewDetail.setUpdatedTime(now);
        ownerQualificationReviewDetail.setUpdatedUserId("-1");
        ownerQualificationReviewDetail.setUpdatedUserName("system");
        ownerQualificationReviewDetailMapper.insertSelective(ownerQualificationReviewDetail);

        //初始化驾照基础信息
        OwnerTrafficQualification saveTrafficQualification = new OwnerTrafficQualification();
        saveTrafficQualification.setOwnerQualificationId(ownerQualificationId);
        saveTrafficQualification.setDriverLicenseCode(driverLicense.getDriverLicenseCode());
        saveTrafficQualification.setDriverLicenseIssuingPlace(driverLicense.getDriverLicenseIssuingPlace());
        saveTrafficQualification.setDriverLicenseIssuingOrganization(driverLicense.getDriverLicenseIssuingOrganization());
        saveTrafficQualification.setDriverFileNo(driverLicense.getDriverFileNo());
        if ("上海市".equals(driverLicense.getDriverLicenseIssuingPlace())) {
            //本地驾照
            saveTrafficQualification.setDriverLicenseType(0);
        } else {
            //外地驾照
            saveTrafficQualification.setDriverLicenseType(1);
        }
        //审核结果
        saveTrafficQualification.setCreatedTime(now);
        saveTrafficQualification.setCreatedUserId("-1");
        saveTrafficQualification.setCreatedUserName("system");
        saveTrafficQualification.setUpdatedTime(now);
        saveTrafficQualification.setUpdatedUserId("-1");
        saveTrafficQualification.setUpdatedUserName("system");
        ownerTrafficQualificationMapper.insertSelective(saveTrafficQualification);
    }

    /**
     * 查询用户驾驶人资质审核结果
     * @param ownerQualificationId 购车资质ID
     * @return 购车资质对应的驾照信息
     */
    private OwnerTrafficQualification queryOwnerTrafficQualification(Long ownerQualificationId){
        SelectStatementProvider selectStatement = SqlBuilder.select(ownerTrafficQualification.allColumns())
                .from(ownerTrafficQualification)
                .where(ownerTrafficQualification.ownerQualificationId,isEqualTo(ownerQualificationId))
                .limit(1)
                .build()
                .render(RenderingStrategies.MYBATIS3);

        final Optional<OwnerTrafficQualification> ownerTrafficQualification = ownerTrafficQualificationMapper.selectOne(selectStatement);
        return ownerTrafficQualification.orElse(null);
    }

    /**
     * 查询车辆信息（上牌进度）
     * @param vin 车架号
     * @return 车架号对应的车辆信息
     */
    @Override
    public DriverVehicleInfo queryVehicleInfo(String vin){
        Map<String, String> uriMap = new HashMap<>();
        uriMap.put("appKey", "98625141753466e54");
        uriMap.put("timestamp", String.valueOf(System.currentTimeMillis()));
        uriMap.put("signature", ShaUtil.shaEncode(uriMap.get("appKey") + "8HXTeWHk" + uriMap.get("timestamp")));
        uriMap.put("serviceCode", "yqmdTkAR86fxU5Qr");

        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json");

        Map<String, String> params = new HashMap<>();
        params.put("CLSBDH",vin);

        String response = RestTemplateUtil.post(ApiConst.Traffic.QUERY_VEHICLE_INFO, header, params, String.class, uriMap);
        if(StringUtils.isNotBlank(response)){
            TrafficBaseResponse<String> baseResponse = JsonUtil.readStr(response, new TypeReference<TrafficBaseResponse<String>>() {});
            List<DriverVehicleInfo> driverLicenseInfoList = JsonUtil.readStr(baseResponse.getData(), new TypeReference<List<DriverVehicleInfo>>() {});
            return driverLicenseInfoList.size() > 0 ? driverLicenseInfoList.get(0) : null;
        }else{
            throw new ServiceException("调用查询车辆信息接口（同步上牌进度）请求失败");
        }

    }


    /**
     * 根据档案编号查询驾照信息 - 小时库
     * @param fileNo 驾驶证档案编号
     * @return 驾照信息
     */
    public DriverLicenseInfo queryDriverLicenseInfoByFileNoHour(String fileNo){
        Map<String, String> map = new HashMap<>();
        map.put("appKey", "7096b9f7dcfc455a9dc415b7afb685b3");
        map.put("timestamp", String.valueOf(System.currentTimeMillis()));
        map.put("signature", ShaUtil.shaEncode(map.get("appKey") + "IeXc1UtI" + map.get("timestamp")));
        map.put("serviceCode", "HPXwEjWJ32lO4MvF");

        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json");

        Map<String, String> params = new HashMap<>();
        params.put("DABH",fileNo);

        String response = RestTemplateUtil.post(ApiConst.Traffic.QUERY_DRIVER_LICENSE_BY_FILE_HOUR, header, params, String.class, map);
        logger.info(fileNo + "查询本地驾驶证-小时库 信息结果:" + response);
        TrafficBaseResponse<String> driveInfo = JsonUtil.readStr(response, new TypeReference<TrafficBaseResponse<String>>() {});
        List<DriverLicenseInfo> driverLicenseInfoList = JsonUtil.readStr(driveInfo.getData(), new TypeReference<List<DriverLicenseInfo>>() {});
        DriverLicenseInfo result = null;
        if(!driverLicenseInfoList.isEmpty()){
            result = driverLicenseInfoList.get(0);

            //如果查询到多个驾照信息，则优先获取有效的驾照
            if(driverLicenseInfoList.size() > 1){
                for(DriverLicenseInfo driverLicenseInfo : driverLicenseInfoList){
                    //校验驾照状态
                    if(isDriverLicenseStatusValid(driverLicenseInfo.getZt())){
                        result = driverLicenseInfo;
                        break;
                    }
                }
            }
        }
        return result;
    }


    /**
     * 根据档案编号查询驾照信息
     * @param fileNo 驾驶证档案编号
     * @return 驾照信息
     */
    public DriverLicenseInfo queryDriverLicenseInfoByFileNo(String fileNo){
        Map<String, String> map = new HashMap<>();
        map.put("appKey", "98625141753466e54");
        map.put("timestamp", String.valueOf(System.currentTimeMillis()));
        map.put("signature", ShaUtil.shaEncode(map.get("appKey") + "8HXTeWHk" + map.get("timestamp")));
        map.put("serviceCode", "p6tXwHd7jo5Iz2oE");

        System.out.println(map);

        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json");

        Map<String, String> params = new HashMap<>();
        params.put("DABH",fileNo);

        String response = RestTemplateUtil.post(ApiConst.Traffic.QUERY_DRIVER_LICENSE, header, params, String.class, map);
        logger.info(fileNo + "查询本地驾驶证信息结果:" + response);
        TrafficBaseResponse<String> driveInfo = JsonUtil.readStr(response, new TypeReference<TrafficBaseResponse<String>>() {});
        List<DriverLicenseInfo> driverLicenseInfoList = JsonUtil.readStr(driveInfo.getData(), new TypeReference<List<DriverLicenseInfo>>() {});

        DriverLicenseInfo result = null;
        if(driverLicenseInfoList.size() > 0){
            result = driverLicenseInfoList.get(0);

            //如果查询到多个驾照信息，则优先获取有效的驾照
            if(driverLicenseInfoList.size() > 1){
                for(DriverLicenseInfo driverLicenseInfo : driverLicenseInfoList){
                    //校验驾照状态
                    if(isDriverLicenseStatusValid(driverLicenseInfo.getZt())){
                        result = driverLicenseInfo;
                        break;
                    }
                }
            }
        }
        return result;
    }


    /**
     * 根据身份证（驾照号） 查询驾照信息 - 小时库
     * @param driverLicenseCode 驾照号
     * @return 驾照信息
     */
    @Override
    public DriverLicenseInfo queryDriverLicenseInfoByDriverLicenseCodeHour(String driverLicenseCode) {
        Map<String, String> map = new HashMap<>();
        map.put("appKey", "7096b9f7dcfc455a9dc415b7afb685b3");
        map.put("timestamp", String.valueOf(System.currentTimeMillis()));
        map.put("signature", ShaUtil.shaEncode(map.get("appKey") + "IeXc1UtI" + map.get("timestamp")));
        map.put("serviceCode", "NmiN5aTnruro6rqo");

        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json");

        Map<String, String> params = new HashMap<>();
        params.put("SFZMHM",driverLicenseCode);
        String response = RestTemplateUtil.post(ApiConst.Traffic.QUERY_DRIVER_LICENSE_HOUR, header, params, String.class, map);
        logger.info(driverLicenseCode + "查询本地驾驶证-小时库 信息结果:" + response);

        TrafficBaseResponse<String> driveInfo = JsonUtil.readStr(response, new TypeReference<TrafficBaseResponse<String>>() {});
        List<DriverLicenseInfo> driverLicenseInfoList = JsonUtil.readStr(driveInfo.getData(), new TypeReference<List<DriverLicenseInfo>>() {});
        DriverLicenseInfo result = null;
        if(driverLicenseInfoList.size() > 0){
            result = driverLicenseInfoList.get(0);
            //如果查询到多个驾照信息，则优先获取有效的驾照
            if(driverLicenseInfoList.size() > 1){
                for(DriverLicenseInfo driverLicenseInfo : driverLicenseInfoList){
                    //校验驾照状态
                    if(isDriverLicenseStatusValid(driverLicenseInfo.getZt())){
                        result = driverLicenseInfo;
                        break;
                    }
                }
            }
        }
        return result;
    }

    /**
     * 根据身份证（驾照号） 查询驾照信息
     * @param driverLicenseCode 驾驶证编号
     * @return 驾照信息
     */
    public DriverLicenseInfo queryDriverLicenseInfoByDriverLicenseCode(String driverLicenseCode) {
        Map<String, String> map = new HashMap<>();
        map.put("appKey", "98625141753466e54");
        map.put("timestamp", String.valueOf(System.currentTimeMillis()));
        map.put("signature", ShaUtil.shaEncode(map.get("appKey") + "8HXTeWHk" + map.get("timestamp")));
        map.put("serviceCode", "aUdU477pHWMunxLb");

        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json");

        Map<String, String> params = new HashMap<>();
        params.put("SFZMHM",driverLicenseCode);
        String response = RestTemplateUtil.post(ApiConst.Traffic.QUERY_DRIVER_LICENSE, header, params, String.class, map);
        logger.info(driverLicenseCode + "查询本地驾驶证 信息结果:" + response);

        TrafficBaseResponse<String> driveInfo = JsonUtil.readStr(response, new TypeReference<TrafficBaseResponse<String>>() {});
        List<DriverLicenseInfo> driverLicenseInfoList = JsonUtil.readStr(driveInfo.getData(), new TypeReference<List<DriverLicenseInfo>>() {});
        DriverLicenseInfo result = null;
        if(driverLicenseInfoList.size() > 0){
            result = driverLicenseInfoList.get(0);
            //如果查询到多个驾照信息，则优先获取有效的驾照
            if(driverLicenseInfoList.size() > 1){
                for(DriverLicenseInfo driverLicenseInfo : driverLicenseInfoList){
                    //校验驾照状态
                    if(isDriverLicenseStatusValid(driverLicenseInfo.getZt())){
                        result = driverLicenseInfo;
                        break;
                    }
                }
            }
        }
        return result;
    }

    /**
     * 查询交通违法次数
     * @param driverLicenseCode 驾驶证编号
     * @return 违法次数
     */
    public static Integer queryViolationCount(String driverLicenseCode) {
        Map<String, String> uriMap = new HashMap<>();
        uriMap.put("appKey", "98625141753466e54");
        uriMap.put("timestamp", String.valueOf(System.currentTimeMillis()));
        uriMap.put("signature", ShaUtil.shaEncode(uriMap.get("appKey") + "8HXTeWHk" + uriMap.get("timestamp")));
        uriMap.put("serviceCode", "ygo9Y1gmtWWaDh60");


        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json");

        Map<String, String> params = new HashMap<>();
        params.put("driverLicenseNo",driverLicenseCode);
        Date now = new Date();
        Date oneYearsAgo = new Date(now.getTime() - TimeUnit.DAYS.toMillis(365));
        params.put("illegalStartTime", DateUtil.format(oneYearsAgo, DateUtil.DATE_TYPE1));
        params.put("illegalEndTime", DateUtil.format(now, DateUtil.DATE_TYPE1));
        String response = RestTemplateUtil.post(ApiConst.Traffic.QUERY_DRIVER_ILLEGAL_COUNT, header, params, String.class, uriMap);
        DriverIllegalInfo driverIllegalInfo = JsonUtil.readStr(response, new TypeReference<DriverIllegalInfo>() {});
        return driverIllegalInfo.getData().getIllegalCount();
    }

    /**
     * 查询驾驶员处罚数
     * @param driverLicenseCode 驾驶证编号
     * @return 处罚次数
     */
    private Integer queryPunishCount(String driverLicenseCode){
//        Map<String, String> uriMap = new HashMap<>();
//        uriMap.put("appKey", "98625141753466e54");
//        uriMap.put("timestamp", String.valueOf(System.currentTimeMillis()));
//        uriMap.put("signature", ShaUtil.shaEncode(uriMap.get("appKey") + "8HXTeWHk" + uriMap.get("timestamp")));
//        uriMap.put("serviceCode", "uoDRPXdC8CAuQ0Mr");
//
//        Map<String, String> header = new HashMap<>();
//        header.put("Content-Type", "application/json");
//
//        Map<String, String> params = new HashMap<>();
//        params.put("driverLicenseNo",driverLicenseCode);
//        Date now = new Date();
//        Date oneYearsAgo = new Date(now.getTime() - TimeUnit.DAYS.toMillis(365));
//        params.put("illegalStartTime", DateUtil.format(oneYearsAgo, DateUtil.DATE_TYPE1));
//        params.put("illegalEndTime", DateUtil.format(now, DateUtil.DATE_TYPE1));
//        String response = RestTemplateUtil.post(ApiConst.Traffic.QUERY_DRIVER_PUNISH_COUNT, header, params, String.class, uriMap);
//        DriverIllegalInfo driverIllegalInfo = JsonUtil.readStr(response, new TypeReference<DriverIllegalInfo>() {});
//        return driverIllegalInfo.getData().getIllegalCount();


        //2025-2-24日，取消针对用户是否存在处罚的判断
        return 0;
    }


    @Override
    public List<OwnerVehicleInfoDTO> queryOwnerVehicleList(String authId) {
        Map<String, String> uriMap = new HashMap<>();
        uriMap.put("appKey", "98625141753466e54");
        uriMap.put("timestamp", String.valueOf(System.currentTimeMillis()));
        uriMap.put("signature", ShaUtil.shaEncode(uriMap.get("appKey") + "8HXTeWHk" + uriMap.get("timestamp")));
        uriMap.put("serviceCode", "4EmafmU7x8STY4MY");

        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json");

        Map<String, String> params = new HashMap<>();
        params.put("SFZMHM", authId);

        String response = RestTemplateUtil.post(ApiConst.Traffic.QUERY_DRIVER_VEHICLES, header, params, String.class, uriMap);
        QueryOwnerVehicleListResponse ownerVehicleListResponse = JsonUtil.readStr(response, new TypeReference<QueryOwnerVehicleListResponse>() {});
        if (ownerVehicleListResponse != null && StringUtils.isNotBlank(ownerVehicleListResponse.getData())) {
            //二次解析
            return JsonUtil.readStr(ownerVehicleListResponse.getData(), new TypeReference<List<OwnerVehicleInfoDTO>>() {});
        }else{
            return new ArrayList<>();
        }
    }

    @Override
    public Boolean hasNewEnergyVehicle(String authId) {
        List<String> vehicleList = queryDriverVehicleList(authId);
        //查询小时库，以降低错误率
        if (!vehicleList.isEmpty()) {
            vehicleList = queryDriverVehicleListHour(authId);
        }
        if (!vehicleList.isEmpty()) {
            return true;
        }
        return false;
    }

    @Override
    public List<OwnerQualificationReviewDetail> querySpecialPendingReview() {
        SelectStatementProvider selectStatement = SqlBuilder.select(ownerQualificationReviewDetail.allColumns())
                .from(ownerQualificationReviewDetail)
                .leftJoin(ownerQualification).on(ownerQualificationReviewDetail.ownerQulificationId, equalTo(ownerQualification.id))
                .leftJoin(ownerQualificationSpecial).on(ownerQualificationSpecial.authId, equalTo(ownerQualification.authId))
                .where(ownerQualificationReviewDetail.reviewStatus, isEqualTo(OwnerQualificationReviewStatusEnum.PENDING.getStatus()))
                .and(ownerQualificationReviewDetail.reviewType,isEqualTo(0))
                .and(ownerQualificationSpecial.status, isEqualTo(1))
                .and(ownerQualificationSpecial.expireDate, isGreaterThan(new Date()))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return ownerQualificationReviewDetailMapper.selectMany(selectStatement);
    }


    /**
     * 查询用户名下车辆列表
     * @param authId 证件号
     * @return 名下车辆列表
     */
    @Override
    public List<OwnerVehicleInfoDTO> queryDriverCurrentVehicleList(String authId){
        Map<String, String> uriMap = new HashMap<>();
        uriMap.put("appKey", "7096b9f7dcfc455a9dc415b7afb685b3");
        uriMap.put("timestamp", String.valueOf(System.currentTimeMillis()));
        uriMap.put("signature", ShaUtil.shaEncode(uriMap.get("appKey") + "IeXc1UtI" + uriMap.get("timestamp")));
        uriMap.put("serviceCode", "9h6lUwyMtQ37g8zr");


        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json");

        Map<String, String> params = new HashMap<>();
        params.put("SFZMHM",authId);

        String response = RestTemplateUtil.post(ApiConst.Traffic.QUERY_DRIVER_VEHICLES_HOUR, header, params, String.class, uriMap);
        QueryOwnerVehicleListResponse ownerVehicleListResponse = JsonUtil.readStr(response, new TypeReference<QueryOwnerVehicleListResponse>() {});
        if (!"0".equals(ownerVehicleListResponse.getCode())) {
            throw new ServiceException("查询名下车辆列表失败");
        }

        List<OwnerVehicleInfoDTO> result = new ArrayList<>();
        if(StringUtils.isNotBlank(ownerVehicleListResponse.getData())) {
            //获取新能源目录
            Map<String,String> catalogueMap = modelNationalCatalogueService.getEventMap();

            List<OwnerVehicleInfoDTO> ownerVehicleInfoList = JsonUtil.readStr(ownerVehicleListResponse.getData(), new TypeReference<List<OwnerVehicleInfoDTO>>() {});
            for (OwnerVehicleInfoDTO ownerVehicleInfo : ownerVehicleInfoList) {
                //剔除掉无效车辆
                String[] invalidStatusArray = new String[]{"B","M","P","E"};
                for (String invalidStatus : invalidStatusArray){
                    if(ownerVehicleInfo.getZt().contains(invalidStatus)){
                        break;
                    }
                }
                ownerVehicleInfo.setNewEnergy(catalogueMap.containsKey(ownerVehicleInfo.getClxh()));
                //如果是新能源，则校验额度
                if(ownerVehicleInfo.isNewEnergy()){
                    //校验额度性质
                    Integer carQuota = trafficPushService.queryCarQuotaInfo(ownerVehicleInfo.getClsbdh(), authId);
                    ownerVehicleInfo.setCarQuota(carQuota);
                }
                result.add(ownerVehicleInfo);
            }
        }
        return result;
    }


    @Override
    public List<String> queryDriverVehicleListHour(String authId){
        Map<String, String> uriMap = new HashMap<>();
        uriMap.put("appKey", "7096b9f7dcfc455a9dc415b7afb685b3");
        uriMap.put("timestamp", String.valueOf(System.currentTimeMillis()));
        uriMap.put("signature", ShaUtil.shaEncode(uriMap.get("appKey") + "IeXc1UtI" + uriMap.get("timestamp")));
        uriMap.put("serviceCode", "9h6lUwyMtQ37g8zr");


        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json");

        Map<String, String> params = new HashMap<>();
        params.put("SFZMHM",authId);

        String response = RestTemplateUtil.post(ApiConst.Traffic.QUERY_DRIVER_VEHICLES_HOUR, header, params, String.class, uriMap);
        QueryOwnerVehicleListResponse ownerVehicleListResponse = JsonUtil.readStr(response, new TypeReference<QueryOwnerVehicleListResponse>() {});
        if (!"0".equals(ownerVehicleListResponse.getCode())) {
            throw new ServiceException("查询名下有无新能源车失败");
        }


        List<String> result = new ArrayList<>();
        if(StringUtils.isNotBlank(ownerVehicleListResponse.getData())){
            //二次解析
            List<OwnerVehicleInfoDTO> ownerVehicleInfoList =  JsonUtil.readStr(ownerVehicleListResponse.getData(), new TypeReference<List<OwnerVehicleInfoDTO>>() {});
            System.out.println(JSON.toJSON(ownerVehicleInfoList));

            for(OwnerVehicleInfoDTO ownerVehicleInfo : ownerVehicleInfoList){
                //校验车型是否在新能源目录中
                Map<String,String> catalogueMap = modelNationalCatalogueService.getEventMap();
                if(catalogueMap.containsKey(ownerVehicleInfo.getClxh())){

                    //校验车辆状态是否有效
                    String[] invalidStatusArray = new String[]{"B","M","P","E"};
                    boolean isVehicleValidity = true;
                    for (String invalidStatus : invalidStatusArray){
                        if(ownerVehicleInfo.getZt().contains(invalidStatus)){
                            isVehicleValidity = false;
                            break;
                        }
                    }

                    //校验额度性质
                    Integer carQuota = trafficPushService.queryCarQuotaInfo(ownerVehicleInfo.getClsbdh(), authId);

                    //如果车辆在新能源目录中，且有效，且不是非新能源车专用额度上牌
                    if(isVehicleValidity && !Objects.equals(carQuota,2)){
                        result.add(ownerVehicleInfo.getClsbdh());
                    }
                }
            }
        }
        return result;
    }


    /**
     * 查询驾驶员名下新能源车辆列表
     * @param authId 身份证号
     * @return 驾驶员名下新能源车辆列表
     */
    public List<String> queryDriverVehicleList(String authId){
        Map<String, String> uriMap = new HashMap<>();
        uriMap.put("appKey", "98625141753466e54");
        uriMap.put("timestamp", String.valueOf(System.currentTimeMillis()));
        uriMap.put("signature", ShaUtil.shaEncode(uriMap.get("appKey") + "8HXTeWHk" + uriMap.get("timestamp")));
        uriMap.put("serviceCode", "4EmafmU7x8STY4MY");


        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json");

        Map<String, String> params = new HashMap<>();
        params.put("SFZMHM",authId);

        String response = RestTemplateUtil.post(ApiConst.Traffic.QUERY_DRIVER_VEHICLES, header, params, String.class, uriMap);
        QueryOwnerVehicleListResponse ownerVehicleListResponse = JsonUtil.readStr(response, new TypeReference<QueryOwnerVehicleListResponse>() {});
        if (!"0".equals(ownerVehicleListResponse.getCode())) {
            throw new ServiceException("查询名下有无新能源车失败");
        }


        List<String> result = new ArrayList<>();
        if(StringUtils.isNotBlank(ownerVehicleListResponse.getData())){
            //二次解析
            List<OwnerVehicleInfoDTO> ownerVehicleInfoList =  JsonUtil.readStr(ownerVehicleListResponse.getData(), new TypeReference<List<OwnerVehicleInfoDTO>>() {});
            System.out.println(JSON.toJSON(ownerVehicleInfoList));

            for(OwnerVehicleInfoDTO ownerVehicleInfo : ownerVehicleInfoList){
                //校验车型是否在新能源目录中
                Map<String,String> catalogueMap = modelNationalCatalogueService.getEventMap();
                if(catalogueMap.containsKey(ownerVehicleInfo.getClxh())){

                    //校验车辆状态是否有效
                    String[] invalidStatusArray = new String[]{"B","M","P","E"};
                    boolean isVehicleValidity = true;
                    for (String invalidStatus : invalidStatusArray){
                        if(ownerVehicleInfo.getZt().contains(invalidStatus)){
                            isVehicleValidity = false;
                            break;
                        }
                    }

                    //校验额度性质
                    Integer carQuota = trafficPushService.queryCarQuotaInfo(ownerVehicleInfo.getClsbdh(), authId);

                    //如果车辆在新能源目录中，且有效，且不是非新能源车专用额度上牌
                    if(isVehicleValidity && !Objects.equals(carQuota,2)){
                        result.add(ownerVehicleInfo.getClxh());
                    }
                }
            }
        }
        return result;
    }

    /**
     * 校验驾驶证信息状态是否有效
     * @param zt 驾驶证状态
     * @return true:状态有效 false:状态无效
     */
    private Boolean isDriverLicenseStatusValid(String zt){
        boolean result = true;
        String[] invalidStatusArray = new String[]{"B","C","D","E","F","G","J","K","M","R","S","U"};
        for (String invalidStatus : invalidStatusArray){
            if(zt.contains(invalidStatus)){
                result = false;
                break;
            }
        }
        return result;
    }

}
