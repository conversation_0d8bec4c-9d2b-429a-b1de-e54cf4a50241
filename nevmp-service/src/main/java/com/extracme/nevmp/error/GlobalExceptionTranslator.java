package com.extracme.nevmp.error;

import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import com.extracme.nevmp.api.BaseResponse;
import com.extracme.nevmp.api.ResultCode;

/**
 * <AUTHOR>
 * @Description 异常处理转化类
 */
@RestControllerAdvice
public class GlobalExceptionTranslator {

    Logger logger = LoggerFactory.getLogger(GlobalExceptionTranslator.class);


    @ExceptionHandler(ServiceException.class)
    public BaseResponse handlerError(ServiceException e){
        logger.warn("Service Exception", e);
        return BaseResponse
                .builder()
                .resultCode(e.getResultCode())
                .message(e.getMessage())
                .build();
    }

    @ExceptionHandler(PermissionDeniedException.class)
    public BaseResponse handleError(PermissionDeniedException e) {
        logger.warn("Permission Denied", e);
        return BaseResponse
                .builder()
                .resultCode(e.getResultCode())
                .message(e.getMessage())
                .build();
    }

    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public BaseResponse handleError(MethodArgumentTypeMismatchException e) {
        logger.warn("Method Argument Type Mismatch", e);
        String message = String.format("Method Argument Type Mismatch: %s", e.getName());
        return BaseResponse
                .builder()
                .resultCode(ResultCode.PARAM_TYPE_ERROR)
                .message(message)
                .build();
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public BaseResponse handleError(HttpServletRequest request,MethodArgumentNotValidException e) {
        logger.warn("Method Argument Not Valid：" + request.getRequestURI(), e);
        BindingResult result = e.getBindingResult();
        FieldError error = result.getFieldError();
        return BaseResponse
                .builder()
                .resultCode(ResultCode.PARAM_VALID_ERROR)
                .message(error.getDefaultMessage())
                .build();
    }
    @ExceptionHandler(NoTraceException.class)
    public BaseResponse handleError(NoTraceException e) {
        return BaseResponse
                .builder()
                .resultCode(ResultCode.INTERNAL_SERVER_ERROR)
                .message(e.getMessage())
                .build();
    }

    @ExceptionHandler(Throwable.class)
    public BaseResponse handleError(Throwable e) {
        logger.error("Internal Server Error", e);
        return BaseResponse
                .builder()
                .resultCode(ResultCode.INTERNAL_SERVER_ERROR)
                .message(e.getMessage())
                .build();
    }
}
