package com.extracme.nevmp.memcache;

import com.danga.MemCached.MemCachedClient;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.DependsOn;

import java.util.Date;

@DependsOn("memcachePool")
@SpringBootTest(webEnvironment= SpringBootTest.WebEnvironment.NONE)
public class MemcacheTest {

    @Test
    public void get(){
        MemCachedClient memCachedClient = new MemCachedClient();
        boolean test = memCachedClient.set("test", 1L,new Date(200 * 1000));
        System.out.println(test);
        memCachedClient.add("test",2L, new Date(200 * 1000));
        System.out.println(memCachedClient.get("test"));
    }
}
