
**swagger-bootstrap-ui-前后端api接口文档**


**简介**：

**HOST**:************:7691


**联系人**:



**Version**:


**接口路径**：/v2/api-docs


# account-controller

## 修改密码


**接口描述**:


**接口地址**:`/account/changePassword`


**请求方式**：`POST`


**consumes**:`["application/json"]`


**produces**:`["*/*"]`


**请求示例**：
```json
{
	"confirmPassword": "",
	"newPassword": "",
	"oldPassword": ""
}
```


**请求参数**：

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|changePasswordVO| changePasswordVO  | body | true |ChangePasswordVO  | ChangePasswordVO   |

**schema属性说明**



**ChangePasswordVO**

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|confirmPassword| 新密码  | body | true |string  |    |
|newPassword| 新密码  | body | true |string  |    |
|oldPassword| 旧密码  | body | true |string  |    |

**响应示例**:

```json
{
	"message": "",
	"resultCode": "",
	"success": true
}
```

**响应参数**:


| 参数名称         | 参数说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
|message| 失败原因  |string  |    |
|resultCode| 请求结果CODE码,可用值:SUCCESS,WARN,UN_AUTHORIZED,FAILURE,PARAM_VALID_ERROR,PARAM_TYPE_ERROR,INTERNAL_SERVER_ERROR  |string  |    |
|success| 是否请求成功  |boolean  |    |





**响应状态**:


| 状态码         | 说明                            |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200 | OK  |BaseResponse|
| 201 | Created  ||
| 401 | Unauthorized  ||
| 403 | Forbidden  ||
| 404 | Not Found  ||
## 找回密码


**接口描述**:


**接口地址**:`/account/findPassword`


**请求方式**：`POST`


**consumes**:`["application/json"]`


**produces**:`["*/*"]`


**请求示例**：
```json
{
	"mobilePhone": "",
	"newPassword": "",
	"verifyCode": ""
}
```


**请求参数**：

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|findPasswordVO| findPasswordVO  | body | true |FindPasswordVO  | FindPasswordVO   |

**schema属性说明**



**FindPasswordVO**

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|mobilePhone| 手机号  | body | true |string  |    |
|newPassword| 新密码  | body | true |string  |    |
|verifyCode| 验证码  | body | true |string  |    |

**响应示例**:

```json
{
	"message": "",
	"resultCode": "",
	"success": true
}
```

**响应参数**:


| 参数名称         | 参数说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
|message| 失败原因  |string  |    |
|resultCode| 请求结果CODE码,可用值:SUCCESS,WARN,UN_AUTHORIZED,FAILURE,PARAM_VALID_ERROR,PARAM_TYPE_ERROR,INTERNAL_SERVER_ERROR  |string  |    |
|success| 是否请求成功  |boolean  |    |





**响应状态**:


| 状态码         | 说明                            |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200 | OK  |BaseResponse|
| 201 | Created  ||
| 401 | Unauthorized  ||
| 403 | Forbidden  ||
| 404 | Not Found  ||
## 获取openId


**接口描述**:


**接口地址**:`/account/getOpenId`


**请求方式**：`POST`


**consumes**:`["application/json"]`


**produces**:`["*/*"]`


**请求示例**：
```json
{
	"jsCode": ""
}
```


**请求参数**：

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|getOpenIdVO| getOpenIdVO  | body | true |GetOpenIdVO  | GetOpenIdVO   |

**schema属性说明**



**GetOpenIdVO**

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|jsCode| jsCode  | body | true |string  |    |

**响应示例**:

```json
{
	"message": "",
	"openId": "",
	"resultCode": "",
	"success": true
}
```

**响应参数**:


| 参数名称         | 参数说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
|message| 失败原因  |string  |    |
|openId|   |string  |    |
|resultCode| 请求结果CODE码,可用值:SUCCESS,WARN,UN_AUTHORIZED,FAILURE,PARAM_VALID_ERROR,PARAM_TYPE_ERROR,INTERNAL_SERVER_ERROR  |string  |    |
|success| 是否请求成功  |boolean  |    |





**响应状态**:


| 状态码         | 说明                            |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200 | OK  |GetOpenIdResponse|
| 201 | Created  ||
| 401 | Unauthorized  ||
| 403 | Forbidden  ||
| 404 | Not Found  ||
## 获取验证码


**接口描述**:


**接口地址**:`/account/getVerifyCode`


**请求方式**：`POST`


**consumes**:`["application/json"]`


**produces**:`["*/*"]`


**请求示例**：
```json
{
	"messageType": 0,
	"mobilePhone": ""
}
```


**请求参数**：

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|getVerifyCodeVO| getVerifyCodeVO  | body | true |GetVerifyCodeVO  | GetVerifyCodeVO   |

**schema属性说明**



**GetVerifyCodeVO**

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|messageType| 短信类型 1:找回密码 2:注册,可用值:1,2  | body | true |integer(int32)  |    |
|mobilePhone| 手机号  | body | true |string  |    |

**响应示例**:

```json
{
	"message": "",
	"resultCode": "",
	"success": true
}
```

**响应参数**:


| 参数名称         | 参数说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
|message| 失败原因  |string  |    |
|resultCode| 请求结果CODE码,可用值:SUCCESS,WARN,UN_AUTHORIZED,FAILURE,PARAM_VALID_ERROR,PARAM_TYPE_ERROR,INTERNAL_SERVER_ERROR  |string  |    |
|success| 是否请求成功  |boolean  |    |





**响应状态**:


| 状态码         | 说明                            |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200 | OK  |BaseResponse|
| 201 | Created  ||
| 401 | Unauthorized  ||
| 403 | Forbidden  ||
| 404 | Not Found  ||
## 是否登录


**接口描述**:


**接口地址**:`/account/isLogin`


**请求方式**：`GET`


**consumes**:``


**produces**:`["*/*"]`



**请求参数**：

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|openId| 微信openId  | header | true |string  |    |

**响应示例**:

```json
{
	"message": "",
	"resultCode": "",
	"success": true
}
```

**响应参数**:


| 参数名称         | 参数说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
|message| 失败原因  |string  |    |
|resultCode| 请求结果CODE码,可用值:SUCCESS,WARN,UN_AUTHORIZED,FAILURE,PARAM_VALID_ERROR,PARAM_TYPE_ERROR,INTERNAL_SERVER_ERROR  |string  |    |
|success| 是否请求成功  |boolean  |    |





**响应状态**:


| 状态码         | 说明                            |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200 | OK  |BaseResponse|
| 401 | Unauthorized  ||
| 403 | Forbidden  ||
| 404 | Not Found  ||
## 登录


**接口描述**:


**接口地址**:`/account/login`


**请求方式**：`POST`


**consumes**:`["application/json"]`


**produces**:`["*/*"]`


**请求示例**：
```json
{
	"loginName": "",
	"openId": "123",
	"password": ""
}
```


**请求参数**：

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|loginVO| loginVO  | body | true |LoginVO  | LoginVO   |

**schema属性说明**



**LoginVO**

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|loginName| 账号（手机号）  | body | true |string  |    |
|openId| 微信小程序openId  | body | true |string  |    |
|password| 密码  | body | true |string  |    |

**响应示例**:

```json
{
	"message": "",
	"resultCode": "",
	"success": true
}
```

**响应参数**:


| 参数名称         | 参数说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
|message| 失败原因  |string  |    |
|resultCode| 请求结果CODE码,可用值:SUCCESS,WARN,UN_AUTHORIZED,FAILURE,PARAM_VALID_ERROR,PARAM_TYPE_ERROR,INTERNAL_SERVER_ERROR  |string  |    |
|success| 是否请求成功  |boolean  |    |





**响应状态**:


| 状态码         | 说明                            |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200 | OK  |BaseResponse|
| 201 | Created  ||
| 401 | Unauthorized  ||
| 403 | Forbidden  ||
| 404 | Not Found  ||
## 登出


**接口描述**:


**接口地址**:`/account/logout`


**请求方式**：`GET`


**consumes**:``


**produces**:`["*/*"]`



**请求参数**：

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|openId| 微信openId  | header | true |string  |    |

**响应示例**:

```json
{
	"message": "",
	"resultCode": "",
	"success": true
}
```

**响应参数**:


| 参数名称         | 参数说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
|message| 失败原因  |string  |    |
|resultCode| 请求结果CODE码,可用值:SUCCESS,WARN,UN_AUTHORIZED,FAILURE,PARAM_VALID_ERROR,PARAM_TYPE_ERROR,INTERNAL_SERVER_ERROR  |string  |    |
|success| 是否请求成功  |boolean  |    |





**响应状态**:


| 状态码         | 说明                            |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200 | OK  |BaseResponse|
| 401 | Unauthorized  ||
| 403 | Forbidden  ||
| 404 | Not Found  ||
## 注册账号


**接口描述**:


**接口地址**:`/account/register`


**请求方式**：`POST`


**consumes**:`["application/json"]`


**produces**:`["*/*"]`


**请求示例**：
```json
{
	"mobilePhone": "138XXXXXXXX",
	"password": "",
	"verifyCode": ""
}
```


**请求参数**：

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|registerVO| registerVO  | body | true |RegisterVO  | RegisterVO   |

**schema属性说明**



**RegisterVO**

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|mobilePhone| 手机号  | body | true |string  |    |
|password| 密码  | body | true |string  |    |
|verifyCode| 验证码  | body | true |string  |    |

**响应示例**:

```json
{
	"message": "",
	"resultCode": "",
	"success": true
}
```

**响应参数**:


| 参数名称         | 参数说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
|message| 失败原因  |string  |    |
|resultCode| 请求结果CODE码,可用值:SUCCESS,WARN,UN_AUTHORIZED,FAILURE,PARAM_VALID_ERROR,PARAM_TYPE_ERROR,INTERNAL_SERVER_ERROR  |string  |    |
|success| 是否请求成功  |boolean  |    |





**响应状态**:


| 状态码         | 说明                            |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200 | OK  |BaseResponse|
| 201 | Created  ||
| 401 | Unauthorized  ||
| 403 | Forbidden  ||
| 404 | Not Found  ||
# basic-error-controller

## errorHtml


**接口描述**:


**接口地址**:`/error`


**请求方式**：`GET`


**consumes**:``


**produces**:`["text/html"]`



**请求参数**：
暂无



**响应示例**:

```json
{
	"empty": true,
	"model": {},
	"modelMap": {},
	"reference": true,
	"status": "",
	"view": {
		"contentType": ""
	},
	"viewName": ""
}
```

**响应参数**:


| 参数名称         | 参数说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
|empty|   |boolean  |    |
|model|   |object  |    |
|modelMap|   |object  |    |
|reference|   |boolean  |    |
|status| 可用值:100 CONTINUE,101 SWITCHING_PROTOCOLS,102 PROCESSING,103 CHECKPOINT,200 OK,201 CREATED,202 ACCEPTED,203 NON_AUTHORITATIVE_INFORMATION,204 NO_CONTENT,205 RESET_CONTENT,206 PARTIAL_CONTENT,207 MULTI_STATUS,208 ALREADY_REPORTED,226 IM_USED,300 MULTIPLE_CHOICES,301 MOVED_PERMANENTLY,302 FOUND,302 MOVED_TEMPORARILY,303 SEE_OTHER,304 NOT_MODIFIED,305 USE_PROXY,307 TEMPORARY_REDIRECT,308 PERMANENT_REDIRECT,400 BAD_REQUEST,401 UNAUTHORIZED,402 PAYMENT_REQUIRED,403 FORBIDDEN,404 NOT_FOUND,405 METHOD_NOT_ALLOWED,406 NOT_ACCEPTABLE,407 PROXY_AUTHENTICATION_REQUIRED,408 REQUEST_TIMEOUT,409 CONFLICT,410 GONE,411 LENGTH_REQUIRED,412 PRECONDITION_FAILED,413 PAYLOAD_TOO_LARGE,413 REQUEST_ENTITY_TOO_LARGE,414 URI_TOO_LONG,414 REQUEST_URI_TOO_LONG,415 UNSUPPORTED_MEDIA_TYPE,416 REQUESTED_RANGE_NOT_SATISFIABLE,417 EXPECTATION_FAILED,418 I_AM_A_TEAPOT,419 INSUFFICIENT_SPACE_ON_RESOURCE,420 METHOD_FAILURE,421 DESTINATION_LOCKED,422 UNPROCESSABLE_ENTITY,423 LOCKED,424 FAILED_DEPENDENCY,425 TOO_EARLY,426 UPGRADE_REQUIRED,428 PRECONDITION_REQUIRED,429 TOO_MANY_REQUESTS,431 REQUEST_HEADER_FIELDS_TOO_LARGE,451 UNAVAILABLE_FOR_LEGAL_REASONS,500 INTERNAL_SERVER_ERROR,501 NOT_IMPLEMENTED,502 BAD_GATEWAY,503 SERVICE_UNAVAILABLE,504 GATEWAY_TIMEOUT,505 HTTP_VERSION_NOT_SUPPORTED,506 VARIANT_ALSO_NEGOTIATES,507 INSUFFICIENT_STORAGE,508 LOOP_DETECTED,509 BANDWIDTH_LIMIT_EXCEEDED,510 NOT_EXTENDED,511 NETWORK_AUTHENTICATION_REQUIRED  |string  |    |
|view|   |View  | View   |
|viewName|   |string  |    |



**schema属性说明**




**View**

| 参数名称         | 参数说明                             |    类型 |  schema |
| ------------ | ------------------|--------|----------- |
|contentType |    |string  |    |

**响应状态**:


| 状态码         | 说明                            |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200 | OK  |ModelAndView|
| 401 | Unauthorized  ||
| 403 | Forbidden  ||
| 404 | Not Found  ||
## errorHtml


**接口描述**:


**接口地址**:`/error`


**请求方式**：`POST`


**consumes**:`["application/json"]`


**produces**:`["text/html"]`



**请求参数**：
暂无



**响应示例**:

```json
{
	"empty": true,
	"model": {},
	"modelMap": {},
	"reference": true,
	"status": "",
	"view": {
		"contentType": ""
	},
	"viewName": ""
}
```

**响应参数**:


| 参数名称         | 参数说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
|empty|   |boolean  |    |
|model|   |object  |    |
|modelMap|   |object  |    |
|reference|   |boolean  |    |
|status| 可用值:100 CONTINUE,101 SWITCHING_PROTOCOLS,102 PROCESSING,103 CHECKPOINT,200 OK,201 CREATED,202 ACCEPTED,203 NON_AUTHORITATIVE_INFORMATION,204 NO_CONTENT,205 RESET_CONTENT,206 PARTIAL_CONTENT,207 MULTI_STATUS,208 ALREADY_REPORTED,226 IM_USED,300 MULTIPLE_CHOICES,301 MOVED_PERMANENTLY,302 FOUND,302 MOVED_TEMPORARILY,303 SEE_OTHER,304 NOT_MODIFIED,305 USE_PROXY,307 TEMPORARY_REDIRECT,308 PERMANENT_REDIRECT,400 BAD_REQUEST,401 UNAUTHORIZED,402 PAYMENT_REQUIRED,403 FORBIDDEN,404 NOT_FOUND,405 METHOD_NOT_ALLOWED,406 NOT_ACCEPTABLE,407 PROXY_AUTHENTICATION_REQUIRED,408 REQUEST_TIMEOUT,409 CONFLICT,410 GONE,411 LENGTH_REQUIRED,412 PRECONDITION_FAILED,413 PAYLOAD_TOO_LARGE,413 REQUEST_ENTITY_TOO_LARGE,414 URI_TOO_LONG,414 REQUEST_URI_TOO_LONG,415 UNSUPPORTED_MEDIA_TYPE,416 REQUESTED_RANGE_NOT_SATISFIABLE,417 EXPECTATION_FAILED,418 I_AM_A_TEAPOT,419 INSUFFICIENT_SPACE_ON_RESOURCE,420 METHOD_FAILURE,421 DESTINATION_LOCKED,422 UNPROCESSABLE_ENTITY,423 LOCKED,424 FAILED_DEPENDENCY,425 TOO_EARLY,426 UPGRADE_REQUIRED,428 PRECONDITION_REQUIRED,429 TOO_MANY_REQUESTS,431 REQUEST_HEADER_FIELDS_TOO_LARGE,451 UNAVAILABLE_FOR_LEGAL_REASONS,500 INTERNAL_SERVER_ERROR,501 NOT_IMPLEMENTED,502 BAD_GATEWAY,503 SERVICE_UNAVAILABLE,504 GATEWAY_TIMEOUT,505 HTTP_VERSION_NOT_SUPPORTED,506 VARIANT_ALSO_NEGOTIATES,507 INSUFFICIENT_STORAGE,508 LOOP_DETECTED,509 BANDWIDTH_LIMIT_EXCEEDED,510 NOT_EXTENDED,511 NETWORK_AUTHENTICATION_REQUIRED  |string  |    |
|view|   |View  | View   |
|viewName|   |string  |    |



**schema属性说明**




**View**

| 参数名称         | 参数说明                             |    类型 |  schema |
| ------------ | ------------------|--------|----------- |
|contentType |    |string  |    |

**响应状态**:


| 状态码         | 说明                            |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200 | OK  |ModelAndView|
| 201 | Created  ||
| 401 | Unauthorized  ||
| 403 | Forbidden  ||
| 404 | Not Found  ||
## errorHtml


**接口描述**:


**接口地址**:`/error`


**请求方式**：`PUT`


**consumes**:`["application/json"]`


**produces**:`["text/html"]`



**请求参数**：
暂无



**响应示例**:

```json
{
	"empty": true,
	"model": {},
	"modelMap": {},
	"reference": true,
	"status": "",
	"view": {
		"contentType": ""
	},
	"viewName": ""
}
```

**响应参数**:


| 参数名称         | 参数说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
|empty|   |boolean  |    |
|model|   |object  |    |
|modelMap|   |object  |    |
|reference|   |boolean  |    |
|status| 可用值:100 CONTINUE,101 SWITCHING_PROTOCOLS,102 PROCESSING,103 CHECKPOINT,200 OK,201 CREATED,202 ACCEPTED,203 NON_AUTHORITATIVE_INFORMATION,204 NO_CONTENT,205 RESET_CONTENT,206 PARTIAL_CONTENT,207 MULTI_STATUS,208 ALREADY_REPORTED,226 IM_USED,300 MULTIPLE_CHOICES,301 MOVED_PERMANENTLY,302 FOUND,302 MOVED_TEMPORARILY,303 SEE_OTHER,304 NOT_MODIFIED,305 USE_PROXY,307 TEMPORARY_REDIRECT,308 PERMANENT_REDIRECT,400 BAD_REQUEST,401 UNAUTHORIZED,402 PAYMENT_REQUIRED,403 FORBIDDEN,404 NOT_FOUND,405 METHOD_NOT_ALLOWED,406 NOT_ACCEPTABLE,407 PROXY_AUTHENTICATION_REQUIRED,408 REQUEST_TIMEOUT,409 CONFLICT,410 GONE,411 LENGTH_REQUIRED,412 PRECONDITION_FAILED,413 PAYLOAD_TOO_LARGE,413 REQUEST_ENTITY_TOO_LARGE,414 URI_TOO_LONG,414 REQUEST_URI_TOO_LONG,415 UNSUPPORTED_MEDIA_TYPE,416 REQUESTED_RANGE_NOT_SATISFIABLE,417 EXPECTATION_FAILED,418 I_AM_A_TEAPOT,419 INSUFFICIENT_SPACE_ON_RESOURCE,420 METHOD_FAILURE,421 DESTINATION_LOCKED,422 UNPROCESSABLE_ENTITY,423 LOCKED,424 FAILED_DEPENDENCY,425 TOO_EARLY,426 UPGRADE_REQUIRED,428 PRECONDITION_REQUIRED,429 TOO_MANY_REQUESTS,431 REQUEST_HEADER_FIELDS_TOO_LARGE,451 UNAVAILABLE_FOR_LEGAL_REASONS,500 INTERNAL_SERVER_ERROR,501 NOT_IMPLEMENTED,502 BAD_GATEWAY,503 SERVICE_UNAVAILABLE,504 GATEWAY_TIMEOUT,505 HTTP_VERSION_NOT_SUPPORTED,506 VARIANT_ALSO_NEGOTIATES,507 INSUFFICIENT_STORAGE,508 LOOP_DETECTED,509 BANDWIDTH_LIMIT_EXCEEDED,510 NOT_EXTENDED,511 NETWORK_AUTHENTICATION_REQUIRED  |string  |    |
|view|   |View  | View   |
|viewName|   |string  |    |



**schema属性说明**




**View**

| 参数名称         | 参数说明                             |    类型 |  schema |
| ------------ | ------------------|--------|----------- |
|contentType |    |string  |    |

**响应状态**:


| 状态码         | 说明                            |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200 | OK  |ModelAndView|
| 201 | Created  ||
| 401 | Unauthorized  ||
| 403 | Forbidden  ||
| 404 | Not Found  ||
## errorHtml


**接口描述**:


**接口地址**:`/error`


**请求方式**：`DELETE`


**consumes**:``


**produces**:`["text/html"]`



**请求参数**：
暂无



**响应示例**:

```json
{
	"empty": true,
	"model": {},
	"modelMap": {},
	"reference": true,
	"status": "",
	"view": {
		"contentType": ""
	},
	"viewName": ""
}
```

**响应参数**:


| 参数名称         | 参数说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
|empty|   |boolean  |    |
|model|   |object  |    |
|modelMap|   |object  |    |
|reference|   |boolean  |    |
|status| 可用值:100 CONTINUE,101 SWITCHING_PROTOCOLS,102 PROCESSING,103 CHECKPOINT,200 OK,201 CREATED,202 ACCEPTED,203 NON_AUTHORITATIVE_INFORMATION,204 NO_CONTENT,205 RESET_CONTENT,206 PARTIAL_CONTENT,207 MULTI_STATUS,208 ALREADY_REPORTED,226 IM_USED,300 MULTIPLE_CHOICES,301 MOVED_PERMANENTLY,302 FOUND,302 MOVED_TEMPORARILY,303 SEE_OTHER,304 NOT_MODIFIED,305 USE_PROXY,307 TEMPORARY_REDIRECT,308 PERMANENT_REDIRECT,400 BAD_REQUEST,401 UNAUTHORIZED,402 PAYMENT_REQUIRED,403 FORBIDDEN,404 NOT_FOUND,405 METHOD_NOT_ALLOWED,406 NOT_ACCEPTABLE,407 PROXY_AUTHENTICATION_REQUIRED,408 REQUEST_TIMEOUT,409 CONFLICT,410 GONE,411 LENGTH_REQUIRED,412 PRECONDITION_FAILED,413 PAYLOAD_TOO_LARGE,413 REQUEST_ENTITY_TOO_LARGE,414 URI_TOO_LONG,414 REQUEST_URI_TOO_LONG,415 UNSUPPORTED_MEDIA_TYPE,416 REQUESTED_RANGE_NOT_SATISFIABLE,417 EXPECTATION_FAILED,418 I_AM_A_TEAPOT,419 INSUFFICIENT_SPACE_ON_RESOURCE,420 METHOD_FAILURE,421 DESTINATION_LOCKED,422 UNPROCESSABLE_ENTITY,423 LOCKED,424 FAILED_DEPENDENCY,425 TOO_EARLY,426 UPGRADE_REQUIRED,428 PRECONDITION_REQUIRED,429 TOO_MANY_REQUESTS,431 REQUEST_HEADER_FIELDS_TOO_LARGE,451 UNAVAILABLE_FOR_LEGAL_REASONS,500 INTERNAL_SERVER_ERROR,501 NOT_IMPLEMENTED,502 BAD_GATEWAY,503 SERVICE_UNAVAILABLE,504 GATEWAY_TIMEOUT,505 HTTP_VERSION_NOT_SUPPORTED,506 VARIANT_ALSO_NEGOTIATES,507 INSUFFICIENT_STORAGE,508 LOOP_DETECTED,509 BANDWIDTH_LIMIT_EXCEEDED,510 NOT_EXTENDED,511 NETWORK_AUTHENTICATION_REQUIRED  |string  |    |
|view|   |View  | View   |
|viewName|   |string  |    |



**schema属性说明**




**View**

| 参数名称         | 参数说明                             |    类型 |  schema |
| ------------ | ------------------|--------|----------- |
|contentType |    |string  |    |

**响应状态**:


| 状态码         | 说明                            |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200 | OK  |ModelAndView|
| 204 | No Content  ||
| 401 | Unauthorized  ||
| 403 | Forbidden  ||
## errorHtml


**接口描述**:


**接口地址**:`/error`


**请求方式**：`PATCH`


**consumes**:`["application/json"]`


**produces**:`["text/html"]`



**请求参数**：
暂无



**响应示例**:

```json
{
	"empty": true,
	"model": {},
	"modelMap": {},
	"reference": true,
	"status": "",
	"view": {
		"contentType": ""
	},
	"viewName": ""
}
```

**响应参数**:


| 参数名称         | 参数说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
|empty|   |boolean  |    |
|model|   |object  |    |
|modelMap|   |object  |    |
|reference|   |boolean  |    |
|status| 可用值:100 CONTINUE,101 SWITCHING_PROTOCOLS,102 PROCESSING,103 CHECKPOINT,200 OK,201 CREATED,202 ACCEPTED,203 NON_AUTHORITATIVE_INFORMATION,204 NO_CONTENT,205 RESET_CONTENT,206 PARTIAL_CONTENT,207 MULTI_STATUS,208 ALREADY_REPORTED,226 IM_USED,300 MULTIPLE_CHOICES,301 MOVED_PERMANENTLY,302 FOUND,302 MOVED_TEMPORARILY,303 SEE_OTHER,304 NOT_MODIFIED,305 USE_PROXY,307 TEMPORARY_REDIRECT,308 PERMANENT_REDIRECT,400 BAD_REQUEST,401 UNAUTHORIZED,402 PAYMENT_REQUIRED,403 FORBIDDEN,404 NOT_FOUND,405 METHOD_NOT_ALLOWED,406 NOT_ACCEPTABLE,407 PROXY_AUTHENTICATION_REQUIRED,408 REQUEST_TIMEOUT,409 CONFLICT,410 GONE,411 LENGTH_REQUIRED,412 PRECONDITION_FAILED,413 PAYLOAD_TOO_LARGE,413 REQUEST_ENTITY_TOO_LARGE,414 URI_TOO_LONG,414 REQUEST_URI_TOO_LONG,415 UNSUPPORTED_MEDIA_TYPE,416 REQUESTED_RANGE_NOT_SATISFIABLE,417 EXPECTATION_FAILED,418 I_AM_A_TEAPOT,419 INSUFFICIENT_SPACE_ON_RESOURCE,420 METHOD_FAILURE,421 DESTINATION_LOCKED,422 UNPROCESSABLE_ENTITY,423 LOCKED,424 FAILED_DEPENDENCY,425 TOO_EARLY,426 UPGRADE_REQUIRED,428 PRECONDITION_REQUIRED,429 TOO_MANY_REQUESTS,431 REQUEST_HEADER_FIELDS_TOO_LARGE,451 UNAVAILABLE_FOR_LEGAL_REASONS,500 INTERNAL_SERVER_ERROR,501 NOT_IMPLEMENTED,502 BAD_GATEWAY,503 SERVICE_UNAVAILABLE,504 GATEWAY_TIMEOUT,505 HTTP_VERSION_NOT_SUPPORTED,506 VARIANT_ALSO_NEGOTIATES,507 INSUFFICIENT_STORAGE,508 LOOP_DETECTED,509 BANDWIDTH_LIMIT_EXCEEDED,510 NOT_EXTENDED,511 NETWORK_AUTHENTICATION_REQUIRED  |string  |    |
|view|   |View  | View   |
|viewName|   |string  |    |



**schema属性说明**




**View**

| 参数名称         | 参数说明                             |    类型 |  schema |
| ------------ | ------------------|--------|----------- |
|contentType |    |string  |    |

**响应状态**:


| 状态码         | 说明                            |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200 | OK  |ModelAndView|
| 204 | No Content  ||
| 401 | Unauthorized  ||
| 403 | Forbidden  ||
## errorHtml


**接口描述**:


**接口地址**:`/error`


**请求方式**：`OPTIONS`


**consumes**:`["application/json"]`


**produces**:`["text/html"]`



**请求参数**：
暂无



**响应示例**:

```json
{
	"empty": true,
	"model": {},
	"modelMap": {},
	"reference": true,
	"status": "",
	"view": {
		"contentType": ""
	},
	"viewName": ""
}
```

**响应参数**:


| 参数名称         | 参数说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
|empty|   |boolean  |    |
|model|   |object  |    |
|modelMap|   |object  |    |
|reference|   |boolean  |    |
|status| 可用值:100 CONTINUE,101 SWITCHING_PROTOCOLS,102 PROCESSING,103 CHECKPOINT,200 OK,201 CREATED,202 ACCEPTED,203 NON_AUTHORITATIVE_INFORMATION,204 NO_CONTENT,205 RESET_CONTENT,206 PARTIAL_CONTENT,207 MULTI_STATUS,208 ALREADY_REPORTED,226 IM_USED,300 MULTIPLE_CHOICES,301 MOVED_PERMANENTLY,302 FOUND,302 MOVED_TEMPORARILY,303 SEE_OTHER,304 NOT_MODIFIED,305 USE_PROXY,307 TEMPORARY_REDIRECT,308 PERMANENT_REDIRECT,400 BAD_REQUEST,401 UNAUTHORIZED,402 PAYMENT_REQUIRED,403 FORBIDDEN,404 NOT_FOUND,405 METHOD_NOT_ALLOWED,406 NOT_ACCEPTABLE,407 PROXY_AUTHENTICATION_REQUIRED,408 REQUEST_TIMEOUT,409 CONFLICT,410 GONE,411 LENGTH_REQUIRED,412 PRECONDITION_FAILED,413 PAYLOAD_TOO_LARGE,413 REQUEST_ENTITY_TOO_LARGE,414 URI_TOO_LONG,414 REQUEST_URI_TOO_LONG,415 UNSUPPORTED_MEDIA_TYPE,416 REQUESTED_RANGE_NOT_SATISFIABLE,417 EXPECTATION_FAILED,418 I_AM_A_TEAPOT,419 INSUFFICIENT_SPACE_ON_RESOURCE,420 METHOD_FAILURE,421 DESTINATION_LOCKED,422 UNPROCESSABLE_ENTITY,423 LOCKED,424 FAILED_DEPENDENCY,425 TOO_EARLY,426 UPGRADE_REQUIRED,428 PRECONDITION_REQUIRED,429 TOO_MANY_REQUESTS,431 REQUEST_HEADER_FIELDS_TOO_LARGE,451 UNAVAILABLE_FOR_LEGAL_REASONS,500 INTERNAL_SERVER_ERROR,501 NOT_IMPLEMENTED,502 BAD_GATEWAY,503 SERVICE_UNAVAILABLE,504 GATEWAY_TIMEOUT,505 HTTP_VERSION_NOT_SUPPORTED,506 VARIANT_ALSO_NEGOTIATES,507 INSUFFICIENT_STORAGE,508 LOOP_DETECTED,509 BANDWIDTH_LIMIT_EXCEEDED,510 NOT_EXTENDED,511 NETWORK_AUTHENTICATION_REQUIRED  |string  |    |
|view|   |View  | View   |
|viewName|   |string  |    |



**schema属性说明**




**View**

| 参数名称         | 参数说明                             |    类型 |  schema |
| ------------ | ------------------|--------|----------- |
|contentType |    |string  |    |

**响应状态**:


| 状态码         | 说明                            |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200 | OK  |ModelAndView|
| 204 | No Content  ||
| 401 | Unauthorized  ||
| 403 | Forbidden  ||
## errorHtml


**接口描述**:


**接口地址**:`/error`


**请求方式**：`HEAD`


**consumes**:`["application/json"]`


**produces**:`["text/html"]`



**请求参数**：
暂无



**响应示例**:

```json
{
	"empty": true,
	"model": {},
	"modelMap": {},
	"reference": true,
	"status": "",
	"view": {
		"contentType": ""
	},
	"viewName": ""
}
```

**响应参数**:


| 参数名称         | 参数说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
|empty|   |boolean  |    |
|model|   |object  |    |
|modelMap|   |object  |    |
|reference|   |boolean  |    |
|status| 可用值:100 CONTINUE,101 SWITCHING_PROTOCOLS,102 PROCESSING,103 CHECKPOINT,200 OK,201 CREATED,202 ACCEPTED,203 NON_AUTHORITATIVE_INFORMATION,204 NO_CONTENT,205 RESET_CONTENT,206 PARTIAL_CONTENT,207 MULTI_STATUS,208 ALREADY_REPORTED,226 IM_USED,300 MULTIPLE_CHOICES,301 MOVED_PERMANENTLY,302 FOUND,302 MOVED_TEMPORARILY,303 SEE_OTHER,304 NOT_MODIFIED,305 USE_PROXY,307 TEMPORARY_REDIRECT,308 PERMANENT_REDIRECT,400 BAD_REQUEST,401 UNAUTHORIZED,402 PAYMENT_REQUIRED,403 FORBIDDEN,404 NOT_FOUND,405 METHOD_NOT_ALLOWED,406 NOT_ACCEPTABLE,407 PROXY_AUTHENTICATION_REQUIRED,408 REQUEST_TIMEOUT,409 CONFLICT,410 GONE,411 LENGTH_REQUIRED,412 PRECONDITION_FAILED,413 PAYLOAD_TOO_LARGE,413 REQUEST_ENTITY_TOO_LARGE,414 URI_TOO_LONG,414 REQUEST_URI_TOO_LONG,415 UNSUPPORTED_MEDIA_TYPE,416 REQUESTED_RANGE_NOT_SATISFIABLE,417 EXPECTATION_FAILED,418 I_AM_A_TEAPOT,419 INSUFFICIENT_SPACE_ON_RESOURCE,420 METHOD_FAILURE,421 DESTINATION_LOCKED,422 UNPROCESSABLE_ENTITY,423 LOCKED,424 FAILED_DEPENDENCY,425 TOO_EARLY,426 UPGRADE_REQUIRED,428 PRECONDITION_REQUIRED,429 TOO_MANY_REQUESTS,431 REQUEST_HEADER_FIELDS_TOO_LARGE,451 UNAVAILABLE_FOR_LEGAL_REASONS,500 INTERNAL_SERVER_ERROR,501 NOT_IMPLEMENTED,502 BAD_GATEWAY,503 SERVICE_UNAVAILABLE,504 GATEWAY_TIMEOUT,505 HTTP_VERSION_NOT_SUPPORTED,506 VARIANT_ALSO_NEGOTIATES,507 INSUFFICIENT_STORAGE,508 LOOP_DETECTED,509 BANDWIDTH_LIMIT_EXCEEDED,510 NOT_EXTENDED,511 NETWORK_AUTHENTICATION_REQUIRED  |string  |    |
|view|   |View  | View   |
|viewName|   |string  |    |



**schema属性说明**




**View**

| 参数名称         | 参数说明                             |    类型 |  schema |
| ------------ | ------------------|--------|----------- |
|contentType |    |string  |    |

**响应状态**:


| 状态码         | 说明                            |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200 | OK  |ModelAndView|
| 204 | No Content  ||
| 401 | Unauthorized  ||
| 403 | Forbidden  ||
# file-controller

## 上传文件

**接口描述**:fileType: 1:用户资质证明材料, 2:军官证明材料, 3:车辆合格证扫描件, 4:充电桩承诺书, 5:独立计量表扫描件, 6:证件扫描件, 7:购车发票扫描件, 8:社会信用代码证扫描件, 9:行驶证扫描件, 10:充电桩照片, 11:经销商授权证明, 12: 经销商续期授权证明, 20:车辆信息导入, 21:充电条件确认信息导入, 30:充电桩自查反馈表,  31: 充电桩铭牌 40:充电桩建设信息导入, 50:车型申请书, 51:车企车型证明材料, 52:数据中心车型证明材料, 53:车型评审报告, 54:车型质量抽查表, 55:车型参数表

**接口地址**:`/upload`


**请求方式**：`POST`


**consumes**:`["multipart/form-data"]`


**produces**:`["*/*"]`



**请求参数**：

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|file| file  | formData | true |file  |    |
|openId| 微信openId  | header | true |string  |    |
|type| type  | query | true |integer  |    |

**响应示例**:

```json
{
	"absoluteFilePath": "",
	"message": "",
	"relativeFilePath": "",
	"resultCode": "",
	"success": true
}
```

**响应参数**:


| 参数名称         | 参数说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
|absoluteFilePath| 文件绝对路径  |string  |    |
|message| 失败原因  |string  |    |
|relativeFilePath| 文件相对路径  |string  |    |
|resultCode| 请求结果CODE码,可用值:SUCCESS,WARN,UN_AUTHORIZED,FAILURE,PARAM_VALID_ERROR,PARAM_TYPE_ERROR,INTERNAL_SERVER_ERROR  |string  |    |
|success| 是否请求成功  |boolean  |    |





**响应状态**:


| 状态码         | 说明                            |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200 | OK  |FileResponse|
| 201 | Created  ||
| 401 | Unauthorized  ||
| 403 | Forbidden  ||
| 404 | Not Found  ||
# owner-qualification-controller

## 绑定购车资质信息


**接口描述**:


**接口地址**:`/ownerQualification/bindOwnerQualification`


**请求方式**：`POST`


**consumes**:`["application/json"]`


**produces**:`["*/*"]`



**请求参数**：

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|bindOwnerQualificationVO| bindOwnerQualificationVO  | body | true |BindOwnerQualificationVO  | BindOwnerQualificationVO   |
|memSeq| memSeq  | body | false |integer  |    |
|openId| 微信openId  | header | true |string  |    |

**schema属性说明**



**BindOwnerQualificationVO**

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|authId| 证件号  | body | true |string  |    |
|authType| 证件类型  | body | false |integer(int32)  |    |
|ownerType| 申请人性质  1:私人 2:法人  | body | true |integer(int32)  |    |

**响应示例**:

```json
{
	"message": "",
	"resultCode": "",
	"success": true
}
```

**响应参数**:


| 参数名称         | 参数说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
|message| 失败原因  |string  |    |
|resultCode| 请求结果CODE码,可用值:SUCCESS,WARN,UN_AUTHORIZED,FAILURE,PARAM_VALID_ERROR,PARAM_TYPE_ERROR,INTERNAL_SERVER_ERROR  |string  |    |
|success| 是否请求成功  |boolean  |    |





**响应状态**:


| 状态码         | 说明                            |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200 | OK  |BaseResponse|
| 201 | Created  ||
| 401 | Unauthorized  ||
| 403 | Forbidden  ||
| 404 | Not Found  ||
## 取消购车资质复核


**接口描述**:


**接口地址**:`/ownerQualification/cancelReconsiderOwnerQualification`


**请求方式**：`POST`


**consumes**:`["application/json"]`


**produces**:`["*/*"]`



**请求参数**：

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|memSeq| memSeq  | body | false |integer  |    |
|openId| 微信openId  | header | true |string  |    |

**响应示例**:

```json
{
	"message": "",
	"resultCode": "",
	"success": true
}
```

**响应参数**:


| 参数名称         | 参数说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
|message| 失败原因  |string  |    |
|resultCode| 请求结果CODE码,可用值:SUCCESS,WARN,UN_AUTHORIZED,FAILURE,PARAM_VALID_ERROR,PARAM_TYPE_ERROR,INTERNAL_SERVER_ERROR  |string  |    |
|success| 是否请求成功  |boolean  |    |





**响应状态**:


| 状态码         | 说明                            |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200 | OK  |BaseResponse|
| 201 | Created  ||
| 401 | Unauthorized  ||
| 403 | Forbidden  ||
| 404 | Not Found  ||
## 是否存在有效的购车资质


**接口描述**:


**接口地址**:`/ownerQualification/existValidQualification`


**请求方式**：`POST`


**consumes**:`["application/json"]`


**produces**:`["*/*"]`



**请求参数**：

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|existValidQualificationVO| existValidQualificationVO  | body | true |ExistValidQualificationVO  | ExistValidQualificationVO   |
|memSeq| memSeq  | body | false |integer  |    |
|openId| 微信openId  | header | true |string  |    |

**schema属性说明**



**ExistValidQualificationVO**

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|ownerType| 用户性质 1:私人 2:法人  | body | true |integer(int32)  |    |

**响应示例**:

```json
{
	"authId": "",
	"authType": 0,
	"exist": true,
	"message": "",
	"resultCode": "",
	"success": true
}
```

**响应参数**:


| 参数名称         | 参数说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
|authId| 证件号  |string  |    |
|authType| 证件类型  |integer(int32)  | integer(int32)   |
|exist| true:存在 false:不存在  |boolean  |    |
|message| 失败原因  |string  |    |
|resultCode| 请求结果CODE码,可用值:SUCCESS,WARN,UN_AUTHORIZED,FAILURE,PARAM_VALID_ERROR,PARAM_TYPE_ERROR,INTERNAL_SERVER_ERROR  |string  |    |
|success| 是否请求成功  |boolean  |    |





**响应状态**:


| 状态码         | 说明                            |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200 | OK  |ExistValidQualificationResponse|
| 201 | Created  ||
| 401 | Unauthorized  ||
| 403 | Forbidden  ||
| 404 | Not Found  ||
## 失效购车资质


**接口描述**:


**接口地址**:`/ownerQualification/invalidOwnerQualification`


**请求方式**：`POST`


**consumes**:`["application/json"]`


**produces**:`["*/*"]`



**请求参数**：

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|invalidOwnerQualificationVO| invalidOwnerQualificationVO  | body | true |InvalidOwnerQualificationVO  | InvalidOwnerQualificationVO   |
|memSeq| memSeq  | body | false |integer  |    |
|openId| 微信openId  | header | true |string  |    |

**schema属性说明**



**InvalidOwnerQualificationVO**

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|ownerType| 用户性质 1:私人 2:法人  | body | true |integer(int32)  |    |

**响应示例**:

```json
{
	"message": "",
	"resultCode": "",
	"success": true
}
```

**响应参数**:


| 参数名称         | 参数说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
|message| 失败原因  |string  |    |
|resultCode| 请求结果CODE码,可用值:SUCCESS,WARN,UN_AUTHORIZED,FAILURE,PARAM_VALID_ERROR,PARAM_TYPE_ERROR,INTERNAL_SERVER_ERROR  |string  |    |
|success| 是否请求成功  |boolean  |    |





**响应状态**:


| 状态码         | 说明                            |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200 | OK  |BaseResponse|
| 201 | Created  ||
| 401 | Unauthorized  ||
| 403 | Forbidden  ||
| 404 | Not Found  ||
## 获取购车资质信息


**接口描述**:


**接口地址**:`/ownerQualification/queryOwnerQualificationDetail`


**请求方式**：`POST`


**consumes**:`["application/json"]`


**produces**:`["*/*"]`



**请求参数**：

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|memSeq| memSeq  | body | false |integer  |    |
|openId| 微信openId  | header | true |string  |    |
|queryOwnerQualificationDetailVO| queryOwnerQualificationDetailVO  | body | true |QueryOwnerQualificationDetailVO  | QueryOwnerQualificationDetailVO   |

**schema属性说明**



**QueryOwnerQualificationDetailVO**

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|ownerType| 用户性质 1:私人 2:法人  | body | true |integer(int32)  |    |

**响应示例**:

```json
{
	"applyNo": "",
	"applyTime": "",
	"authId": "",
	"authType": 0,
	"canReconsider": true,
	"driverFileNo": "",
	"driverLicenseCode": "",
	"driverLicenseIssuingOrganization": "",
	"driverLicenseIssuingPlace": "",
	"expireTime": "",
	"hasEnoughPoint": 0,
	"householdRegistrationType": 0,
	"id": 0,
	"isMilitaryOfficer": 0,
	"message": "",
	"militaryLicenseFile": [
		{
			"absoluteFilePath": "",
			"relativeFilePath": ""
		}
	],
	"name": "",
	"nonOperationalReviewDetail": {
		"reason": "",
		"reviewStatus": 0
	},
	"ownerCreditReviewDetail": {
		"reason": "",
		"reviewStatus": 0
	},
	"ownerQualifcationRelation": {
		"authId": "",
		"authType": 0,
		"isBind": 0
	},
	"ownerSocialInsuranceReviewDetail": {
		"reason": "",
		"reviewStatus": 0
	},
	"ownerTrafficReviewDetail": {
		"reason": "",
		"reviewStatus": 0
	},
	"property": 0,
	"reason": "",
	"reconsiderationFileCopy": [
		{
			"absoluteFilePath": "",
			"relativeFilePath": ""
		}
	],
	"residencePermitReviewDetail": {
		"reason": "",
		"reviewStatus": 0
	},
	"resultCode": "",
	"reviewStatus": 0,
	"reviewTime": "",
	"success": true,
	"vehicleModelType": 0
}
```

**响应参数**:


| 参数名称         | 参数说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
|applyNo| 联办码  |string  |    |
|applyTime| 提交申请时间  |string(date-time)  | string(date-time)   |
|authId| 证件号  |string  |    |
|authType| 证件类型 1:身份证 5:社会信用统一代码证 6:港澳台居民来往内地通行证 7:台湾居民来往大陆通行证 8:中国护照（华侨） 9:外国人护照 10:外国人永久居留身份证 11:港澳台居民居住证,可用值:1,5,6,7,8,9,10,11  |integer(int32)  | integer(int32)   |
|canReconsider| true:可复核 false:不可复核  |boolean  |    |
|driverFileNo| 档案编号  |string  |    |
|driverLicenseCode| 驾驶证编号  |string  |    |
|driverLicenseIssuingOrganization| 驾驶证发证单位  |string  |    |
|driverLicenseIssuingPlace| 驾驶证签发地  |string  |    |
|expireTime| 过期时间  |string(date-time)  | string(date-time)   |
|hasEnoughPoint| 居住证是否满120分 0：不满120分 1：满120分  |integer(int32)  | integer(int32)   |
|householdRegistrationType| 户籍类型 0:上海本市 1:外省市 2:其他  |integer(int32)  | integer(int32)   |
|id| 主键ID  |integer(int64)  | integer(int64)   |
|isMilitaryOfficer| 是否为现役军人 1:是 0:否  |integer(int32)  | integer(int32)   |
|message| 失败原因  |string  |    |
|militaryLicenseFile| 军官证扫描件列表  |array  | FileInfoDetail   |
|name| 客户名称  |string  |    |
|nonOperationalReviewDetail| 名下有无非营运车额度  |BaseOwnerQualificationReviewDetail  | BaseOwnerQualificationReviewDetail   |
|ownerCreditReviewDetail| 用户信用审核详情  |BaseOwnerQualificationReviewDetail  | BaseOwnerQualificationReviewDetail   |
|ownerQualifcationRelation| 绑定信息  |OwnerQualifcationRelation  | OwnerQualifcationRelation   |
|ownerSocialInsuranceReviewDetail| 用户社保审核详情  |BaseOwnerQualificationReviewDetail  | BaseOwnerQualificationReviewDetail   |
|ownerTrafficReviewDetail| 用户交通审核详情  |BaseOwnerQualificationReviewDetail  | BaseOwnerQualificationReviewDetail   |
|property| 客户性质 1:私人 2:法人  |integer(int32)  | integer(int32)   |
|reason| 综合拒绝原因  |string  |    |
|reconsiderationFileCopy| 异议扫描件列表列表  |array  | FileInfoDetail   |
|residencePermitReviewDetail| 居住证审核详情  |BaseOwnerQualificationReviewDetail  | BaseOwnerQualificationReviewDetail   |
|resultCode| 请求结果CODE码,可用值:SUCCESS,WARN,UN_AUTHORIZED,FAILURE,PARAM_VALID_ERROR,PARAM_TYPE_ERROR,INTERNAL_SERVER_ERROR  |string  |    |
|reviewStatus| 审核状态 0:审核中 1:审核通过 2:审核拒绝 3:复核中  |integer(int32)  | integer(int32)   |
|reviewTime| 审核时间  |string(date-time)  | string(date-time)   |
|success| 是否请求成功  |boolean  |    |
|vehicleModelType| 申请购买车型 0:纯电动 1:插电式混合动力 2:燃料电池动力  |integer(int32)  | integer(int32)   |



**schema属性说明**




**FileInfoDetail**

| 参数名称         | 参数说明                             |    类型 |  schema |
| ------------ | ------------------|--------|----------- |
|absoluteFilePath | 文件绝对路径   |string  |    |
|relativeFilePath | 文件相对路径   |string  |    |

**BaseOwnerQualificationReviewDetail**

| 参数名称         | 参数说明                             |    类型 |  schema |
| ------------ | ------------------|--------|----------- |
|reason | 拒绝原因   |string  |    |
|reviewStatus | 审核状态   |integer(int32)  |    |

**OwnerQualifcationRelation**

| 参数名称         | 参数说明                             |    类型 |  schema |
| ------------ | ------------------|--------|----------- |
|authId | 证件号   |string  |    |
|authType | 证件类型   |integer(int32)  |    |
|isBind | 是否绑定 1:已绑定 0:未绑定   |integer(int32)  |    |

**响应状态**:


| 状态码         | 说明                            |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200 | OK  |QueryOwnerQualificationDetailResponse|
| 201 | Created  ||
| 401 | Unauthorized  ||
| 403 | Forbidden  ||
| 404 | Not Found  ||
## 提交购车资质复核申请


**接口描述**:


**接口地址**:`/ownerQualification/reconsiderOwnerQualification`


**请求方式**：`POST`


**consumes**:`["application/json"]`


**produces**:`["*/*"]`



**请求参数**：

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|memSeq| memSeq  | body | false |integer  |    |
|openId| 微信openId  | header | true |string  |    |
|reconsiderOwnerQualificationVO| reconsiderOwnerQualificationVO  | body | true |ReconsiderOwnerQualificationVO  | ReconsiderOwnerQualificationVO   |

**schema属性说明**



**ReconsiderOwnerQualificationVO**

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|files| 异议扫描件列表  | body | true |array  |    |

**响应示例**:

```json
{
	"message": "",
	"resultCode": "",
	"success": true
}
```

**响应参数**:


| 参数名称         | 参数说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
|message| 失败原因  |string  |    |
|resultCode| 请求结果CODE码,可用值:SUCCESS,WARN,UN_AUTHORIZED,FAILURE,PARAM_VALID_ERROR,PARAM_TYPE_ERROR,INTERNAL_SERVER_ERROR  |string  |    |
|success| 是否请求成功  |boolean  |    |





**响应状态**:


| 状态码         | 说明                            |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200 | OK  |BaseResponse|
| 201 | Created  ||
| 401 | Unauthorized  ||
| 403 | Forbidden  ||
| 404 | Not Found  ||
## 提交法人购车资质信息


**接口描述**:


**接口地址**:`/ownerQualification/submitBusinessOwnerQualification`


**请求方式**：`POST`


**consumes**:`["application/json"]`


**produces**:`["*/*"]`



**请求参数**：

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|memSeq| memSeq  | body | false |integer  |    |
|openId| 微信openId  | header | true |string  |    |
|submitBusinessOwnerQualificationVO| submitBusinessOwnerQualificationVO  | body | true |SubmitBusinessOwnerQualificationVO  | SubmitBusinessOwnerQualificationVO   |

**schema属性说明**



**SubmitBusinessOwnerQualificationVO**

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|authId| 证件号  | body | true |string  |    |
|name| 姓名  | body | true |string  |    |

**响应示例**:

```json
{
	"message": "",
	"resultCode": "",
	"success": true
}
```

**响应参数**:


| 参数名称         | 参数说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
|message| 失败原因  |string  |    |
|resultCode| 请求结果CODE码,可用值:SUCCESS,WARN,UN_AUTHORIZED,FAILURE,PARAM_VALID_ERROR,PARAM_TYPE_ERROR,INTERNAL_SERVER_ERROR  |string  |    |
|success| 是否请求成功  |boolean  |    |





**响应状态**:


| 状态码         | 说明                            |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200 | OK  |BaseResponse|
| 201 | Created  ||
| 401 | Unauthorized  ||
| 403 | Forbidden  ||
| 404 | Not Found  ||
## 提交现役军人购车资质信息


**接口描述**:


**接口地址**:`/ownerQualification/submitMilitaryOwnerQualification`


**请求方式**：`POST`


**consumes**:`["application/json"]`


**produces**:`["*/*"]`



**请求参数**：

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|memSeq| memSeq  | body | false |integer  |    |
|openId| 微信openId  | header | true |string  |    |
|submitMilitaryOwnerQualificationVO| submitMilitaryOwnerQualificationVO  | body | true |SubmitMilitaryOwnerQualificationVO  | SubmitMilitaryOwnerQualificationVO   |

**schema属性说明**



**SubmitMilitaryOwnerQualificationVO**

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|authId| 证件号  | body | true |string  |    |
|authType| 证件类型  | body | false |integer(int32)  |    |
|driverLicenseCode| 驾驶证号  | body | false |string  |    |
|driverLicenseIssuingOrganization| 驾驶证发证机关  | body | false |string  |    |
|driverLicenseIssuingPlace| 驾驶证发证省市  | body | true |string  |    |
|file| 军官证扫描件  | body | false |array  |    |
|householdRegistrationType| 户籍类型 0:上海市 1:外省市 2:其他  | body | false |integer(int32)  |    |
|name| 姓名  | body | true |string  |    |
|vehicleModelType| 车辆类型 1:纯电动 2:插电式混动 3:燃料电池  | body | false |integer(int32)  |    |

**响应示例**:

```json
{
	"message": "",
	"resultCode": "",
	"success": true
}
```

**响应参数**:


| 参数名称         | 参数说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
|message| 失败原因  |string  |    |
|resultCode| 请求结果CODE码,可用值:SUCCESS,WARN,UN_AUTHORIZED,FAILURE,PARAM_VALID_ERROR,PARAM_TYPE_ERROR,INTERNAL_SERVER_ERROR  |string  |    |
|success| 是否请求成功  |boolean  |    |





**响应状态**:


| 状态码         | 说明                            |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200 | OK  |BaseResponse|
| 201 | Created  ||
| 401 | Unauthorized  ||
| 403 | Forbidden  ||
| 404 | Not Found  ||
## 提交私人购车资质信息


**接口描述**:


**接口地址**:`/ownerQualification/submitPrivateOwnerQualification`


**请求方式**：`POST`


**consumes**:`["application/json"]`


**produces**:`["*/*"]`



**请求参数**：

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|memSeq| memSeq  | body | false |integer  |    |
|openId| 微信openId  | header | true |string  |    |
|submitPrivateOwnerQualificationVO| submitPrivateOwnerQualificationVO  | body | true |SubmitPrivateOwnerQualificationVO  | SubmitPrivateOwnerQualificationVO   |

**schema属性说明**



**SubmitPrivateOwnerQualificationVO**

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|authId| 证件号  | body | true |string  |    |
|authType| 证件类型 1:身份证 5:社会信用统一代码证 6:港澳台居民来往内地通行证 7:台湾居民来往大陆通行证 8:中国护照（华侨） 9:外国人护照 10:外国人永久居留身份证 11:港澳台居民居住证,可用值:1,6,7,8,9,10,11  | body | true |integer(int32)  |    |
|driverFileNo| 档案编号  | body | false |string  |    |
|driverLicenseCode| 驾驶证号  | body | false |string  |    |
|driverLicenseIssuingOrganization| 驾驶证发证机关  | body | false |string  |    |
|driverLicenseIssuingPlace| 驾驶证发证省市  | body | true |string  |    |
|householdRegistrationType| 户籍类型 0:上海市 1:外省市 2:其他  | body | false |integer(int32)  |    |
|name| 姓名  | body | true |string  |    |
|vehicleModelType| 车辆类型 1:纯电动 2:插电式混动 3:燃料电池  | body | false |integer(int32)  |    |

**响应示例**:

```json
{
	"message": "",
	"resultCode": "",
	"success": true
}
```

**响应参数**:


| 参数名称         | 参数说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
|message| 失败原因  |string  |    |
|resultCode| 请求结果CODE码,可用值:SUCCESS,WARN,UN_AUTHORIZED,FAILURE,PARAM_VALID_ERROR,PARAM_TYPE_ERROR,INTERNAL_SERVER_ERROR  |string  |    |
|success| 是否请求成功  |boolean  |    |





**响应状态**:


| 状态码         | 说明                            |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200 | OK  |BaseResponse|
| 201 | Created  ||
| 401 | Unauthorized  ||
| 403 | Forbidden  ||
| 404 | Not Found  ||
# second-hand-vehicle-owner-controller

## 申请解除绑定（提交解除绑定材料）


**接口描述**:


**接口地址**:`/secondHandVehicleOwner/applyUnbindOwner`


**请求方式**：`POST`


**consumes**:`["application/json"]`


**produces**:`["*/*"]`



**请求参数**：

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|applyUnbindOwnerVO| applyUnbindOwnerVO  | body | true |ApplyUnbindOwnerVO  | ApplyUnbindOwnerVO   |
|memSeq| memSeq  | body | false |integer  |    |
|openId| 微信openId  | header | true |string  |    |

**schema属性说明**



**ApplyUnbindOwnerVO**

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|noVehicleCertificateFile| 名下无车证明  | body | false |array  |    |
|ownerSeq| 二手车唯一编号  | body | true |string  |    |
|ownerType| 用户性质  | body | true |integer(int32)  |    |
|vehicleRegistrationCertificateFile| 机动车注册登记证  | body | false |array  |    |
|vehicleTransactionInvoiceFile| 机动车交易发票  | body | false |array  |    |

**响应示例**:

```json
{
	"message": "",
	"resultCode": "",
	"success": true
}
```

**响应参数**:


| 参数名称         | 参数说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
|message| 失败原因  |string  |    |
|resultCode| 请求结果CODE码,可用值:SUCCESS,WARN,UN_AUTHORIZED,FAILURE,PARAM_VALID_ERROR,PARAM_TYPE_ERROR,INTERNAL_SERVER_ERROR  |string  |    |
|success| 是否请求成功  |boolean  |    |





**响应状态**:


| 状态码         | 说明                            |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200 | OK  |BaseResponse|
| 201 | Created  ||
| 401 | Unauthorized  ||
| 403 | Forbidden  ||
| 404 | Not Found  ||
## 撤销充电条件申请 （逻辑拒绝）


**接口描述**:


**接口地址**:`/secondHandVehicleOwner/cancelOwnerApply`


**请求方式**：`POST`


**consumes**:`["application/json"]`


**produces**:`["*/*"]`



**请求参数**：

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|cancelOwnerApplyVO| cancelOwnerApplyVO  | body | true |CancelOwnerApplyVO  | CancelOwnerApplyVO   |
|memSeq| memSeq  | body | false |integer  |    |
|openId| 微信openId  | header | true |string  |    |

**schema属性说明**



**CancelOwnerApplyVO**

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|ownerSeq| 二手车编号  | body | true |string  |    |
|ownerType| 用户性质 1:私人 2:法人  | body | true |integer(int32)  |    |

**响应示例**:

```json
{
	"message": "",
	"resultCode": "",
	"success": true
}
```

**响应参数**:


| 参数名称         | 参数说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
|message| 失败原因  |string  |    |
|resultCode| 请求结果CODE码,可用值:SUCCESS,WARN,UN_AUTHORIZED,FAILURE,PARAM_VALID_ERROR,PARAM_TYPE_ERROR,INTERNAL_SERVER_ERROR  |string  |    |
|success| 是否请求成功  |boolean  |    |





**响应状态**:


| 状态码         | 说明                            |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200 | OK  |BaseResponse|
| 201 | Created  ||
| 401 | Unauthorized  ||
| 403 | Forbidden  ||
| 404 | Not Found  ||
## 取消充电条件申请（逻辑删除）


**接口描述**:


**接口地址**:`/secondHandVehicleOwner/deleteOwnerApply`


**请求方式**：`POST`


**consumes**:`["application/json"]`


**produces**:`["*/*"]`



**请求参数**：

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|deleteOwnerApplyVO| deleteOwnerApplyVO  | body | true |DeleteOwnerApplyVO  | DeleteOwnerApplyVO   |
|memSeq| memSeq  | body | false |integer  |    |
|openId| 微信openId  | header | true |string  |    |

**schema属性说明**



**DeleteOwnerApplyVO**

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|ownerSeq| 二手车编号  | body | true |string  |    |
|ownerType| 用户性质 1:私人 2:法人  | body | true |integer(int32)  |    |

**响应示例**:

```json
{
	"message": "",
	"resultCode": "",
	"success": true
}
```

**响应参数**:


| 参数名称         | 参数说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
|message| 失败原因  |string  |    |
|resultCode| 请求结果CODE码,可用值:SUCCESS,WARN,UN_AUTHORIZED,FAILURE,PARAM_VALID_ERROR,PARAM_TYPE_ERROR,INTERNAL_SERVER_ERROR  |string  |    |
|success| 是否请求成功  |boolean  |    |





**响应状态**:


| 状态码         | 说明                            |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200 | OK  |BaseResponse|
| 201 | Created  ||
| 401 | Unauthorized  ||
| 403 | Forbidden  ||
| 404 | Not Found  ||
## 查询充电桩下拉列表框


**接口描述**:


**接口地址**:`/secondHandVehicleOwner/queryChargeOperatorCombo`


**请求方式**：`GET`


**consumes**:``


**produces**:`["*/*"]`



**请求参数**：

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|openId| 微信openId  | header | true |string  |    |

**响应示例**:

```json
{
	"comboBoxList": [
		{
			"id": {},
			"text": {}
		}
	],
	"message": "",
	"resultCode": "",
	"success": true
}
```

**响应参数**:


| 参数名称         | 参数说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
|comboBoxList|   |array  | ComboInfo«object,object»   |
|message| 失败原因  |string  |    |
|resultCode| 请求结果CODE码,可用值:SUCCESS,WARN,UN_AUTHORIZED,FAILURE,PARAM_VALID_ERROR,PARAM_TYPE_ERROR,INTERNAL_SERVER_ERROR  |string  |    |
|success| 是否请求成功  |boolean  |    |



**schema属性说明**




**ComboInfo«object,object»**

| 参数名称         | 参数说明                             |    类型 |  schema |
| ------------ | ------------------|--------|----------- |
|id |    |object  |    |
|text |    |object  |    |

**响应状态**:


| 状态码         | 说明                            |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200 | OK  |ComboInfoResponse|
| 401 | Unauthorized  ||
| 403 | Forbidden  ||
| 404 | Not Found  ||
## 获取充电条件信息


**接口描述**:


**接口地址**:`/secondHandVehicleOwner/queryOwnerDetail`


**请求方式**：`POST`


**consumes**:`["application/json"]`


**produces**:`["*/*"]`



**请求参数**：

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|memSeq| memSeq  | body | false |integer  |    |
|openId| 微信openId  | header | true |string  |    |
|queryOwnerDetailVO| queryOwnerDetailVO  | body | true |QueryOwnerDetailVO  | QueryOwnerDetailVO   |

**schema属性说明**



**QueryOwnerDetailVO**

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|ownerSeq| 二手车唯一编号  | body | true |string  |    |
|ownerType|   | body | false |integer(int32)  |    |

**响应示例**:

```json
{
	"applyTime": "",
	"authId": "",
	"authKind": 0,
	"chargeAuditTime": "",
	"chargeConfirmCopy": {
		"absoluteFilePath": "",
		"relativeFilePath": ""
	},
	"chargeDeviceDistrict": "",
	"chargeDeviceParkNo": "",
	"chargeDeviceProperty": 0,
	"chargeDeviceStartTime": "",
	"chargeDeviceStreet": "",
	"chargeDeviceType": "",
	"chargeNameplatePhoto": {
		"absoluteFilePath": "",
		"relativeFilePath": ""
	},
	"chargeNo": "",
	"chargeOperatorName": "",
	"chargePhotoCopy": {
		"absoluteFilePath": "",
		"relativeFilePath": ""
	},
	"disable": 0,
	"message": "",
	"mobilePhone": "",
	"name": "",
	"ownerSeq": "",
	"ownerStatus": 0,
	"ownerType": 0,
	"reason": "",
	"resultCode": "",
	"success": true,
	"vin": ""
}
```

**响应参数**:


| 参数名称         | 参数说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
|applyTime| 提交时间  |string(date-time)  | string(date-time)   |
|authId| 证件号  |string  |    |
|authKind| 证件类型 1:身份证 5:社会信用统一代码证 6:港澳台居民来往内地通行证 7:台湾居民来往大陆通行证 8:中国护照（华侨） 9:外国人护照 10:外国人永久居留身份证 11:港澳台居民居住证,可用值:1,5,6,7,8,9,10,11  |integer(int32)  | integer(int32)   |
|chargeAuditTime| 审核时间  |string(date-time)  | string(date-time)   |
|chargeConfirmCopy| 充电桩证明材料  |FileInfoDetail  | FileInfoDetail   |
|chargeDeviceDistrict| 充电桩安装区域  |string  |    |
|chargeDeviceParkNo| 充电设备安装停车位  |string  |    |
|chargeDeviceProperty| 充电桩性质： 1：自建  2：专用  3: 公用  |integer(int32)  | integer(int32)   |
|chargeDeviceStartTime| 充电设备投入运营时间  |string(date-time)  | string(date-time)   |
|chargeDeviceStreet| 充电设备安装街道  |string  |    |
|chargeDeviceType| 充电设备类型：  220V:AC220   380V:DC380   其他:OTHER  |string  |    |
|chargeNameplatePhoto| 充电桩铭牌照片  |FileInfoDetail  | FileInfoDetail   |
|chargeNo| 充电桩编号  |string  |    |
|chargeOperatorName| 充电桩建设运营单位名称  |string  |    |
|chargePhotoCopy| 充电桩照片  |FileInfoDetail  | FileInfoDetail   |
|disable| 有效状态 1:有效 0:失效  |integer(int32)  | integer(int32)   |
|message| 失败原因  |string  |    |
|mobilePhone| 手机号  |string  |    |
|name| 姓名  |string  |    |
|ownerSeq| 二手车编号  |string  |    |
|ownerStatus| 审核状态  2:审核中 3:审核通过 4:审核拒绝  |integer(int32)  | integer(int32)   |
|ownerType| 申请人性质  1:私人 2:法人  |integer(int32)  | integer(int32)   |
|reason| 拒绝原因  |string  |    |
|resultCode| 请求结果CODE码,可用值:SUCCESS,WARN,UN_AUTHORIZED,FAILURE,PARAM_VALID_ERROR,PARAM_TYPE_ERROR,INTERNAL_SERVER_ERROR  |string  |    |
|success| 是否请求成功  |boolean  |    |
|vin| 绑定车架号  |string  |    |



**schema属性说明**




**FileInfoDetail**

| 参数名称         | 参数说明                             |    类型 |  schema |
| ------------ | ------------------|--------|----------- |
|absoluteFilePath | 文件绝对路径   |string  |    |
|relativeFilePath | 文件相对路径   |string  |    |

**响应状态**:


| 状态码         | 说明                            |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200 | OK  |QuerySecondHandVehicleOwnerInfoResponse|
| 201 | Created  ||
| 401 | Unauthorized  ||
| 403 | Forbidden  ||
| 404 | Not Found  ||
## 查询充电条件列表


**接口描述**:


**接口地址**:`/secondHandVehicleOwner/queryOwnerList`


**请求方式**：`POST`


**consumes**:`["application/json"]`


**produces**:`["*/*"]`



**请求参数**：

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|memSeq| memSeq  | body | false |integer  |    |
|openId| 微信openId  | header | true |string  |    |
|queryOwnerListVO| queryOwnerListVO  | body | true |QueryOwnerListVO  | QueryOwnerListVO   |

**schema属性说明**



**QueryOwnerListVO**

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|ownerType|   | body | false |integer(int32)  |    |

**响应示例**:

```json
{
	"message": "",
	"resultCode": "",
	"rows": [
		{
			"disable": 0,
			"ownerSeq": "",
			"ownerStatus": 0,
			"vin": ""
		}
	],
	"success": true,
	"total": 0
}
```

**响应参数**:


| 参数名称         | 参数说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
|message| 失败原因  |string  |    |
|resultCode| 请求结果CODE码,可用值:SUCCESS,WARN,UN_AUTHORIZED,FAILURE,PARAM_VALID_ERROR,PARAM_TYPE_ERROR,INTERNAL_SERVER_ERROR  |string  |    |
|rows|   |array  | SecondHandVehicleOwnerListDetail   |
|success| 是否请求成功  |boolean  |    |
|total|   |integer(int64)  | integer(int64)   |



**schema属性说明**




**SecondHandVehicleOwnerListDetail**

| 参数名称         | 参数说明                             |    类型 |  schema |
| ------------ | ------------------|--------|----------- |
|disable | 有效状态 1:正常 0:失效   |integer(int32)  |    |
|ownerSeq | 二手车编号   |string  |    |
|ownerStatus | 审核状态 2:审核中 3:通过 4:拒绝   |integer(int32)  |    |
|vin | 绑定车架号   |string  |    |

**响应状态**:


| 状态码         | 说明                            |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200 | OK  |QuerySecondHandVehicleOwnerListResponse|
| 201 | Created  ||
| 401 | Unauthorized  ||
| 403 | Forbidden  ||
| 404 | Not Found  ||
## 查看解除绑定材料信息


**接口描述**:


**接口地址**:`/secondHandVehicleOwner/queryUnbindDetail`


**请求方式**：`POST`


**consumes**:`["application/json"]`


**produces**:`["*/*"]`



**请求参数**：

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|memSeq| memSeq  | body | false |integer  |    |
|queryUnbindDetailVO| queryUnbindDetailVO  | body | true |QueryUnbindDetailVO  | QueryUnbindDetailVO   |

**schema属性说明**



**QueryUnbindDetailVO**

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|ownerSeq|   | body | false |string  |    |
|ownerType|   | body | false |integer(int32)  |    |

**响应示例**:

```json
{
	"message": "",
	"noVehicleCertificateFile": [
		{
			"absoluteFilePath": "",
			"relativeFilePath": ""
		}
	],
	"ownerUnbindStatus": 0,
	"reason": "",
	"resultCode": "",
	"success": true,
	"vehicleRegistrationCertificateFile": [
		{
			"absoluteFilePath": "",
			"relativeFilePath": ""
		}
	],
	"vehicleTransactionInvoiceFile": [
		{
			"absoluteFilePath": "",
			"relativeFilePath": ""
		}
	]
}
```

**响应参数**:


| 参数名称         | 参数说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
|message| 失败原因  |string  |    |
|noVehicleCertificateFile|   |array  | FileInfoDetail   |
|ownerUnbindStatus|   |integer(int32)  | integer(int32)   |
|reason|   |string  |    |
|resultCode| 请求结果CODE码,可用值:SUCCESS,WARN,UN_AUTHORIZED,FAILURE,PARAM_VALID_ERROR,PARAM_TYPE_ERROR,INTERNAL_SERVER_ERROR  |string  |    |
|success| 是否请求成功  |boolean  |    |
|vehicleRegistrationCertificateFile|   |array  | FileInfoDetail   |
|vehicleTransactionInvoiceFile|   |array  | FileInfoDetail   |



**schema属性说明**




**FileInfoDetail**

| 参数名称         | 参数说明                             |    类型 |  schema |
| ------------ | ------------------|--------|----------- |
|absoluteFilePath | 文件绝对路径   |string  |    |
|relativeFilePath | 文件相对路径   |string  |    |

**响应状态**:


| 状态码         | 说明                            |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200 | OK  |QuerySecondHandVehicleOwnerUnbindDetailResponse|
| 201 | Created  ||
| 401 | Unauthorized  ||
| 403 | Forbidden  ||
| 404 | Not Found  ||
## 提交充电条件信息


**接口描述**:


**接口地址**:`/secondHandVehicleOwner/submitOwnerInfo`


**请求方式**：`POST`


**consumes**:`["application/json"]`


**produces**:`["*/*"]`



**请求参数**：

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|memSeq| memSeq  | body | false |integer  |    |
|openId| 微信openId  | header | true |string  |    |
|submitOwnerInfoVO| submitOwnerInfoVO  | body | true |SubmitOwnerInfoVO  | SubmitOwnerInfoVO   |

**schema属性说明**



**SubmitOwnerInfoVO**

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|authId| 证件号  | body | false |string  |    |
|authKind| 证件类型 1:身份证 5:社会信用统一代码证 6:港澳台居民来往内地通行证 7:台湾居民来往大陆通行证 8:中国护照（华侨） 9:外国人护照 10:外国人永久居留身份证 11:港澳台居民居住证,可用值:1,5,6,7,8,9,10,11  | body | true |integer(int32)  |    |
|chargeConfirmCopy| 充电桩证明材料  | body | false |string  |    |
|chargeDeviceDistrict| 充电桩安装区域  | body | false |string  |    |
|chargeDeviceParkNo| 充电设备安装停车位  | body | false |string  |    |
|chargeDeviceProperty| 充电桩性质： 1：自建  2：专用  3: 公用  | body | false |integer(int32)  |    |
|chargeDeviceStartTime| 充电设备投入运营时间  | body | false |string(date-time)  |    |
|chargeDeviceStreet| 充电设备安装街道  | body | false |string  |    |
|chargeDeviceType| 充电设备类型：  220V:AC220   380V:DC380   其他:OTHER  | body | false |string  |    |
|chargeNameplatePhoto| 充电桩铭牌照片  | body | false |string  |    |
|chargeNo| 充电桩编号  | body | false |string  |    |
|chargeOperatorName| 充电桩建设运营单位名称  | body | false |string  |    |
|chargePhotoCopy| 充电桩照片  | body | false |string  |    |
|mobilePhone| 手机号  | body | false |string  |    |
|name| 姓名  | body | true |string  |    |
|ownerType| 申请人性质  1:私人 2:法人  | body | true |integer(int32)  |    |

**响应示例**:

```json
{
	"message": "",
	"resultCode": "",
	"success": true
}
```

**响应参数**:


| 参数名称         | 参数说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
|message| 失败原因  |string  |    |
|resultCode| 请求结果CODE码,可用值:SUCCESS,WARN,UN_AUTHORIZED,FAILURE,PARAM_VALID_ERROR,PARAM_TYPE_ERROR,INTERNAL_SERVER_ERROR  |string  |    |
|success| 是否请求成功  |boolean  |    |





**响应状态**:


| 状态码         | 说明                            |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200 | OK  |BaseResponse|
| 201 | Created  ||
| 401 | Unauthorized  ||
| 403 | Forbidden  ||
| 404 | Not Found  ||
## 修改充电条件信息


**接口描述**:


**接口地址**:`/secondHandVehicleOwner/updateOwnerInfo`


**请求方式**：`POST`


**consumes**:`["application/json"]`


**produces**:`["*/*"]`



**请求参数**：

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|memSeq| memSeq  | body | false |integer  |    |
|openId| 微信openId  | header | true |string  |    |
|updateOwnerInfoVO| updateOwnerInfoVO  | body | true |UpdateOwnerInfoVO  | UpdateOwnerInfoVO   |

**schema属性说明**



**UpdateOwnerInfoVO**

| 参数名称         | 参数说明     |     in |  是否必须      |  数据类型  |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
|authId| 证件号  | body | false |string  |    |
|authKind| 证件类型 1:身份证 5:社会信用统一代码证 6:港澳台居民来往内地通行证 7:台湾居民来往大陆通行证 8:中国护照（华侨） 9:外国人护照 10:外国人永久居留身份证 11:港澳台居民居住证,可用值:1,5,6,7,8,9,10,11  | body | true |integer(int32)  |    |
|chargeConfirmCopy| 充电桩证明材料  | body | false |string  |    |
|chargeDeviceDistrict| 充电桩安装区域  | body | false |string  |    |
|chargeDeviceParkNo| 充电设备安装停车位  | body | false |string  |    |
|chargeDeviceProperty| 充电桩性质： 1：自建  2：专用  3: 公用  | body | false |integer(int32)  |    |
|chargeDeviceStartTime| 充电设备投入运营时间  | body | false |string(date-time)  |    |
|chargeDeviceStreet| 充电设备安装街道  | body | false |string  |    |
|chargeDeviceType| 充电设备类型：  220V:AC220   380V:DC380   其他:OTHER  | body | false |string  |    |
|chargeNameplatePhoto| 充电桩铭牌照片  | body | false |string  |    |
|chargeNo| 充电桩编号  | body | false |string  |    |
|chargeOperatorName| 充电桩建设运营单位名称  | body | false |string  |    |
|chargePhotoCopy| 充电桩照片  | body | false |string  |    |
|mobilePhone| 手机号  | body | false |string  |    |
|name| 姓名  | body | true |string  |    |
|ownerSeq| 二手车唯一编号  | body | true |string  |    |
|ownerType| 申请人性质  1:私人 2:法人  | body | true |integer(int32)  |    |

**响应示例**:

```json
{
	"message": "",
	"resultCode": "",
	"success": true
}
```

**响应参数**:


| 参数名称         | 参数说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
|message| 失败原因  |string  |    |
|resultCode| 请求结果CODE码,可用值:SUCCESS,WARN,UN_AUTHORIZED,FAILURE,PARAM_VALID_ERROR,PARAM_TYPE_ERROR,INTERNAL_SERVER_ERROR  |string  |    |
|success| 是否请求成功  |boolean  |    |





**响应状态**:


| 状态码         | 说明                            |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200 | OK  |BaseResponse|
| 201 | Created  ||
| 401 | Unauthorized  ||
| 403 | Forbidden  ||
| 404 | Not Found  ||
