package com.extracme.nevmp.vo.qualification;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 复核购车资质信息（私人， 法人无法复核）
 * <AUTHOR>
 */
@Data
public class ReconsiderOwnerQualificationVO {

    /**
     * 异议扫描件列表
     */
    @NotEmpty
    @ApiModelProperty(value = "异议扫描件列表", required = true)
    private List<String> files;
}
