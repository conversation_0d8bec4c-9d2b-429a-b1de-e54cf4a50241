package com.extracme.nevmp.vo.qualification;

import com.extracme.nevmp.enums.AuthTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
public class BindOwnerQualificationVO {


    /**
     * 申请人性质
     * 1:私人
     * 2:法人
     */
    @NotNull
    @ApiModelProperty(value = "申请人性质  1:私人 2:法人", required = true)
    private Integer ownerType;

    /**
     * 证件类型
     */
    @NotNull
    @ApiModelProperty(value = "证件类型")
    private Integer authType = AuthTypeEnum.IDENTIFICATION_CARD.getType();

    /**
     * 证件号
     */
    @NotBlank
    @ApiModelProperty(value = "证件号",required = true)
    private String authId;

}
