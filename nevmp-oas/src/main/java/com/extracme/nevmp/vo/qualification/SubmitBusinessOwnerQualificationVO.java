package com.extracme.nevmp.vo.qualification;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 提交法人购车资质信息 Viewer Object
 */
@Data
public class SubmitBusinessOwnerQualificationVO {

    @NotBlank
    @ApiModelProperty(value = "姓名", required = true)
    private  String name;

    @NotBlank
    @ApiModelProperty(value = "证件号",required = true)
    private String authId;
}
