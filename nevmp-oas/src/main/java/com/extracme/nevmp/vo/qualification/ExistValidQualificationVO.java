package com.extracme.nevmp.vo.qualification;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 判断是否存在有效的购车资质
 * <AUTHOR>
 */
@Data
public class ExistValidQualificationVO {

    /**
     * 用户性质
     * 1:私人
     * 2：法人
     */
    @NotNull
    @ApiModelProperty(value = "用户性质 1:私人 2:法人", required = true)
    private Integer ownerType;
}
