package com.extracme.nevmp.vo.qualification;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 查询用户购车资质详情
 * <AUTHOR>
 */
@Data
public class QueryOwnerQualificationDetailVO {

    /**
     * 用户性质
     * 1:私人
     * 2：法人
     */
    @NotNull
    @ApiModelProperty(value = "用户性质 1:私人 2:法人", required = true)
    private Integer ownerType;
}
