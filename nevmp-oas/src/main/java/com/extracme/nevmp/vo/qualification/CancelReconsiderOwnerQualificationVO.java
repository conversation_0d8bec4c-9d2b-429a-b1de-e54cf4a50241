package com.extracme.nevmp.vo.qualification;

import com.extracme.nevmp.enums.OwnerTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class CancelReconsiderOwnerQualificationVO {

    /**
     * 用户性质
     * 1:私人
     * 2：法人
     */
    @NotNull
    @ApiModelProperty(value = "用户性质 1:私人 2:法人", required = true)
    private Integer ownerType = OwnerTypeEnum.PRIVATE.getValue();
}
