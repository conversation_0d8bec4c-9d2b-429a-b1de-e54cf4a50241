package com.extracme.nevmp.controller;

import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.SessionAttribute;
import org.springframework.web.client.RestTemplate;

import com.extracme.nevmp.api.BaseResponse;
import com.extracme.nevmp.auth.Authorize;
import com.extracme.nevmp.configuration.NevmpRequestUrl;
import com.extracme.nevmp.enums.OwnerTypeEnum;
import com.extracme.nevmp.error.ServiceException;
import com.extracme.nevmp.response.exchange.GetExchangeQualificationDetailResponse;
import com.extracme.nevmp.response.exchange.QueryMembershipQualificationRelationResponse;
import com.extracme.nevmp.response.exchange.SearchExchangeQualificationListResponse;
import com.extracme.nevmp.response.exchange.SubmitExchangeQualificationResponse;
import com.extracme.nevmp.service.impl.OwnerQualificationService;
import com.extracme.nevmp.service.model.MembershipQualificationRelation;
import com.extracme.nevmp.vo.exchange.CancelReconsiderExchangeQualificationVO;
import com.extracme.nevmp.vo.exchange.GetExchangeQualificationDetailVO;
import com.extracme.nevmp.vo.exchange.RelateMembershipQualificationVO;
import com.extracme.nevmp.vo.exchange.SearchExchangeQualificationListVO;
import com.extracme.nevmp.vo.exchange.SubmitMilitaryExchangeQualificationVO;
import com.extracme.nevmp.vo.exchange.SubmitPrivateExchangeQualificationVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;


@Api(tags = "以旧换新资格申请")
@Slf4j
@Authorize
@RestController
@RequestMapping("partExchangeQualification")
public class PartExchangeQualificationController {

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private OwnerQualificationService ownerQualificationService;

    @PostMapping("submitPrivateExchangeQualification")
    @ApiOperation(value = "提交私人以旧换新资格申请")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "成功", response = SubmitExchangeQualificationResponse.class)
    })
    @ApiImplicitParam(paramType = "header", name = "openId", dataType = "String", required = true, value = "微信openId", defaultValue = "123")
    public SubmitExchangeQualificationResponse submitPrivateExchangeQualification(@SessionAttribute("memSeq") Long memSeq
            , @RequestBody @Validated @ApiParam(value = "私人以旧换新资格申请信息", required = true) SubmitPrivateExchangeQualificationVO submitPrivateExchangeQualificationVO){
        //校验提交人信息
        MembershipQualificationRelation relation = ownerQualificationService.queryMembershipQualificationRelation(memSeq);
        if(relation == null || relation.getBind() != 1){
            throw new ServiceException("请先进行账号认证");
        }
        if(!Objects.equals(relation.getAuthId(),submitPrivateExchangeQualificationVO.getAuthId()) ||
                !Objects.equals(relation.getAuthType(),submitPrivateExchangeQualificationVO.getAuthType())) {
            throw new ServiceException("该账号已绑定 证件号:" + relation.getAuthId());
        }

        //保存以旧换新提交信息
        ResponseEntity<SubmitExchangeQualificationResponse> responseEntity = restTemplate.postForEntity(
                NevmpRequestUrl.getUrl("/partExchangeQualification/submitPrivateExchangeQualification"), submitPrivateExchangeQualificationVO, SubmitExchangeQualificationResponse.class);
        return responseEntity.getBody();
    }

    @PostMapping("submitMilitaryExchangeQualification")
    @ApiOperation(value = "提交军官以旧换新资格申请")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "成功", response = SubmitExchangeQualificationResponse.class)
    })
    @ApiImplicitParam(paramType = "header", name = "openId", dataType = "String", required = true, value = "微信openId", defaultValue = "123")
    public SubmitExchangeQualificationResponse submitMilitaryExchangeQualification(@SessionAttribute("memSeq") Long memSeq
            , @RequestBody @Validated @ApiParam(value = "军人以旧换新资格申请信息", required = true) SubmitMilitaryExchangeQualificationVO submitMilitaryExchangeQualificationVO){
        //校验提交人信息
        MembershipQualificationRelation relation = ownerQualificationService.queryMembershipQualificationRelation(memSeq);
        if(relation == null || relation.getBind() != 1){
            throw new ServiceException("请先进行账号认证");
        }
        if(!Objects.equals(relation.getAuthId(),submitMilitaryExchangeQualificationVO.getAuthId()) ||
                !Objects.equals(relation.getAuthType(),submitMilitaryExchangeQualificationVO.getAuthType())) {
            throw new ServiceException("该账号已绑定 证件号:" + relation.getAuthId());
        }

        //保存以旧换新提交信息
        ResponseEntity<SubmitExchangeQualificationResponse> responseEntity = restTemplate.postForEntity(
                NevmpRequestUrl.getUrl("/partExchangeQualification/submitMilitaryExchangeQualification"), submitMilitaryExchangeQualificationVO, SubmitExchangeQualificationResponse.class);
        return responseEntity.getBody();
    }



    @PostMapping("searchExchangeQualificationList")
    @ApiOperation(value = "查询用户以旧换新申请列表")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "成功", response = SearchExchangeQualificationListResponse.class)
    })
    @ApiImplicitParam(paramType = "header", name = "openId", dataType = "String", required = true, value = "微信openId", defaultValue = "123")
    public SearchExchangeQualificationListResponse searchExchangeQualificationList(@SessionAttribute("memSeq") Long memSeq){

        MembershipQualificationRelation relation = ownerQualificationService.queryMembershipQualificationRelation(memSeq);
        if(relation == null || relation.getBind() != 1){
            throw new ServiceException("请先进行账号认证");
        }

        SearchExchangeQualificationListVO searchExchangeQualificationListVO = new SearchExchangeQualificationListVO();
        searchExchangeQualificationListVO.setAuthId(relation.getAuthId());
        searchExchangeQualificationListVO.setAuthType(relation.getAuthType());

        //保存以旧换新提交信息
        ResponseEntity<SearchExchangeQualificationListResponse> responseEntity = restTemplate.postForEntity(
                NevmpRequestUrl.getUrl("/partExchangeQualification/searchExchangeQualificationList"), searchExchangeQualificationListVO, SearchExchangeQualificationListResponse.class);
        return responseEntity.getBody();
    }

    @PostMapping("getExchangeQualificationDetail")
    @ApiOperation(value = "查询以旧换新资质详情")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "成功", response = GetExchangeQualificationDetailResponse.class)
    })
    @ApiImplicitParam(paramType = "header", name = "openId", dataType = "String", required = true, value = "微信openId", defaultValue = "123")
    public GetExchangeQualificationDetailResponse getExchangeQualificationDetail(@SessionAttribute("memSeq") Long memSeq
            , @RequestBody @Validated @ApiParam(value = "以旧换新明细查询条件", required = true) GetExchangeQualificationDetailVO getExchangeQualificationDetailVO ){

        MembershipQualificationRelation relation = ownerQualificationService.queryMembershipQualificationRelation(memSeq);
        if(relation == null || relation.getBind() != 1){
            throw new ServiceException("请先进行账号认证");
        }

        getExchangeQualificationDetailVO.setAuthType(relation.getAuthType());
        getExchangeQualificationDetailVO.setAuthId(relation.getAuthId());

        //查询以旧换新资质详情
        ResponseEntity<GetExchangeQualificationDetailResponse> responseEntity = restTemplate.postForEntity(
                NevmpRequestUrl.getUrl("/partExchangeQualification/getExchangeQualificationDetail"), getExchangeQualificationDetailVO, GetExchangeQualificationDetailResponse.class);
        return responseEntity.getBody();
    }

    @PostMapping("cancelReconsiderExchangeQualification")
    @ApiOperation(value = "取消以旧换新资格复核")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "成功", response = BaseResponse.class)
    })
    @ApiImplicitParam(paramType = "header", name = "openId", dataType = "String", required = true, value = "微信openId", defaultValue = "123")
    public BaseResponse cancelReconsiderExchangeQualification(@SessionAttribute("memSeq") Long memSeq
            ,@RequestBody @Validated @ApiParam(value = "取消以旧换新复核信息", required = true) CancelReconsiderExchangeQualificationVO cancelReconsiderExchangeQualificationVO ){

        MembershipQualificationRelation relation = ownerQualificationService.queryMembershipQualificationRelation(memSeq);
        if(relation == null || relation.getBind() != 1){
            throw new ServiceException("请先进行账号认证");
        }
        cancelReconsiderExchangeQualificationVO.setAuthType(relation.getAuthType());
        cancelReconsiderExchangeQualificationVO.setAuthId(relation.getAuthId());

        //取消以旧换新资格复核
        ResponseEntity<BaseResponse> responseEntity = restTemplate.postForEntity(
                NevmpRequestUrl.getUrl("/partExchangeQualification/cancelReconsiderExchangeQualification"), cancelReconsiderExchangeQualificationVO, BaseResponse.class);
        return responseEntity.getBody();
    }

    @PostMapping("relateMembershipQualification")
    @ApiOperation(value = "关联用户认证信息", httpMethod = "POST")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "成功", response = BaseResponse.class)
    })
    @ApiImplicitParam(paramType = "header", name = "openId", dataType = "String", required = true, value = "微信openId", defaultValue = "123")
    public BaseResponse relateMembershipQualification(@SessionAttribute("memSeq") Long memSeq
            ,@RequestBody @Validated RelateMembershipQualificationVO relateMembershipQualificationVO){
        //关联用户认证信息
        ownerQualificationService.relateMembershipQualification(memSeq, OwnerTypeEnum.PRIVATE.getValue(),
                relateMembershipQualificationVO.getAuthType(), relateMembershipQualificationVO.getAuthId());
        return new BaseResponse();
    }


    @PostMapping("queryMembershipQualificationRelation")
    @ApiOperation(value = "查询用户关联信息", httpMethod = "POST")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "成功", response = QueryMembershipQualificationRelationResponse.class)
    })
    @ApiImplicitParam(paramType = "header", name = "openId", dataType = "String", required = true, value = "微信openId", defaultValue = "123")
    public QueryMembershipQualificationRelationResponse queryMembershipQualificationRelation(@SessionAttribute("memSeq") Long memSeq){

        MembershipQualificationRelation relation = ownerQualificationService.queryMembershipQualificationRelation(memSeq);

        //组装返回信息
        QueryMembershipQualificationRelationResponse response = new QueryMembershipQualificationRelationResponse();
        if(relation!= null){
            response.setHasBind(Objects.equals(relation.getBind(), 1));
            response.setAuthType(relation.getAuthType());
            response.setAuthId(relation.getAuthId());
        }
        return response;
    }
}
