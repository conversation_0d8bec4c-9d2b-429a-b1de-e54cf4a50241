package com.extracme.nevmp.service.library;

import java.util.HashMap;
import java.util.Map;

import org.junit.jupiter.api.Test;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @Description 办件库测试类
 */
public class LibraryTest {
    private static String WEB_URL = "http://**************:8022/ac-product-api";

    private static String applyNo;

    private static String name;

    private static String authType;

    private static String authId;

    @Test
    public String getToken(){
        String url = WEB_URL + "/oauth2/getToken";

        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/x-www-form-urlencoded");

        MultiValueMap<String, Object> postParameters = new LinkedMultiValueMap<>();
        postParameters.add("clientId", "SHJXSH-YJS");
        postParameters.add("clientSecret", "111111b");

        HttpEntity<MultiValueMap<String, Object>> httpEntity = new HttpEntity<>(postParameters, headers);
        RestTemplate restTemplate = new RestTemplate();
        final Map map = restTemplate.postForObject(url, httpEntity, Map.class);

        map.forEach((k,v)->{
            System.out.println(k + ":" + v);
        });
        System.out.println("========");
        return map.get("access_token").toString();
    }

    /**
     * 获取待办理办件列表
     */
    @Test
    public void queryAcceptApplyList(){
        String url = WEB_URL  + "/uapply/queryAcceptApplyList";
        String accessToken = getToken();
        RestTemplate restTemplate = new RestTemplate();
        Map params = new HashMap();
        params.put("accessToken", accessToken);
        params.put("status","已撤销");
        params.put("pageSize",20);
        final Map map = restTemplate.postForObject(url, params, Map.class);
        map.forEach((k,v)->{
            System.out.println(k + ":" + v);
        });
        System.out.println("========");
    }



    /**
     * 受理 (未知的办件来源)
     */
    @Test
    public void acceptApply(){
        String url = WEB_URL  + "/uapply/acceptApply";
        String accessToken = getToken();
        RestTemplate restTemplate = new RestTemplate();
        Map params = new HashMap();
        params.put("accessToken", accessToken);
        //统一审批编码
        params.put("applyNo", "03W42572000000E");
        //受理结果
        params.put("result", "受理");

        params.put("method","网上受理");

        params.put("opTime", "2020-08-05 07:36:00");

        //受理部门编号
        params.put("opDepartCode","SHJXSH");

        //受理部门
        params.put("opDepartName","上海市经信委");

        //受理人员工号
        params.put("opUserId","-1");

        //受理人员姓名
        params.put("opUsername","system");

        final Map map = restTemplate.postForObject(url, params, Map.class);
        map.forEach((k,v)->{
            System.out.println(k + ":" + v);
        });
        System.out.println("========");
    }


    @Test
    public void generateApplyNo(){

        String url = WEB_URL + "/portal/generateApplyNo";
        String accessToken = getToken();
        RestTemplate restTemplate = new RestTemplate();
        Map params = new HashMap();
        params.put("accessToken", accessToken);
        //购车资格查询
        params.put("itemCode", "3120W4257000");

        final Map map = restTemplate.postForObject(url, params, Map.class);
        map.forEach((k,v)->{
            System.out.println(k + ":" + v);
        });
        System.out.println("========");

    }




    @Test
    public void saveApply(){
        String url = WEB_URL  + "/uapply/saveApply";
        String accessToken = getToken();
        RestTemplate restTemplate = new RestTemplate();
        Map params = new HashMap();
        params.put("accessToken", accessToken);
        //统一审批编码
        params.put("applyNo","03W42572000000E");
        //事项编码
        params.put("itemCode", "3120W4257000");
        //业务办理项编码
        params.put("taskHandleItem","11310000002422004523120W425700001");
        //事项名称
        params.put("itemName","用户资格审核");
        //办理对象类型
        params.put("targetType","个人");
        //办理对象名称
        params.put("targetName","AA");
        //办理对象编号
        params.put("targetNo","310114199305120411");
        //用户ID
        params.put("userId", 33);
        //申请人姓名
        params.put("username","AA");
        //申请人证件类型
        params.put("licenseType","身份证");
        //申请人证件编号
        params.put("licenseNo","310114199305120411");
        //申请人手机号
        params.put("mobile","13816607573");
        //受理部门（办理点）编号
        params.put("departCode", "SHJXSH");
        //受理部门（办理点）名称
        params.put("departName", "上海市经信委");
        //办件来源
        params.put("source", "网上申请");
        //保存时间
        params.put("opTime", "2020-08-05 07:26:00");
        //办理项类型
        params.put("submitType","新办");

        Map info = new HashMap();
        info.put("name","AA");
        info.put("authId","123");
        info.put("driverCode", "310115199306118888");
        info.put("status1",1);
        //完整的申请数据
        params.put("info",info);
        //1即办件 2 承诺件
        params.put("ProjectType",1);
        //所属区域
        params.put("districtCode","SH00SH");

        final Map map = restTemplate.postForObject(url, params, Map.class);
        map.forEach((k,v)->{
            System.out.println(k + ":" + v);
        });
        System.out.println("========");
    }


    @Test
    public void submitApply(){
        String url = WEB_URL + "/uapply/submitApply";
        String accessToken = getToken();
        RestTemplate restTemplate = new RestTemplate();
        Map params = new HashMap();
        params.put("accessToken", accessToken);
        //统一审批编码
        params.put("applyNo","03W42572000000E");

        //联办件统一审批编码（主办件信息异常）
//        params.put("uapplyNo", "31LB00010400");

        params.put("itemCode","3120W4257000");
        //提交时间
        params.put("opTime", "2020-08-05 07:26:00");

        final Map map = restTemplate.postForObject(url, params, Map.class);
        map.forEach((k,v)->{
            System.out.println(k + ":" + v);
        });
        System.out.println("========");
    }

}
