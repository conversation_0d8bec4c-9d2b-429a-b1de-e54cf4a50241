package com.extracme.nevmp.auth;

import com.extracme.nevmp.model.Syresource;
import com.extracme.nevmp.model.Syuser;
import com.extracme.nevmp.utils.MemcachedUtil;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * <AUTHOR>
 * @Description 登录session类
 */
public class Sessions {

    public static void loginUser(Syuser user, List<Syresource> resources, HttpServletResponse response){

        String token = UUID.randomUUID().toString();
        Cookie cookie = new Cookie(AuthConstant.COOKIE_NAME, token);
        long duration = AuthConstant.SHORT_SESSION;
        int maxAge = (int) (duration / 1000);
        cookie.setMaxAge(maxAge);
        cookie.setPath("/");
        response.addCookie(cookie);

        TokenSession tokenSession = new TokenSession();
        tokenSession.setUserId(user.getId());
        tokenSession.setUserName(user.getName());
        tokenSession.setOrgId(user.getOrgId());
        tokenSession.setOrgName(user.getOrgName());
        tokenSession.setOrgKind(user.getOrgKind());
        Map<String,String> resourceMap = new HashMap<>();
        for(Syresource resource : resources){
            resourceMap.put(resource.getUrl(), resource.getName());
        }
        tokenSession.setResourceMap(resourceMap);
        MemcachedUtil.set(token, tokenSession, duration/1000);
    }

    public static String getToken(HttpServletRequest request) {
        Cookie[] cookies = request.getCookies();
        if (cookies == null || cookies.length == 0) {
            return null;
        }
        Cookie tokenCookie = Arrays.stream(cookies)
                .filter(cookie -> AuthConstant.COOKIE_NAME.equals(cookie.getName()))
                .findAny().orElse(null);
        if (tokenCookie == null) {
            return null;
        }
        return tokenCookie.getValue();
    }

    public static void logout(HttpServletRequest request, HttpServletResponse response){

        //清除memCache
        String token = getToken(request);
        if(StringUtils.isNotBlank(token)){
            MemcachedUtil.delete(token);
        }

        //清除Session
        request.getSession().setAttribute("user", null);

        //清除Cookie
        Cookie cookie = new Cookie(AuthConstant.COOKIE_NAME, "");
        cookie.setPath("/");
        cookie.setMaxAge(0);
        response.addCookie(cookie);


    }

    public static TokenSession getTokenSession(String token) {
        if(StringUtils.isBlank(token)){
            return null;
        }
        Object result = MemcachedUtil.get(token);
        return result == null? null:(TokenSession) result;
    }

    /**
     * 操作加锁
     * @param userSession
     * @return
     */
    public static Boolean lock(UserSession userSession, String operate){
        //加锁，超时时间为15分钟
        return MemcachedUtil.add("LockKey_"+ userSession.getOrgId() + "_" + operate, "value",  10 * 60);
    }

    /**
     * 操作解锁
     * @param userSession
     * @param operate
     */
    public static void unlock(UserSession userSession, String operate){
        MemcachedUtil.delete("LockKey" +"_"+ userSession.getOrgId() + "_" + operate);
    }
}
