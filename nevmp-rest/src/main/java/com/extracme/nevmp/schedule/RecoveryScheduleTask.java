package com.extracme.nevmp.schedule;

import com.extracme.nevmp.enums.OwnerStatusEnum;
import com.extracme.nevmp.mapper.ChargeBuildInfoMapper;
import com.extracme.nevmp.mapper.extend.OwnerInfoExtendMapper;
import com.extracme.nevmp.model.OwnerChargeInfo;
import com.extracme.nevmp.model.OwnerInfo;
import com.extracme.nevmp.model.VehicleInfo;
import com.extracme.nevmp.service.async.AsyncService;
import com.extracme.nevmp.service.library.LibraryService;
import com.extracme.nevmp.service.owner.OwnerService;
import com.extracme.nevmp.service.vehicle.VehicleService;
import org.mybatis.dynamic.sql.SqlBuilder;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.extracme.nevmp.mapper.OwnerInfoDynamicSqlSupport.ownerInfo;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

/**
 * <AUTHOR>
 * @Description
 */
@Configuration
@EnableScheduling
public class RecoveryScheduleTask {


    @Autowired
    private OwnerInfoExtendMapper ownerInfoExtendMapper;

    @Autowired
    private ChargeBuildInfoMapper chargeBuildInfoMapper;

    @Autowired
    private LibraryService libraryService;

    @Autowired
    private AsyncService asyncService;

    @Autowired
    private VehicleService vehicleService;

    @Autowired
    private OwnerService ownerService;


    @Scheduled(initialDelay = 5000, fixedDelay = 86400000)
    private void recoveryOwnerConfirm(){
        Date oneHourAgo = new Date(System.currentTimeMillis() - TimeUnit.HOURS.toMillis(1));
        SelectStatementProvider selectStatement = SqlBuilder.select(ownerInfo.allColumns())
                .from(ownerInfo)
                .where(ownerInfo.ownerStatus, isEqualTo(OwnerStatusEnum.OWNER_CONFIRM.getOwnerStatus()))
                .and(ownerInfo.applyNo, isNull())
                .and(ownerInfo.uapplyNo, isNotNull())
                .and(ownerInfo.applyToOwnerConfirmTime, isLessThanWhenPresent(oneHourAgo))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        final List<OwnerInfo> ownerInfoList = ownerInfoExtendMapper.selectMany(selectStatement);
        for(OwnerInfo recoveryOwner : ownerInfoList) {
            System.out.println("开始纠正充电条件确认信息:" + recoveryOwner.getId());
            //查询关联的车辆信息
            VehicleInfo relationVehicle = vehicleService.queryOwnerRelationVehicleInfo(recoveryOwner.getOwnerId());

            //查询关联的充电桩信息
            OwnerChargeInfo relationCharge = ownerService.getOwnerChargeInfo(recoveryOwner.getId());

            asyncService.submitOwnerApply(recoveryOwner, relationVehicle, relationCharge);
        }
    }
}
