package com.extracme.nevmp.controller;

import com.extracme.nevmp.api.BaseResponse;
import com.extracme.nevmp.auth.Authorize;
import com.extracme.nevmp.auth.UserSession;
import com.extracme.nevmp.converter.ConvertUtil;
import com.extracme.nevmp.dto.charge.*;
import com.extracme.nevmp.dto.file.SearchFileNoteDTO;
import com.extracme.nevmp.enums.FileTypeEnum;
import com.extracme.nevmp.service.charger.ChargeBuildService;
import com.extracme.nevmp.service.file.FileNoteService;
import com.extracme.nevmp.utils.AssertUtil;
import com.extracme.nevmp.utils.CommonUtil;
import com.extracme.nevmp.utils.DateUtil;
import com.extracme.nevmp.vo.*;
import com.extracme.nevmp.vo.charge.SearchChargeBuildInfoVO;
import com.extracme.nevmp.vo.common.SingleId;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @date 2020/11/6
 */
@Authorize
@RestController
@RequestMapping("charge")
@Api("充电桩建设信息维护")
public class ChargeController {

    @Autowired
    private ChargeBuildService chargeBuildService;
    @Autowired
    private FileNoteService fileNoteService;

    @PostMapping("addChargeBuild")
    @ApiOperation("新增充电桩")
    public BaseResponse addChargeBuild(@RequestBody @Validated AddChargeBuildVO addChargeBuildVO, @SessionAttribute("user")UserSession userSession) {
        AddChargeBuildDTO addChargeBuildDTO = ConvertUtil.convert(addChargeBuildVO, AddChargeBuildDTO.class);
        addChargeBuildDTO.setStartTime(DateUtil.format(addChargeBuildVO.getStartTime(), DateUtil.DATE_TYPE3));
        addChargeBuildDTO.setUserId(userSession.getUserId());
        addChargeBuildDTO.setUserName(userSession.getUserName());
        addChargeBuildDTO.setOrgId(userSession.getOrgId());
        return chargeBuildService.addChargeBuild(addChargeBuildDTO);
    }

    @PostMapping("importChargeBuild")
    @ApiOperation("导入充电桩")
    public BaseResponse importChargeBuild(@RequestParam("file")MultipartFile file, @SessionAttribute("user") UserSession userSession) {
        ImportChargeBuildDTO importChargeBuildDTO = new ImportChargeBuildDTO();
        importChargeBuildDTO.setFile(file);
        importChargeBuildDTO.setUserId(userSession.getUserId());
        importChargeBuildDTO.setUserName(userSession.getUserName());
        importChargeBuildDTO.setOrgId(userSession.getOrgId());
        return chargeBuildService.importChargeBuild(importChargeBuildDTO);
    }

    @PostMapping("updateChargeBuild")
    @ApiOperation("更新充电桩")
    public BaseResponse updateChargeBuild(@RequestBody @Validated UpdateChargeBuildVO updateChargeBuildVO, @SessionAttribute("user")UserSession userSession) {
        UpdateChargeBuildDTO updateChargeBuildDTO = ConvertUtil.convert(updateChargeBuildVO, UpdateChargeBuildDTO.class);
        updateChargeBuildDTO.setStartTime(DateUtil.format(updateChargeBuildVO.getStartTime(), DateUtil.DATE_TYPE3));
        updateChargeBuildDTO.setUserId(userSession.getUserId());
        updateChargeBuildDTO.setUserName(userSession.getUserName());
        updateChargeBuildDTO.setOrgId(userSession.getOrgId());
        return chargeBuildService.updateChargeBuild(updateChargeBuildDTO);
    }

    @PostMapping("deleteChargeBuild")
    @ApiOperation("删除充电桩")
    public BaseResponse deleteChargeBuild(@RequestBody @Validated DeleteChargeBuildVO deleteChargeBuildVO, @SessionAttribute("user")UserSession userSession) {
        return chargeBuildService.deleteChargeBuild(deleteChargeBuildVO.getChargeBuildSeq(), userSession.getOrgId());
    }


    @PostMapping("getChargeBuild")
    @ApiOperation("获取充电桩详情")
    public BaseResponse getChargeBuild(@RequestBody @Validated GetChargeBuildVO getChargeBuildVO) {
        return chargeBuildService.getChargeBuild(getChargeBuildVO.getChargeBuildSeq());
    }

    @PostMapping("searchChargeBuild")
    @ApiOperation("查询充电桩")
    public BaseResponse searchChargeBuild(@RequestBody @Validated SearchChargeBuildInfoVO searchChargeBuildInfoVO, @SessionAttribute("user") UserSession user) {
        SearchChargeBuildInfoDTO searchChargeBuildInfoDTO = ConvertUtil.convert(searchChargeBuildInfoVO, SearchChargeBuildInfoDTO.class);
        searchChargeBuildInfoDTO.setOrgId(user.getOrgId());
        searchChargeBuildInfoDTO.setOffset(CommonUtil.getOffset(searchChargeBuildInfoVO.getPageNum(), searchChargeBuildInfoVO.getPageSize()));
        searchChargeBuildInfoDTO.setLimit(searchChargeBuildInfoVO.getPageSize());
        return chargeBuildService.searchChargeBuild(searchChargeBuildInfoDTO);
    }

    @PostMapping("uploadChargeCopy")
    @ApiOperation("上传充电桩照片")
    public BaseResponse uploadChargeCopy(@RequestParam("file")MultipartFile file, @SessionAttribute("user") UserSession userSession) {
        UploadChargeCopyDTO uploadChargeCopyDTO = new UploadChargeCopyDTO();
        uploadChargeCopyDTO.setMultipartFile(file);
        uploadChargeCopyDTO.setUserId(userSession.getUserId());
        uploadChargeCopyDTO.setUserName(userSession.getUserName());
        uploadChargeCopyDTO.setOrgId(userSession.getOrgId());
        return chargeBuildService.uploadChargeCopy(uploadChargeCopyDTO);
    }

    @PostMapping("getChargeCopy")
    @ApiOperation("获取充电桩照片")
    @Authorize(accessAuthorize = false)
    public BaseResponse getChargeCopy(@RequestBody SingleId<String> id) {
        AssertUtil.hasText(id.getId(), "充电桩编号不能为空");
        return chargeBuildService.getChargeCopy(id.getId());
    }


    @PostMapping("importChargeBuildNote")
    @ApiOperation("导入充电桩日志")
    public BaseResponse importChargeBuildNote(@RequestBody SearchFileNoteVO searchFileNoteVO, @SessionAttribute("user") UserSession user) {
        SearchFileNoteDTO searchFileNoteDTO = ConvertUtil.convert(searchFileNoteVO, SearchFileNoteDTO.class);
        searchFileNoteDTO.setFileType(FileTypeEnum.IMPORT_CHARGE_BUILD_EXCEL);
        searchFileNoteDTO.setOrgId(user.getOrgId());
        return fileNoteService.searchImportFileNote(searchFileNoteDTO);
    }

    @PostMapping("importChargeBuildCopyNote")
    @ApiOperation("导入充电桩照片日志")
    public BaseResponse importChargeBuildCopyNote(@RequestBody SearchFileNoteVO searchFileNoteVO, @SessionAttribute("user") UserSession user) {
        SearchFileNoteDTO searchFileNoteDTO = ConvertUtil.convert(searchFileNoteVO, SearchFileNoteDTO.class);
        searchFileNoteDTO.setFileType(FileTypeEnum.CHARGE_PHOTO_COPY);
        searchFileNoteDTO.setOrgId(user.getOrgId());
        return fileNoteService.searchImportFileNote(searchFileNoteDTO);
    }





}
