package com.extracme.nevmp.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.SessionAttribute;

import com.extracme.nevmp.api.BaseResponse;
import com.extracme.nevmp.auth.Authorize;
import com.extracme.nevmp.auth.UserSession;
import com.extracme.nevmp.converter.ConvertUtil;
import com.extracme.nevmp.dto.common.ComboInfoBO;
import com.extracme.nevmp.dto.common.PageInfoBO;
import com.extracme.nevmp.dto.organization.GetOrgInfoDetailDTO;
import com.extracme.nevmp.dto.organization.OrgInfoDTO;
import com.extracme.nevmp.dto.organization.SaveOrgInfoDTO;
import com.extracme.nevmp.dto.organization.SearchOrgInfoDTO;
import com.extracme.nevmp.dto.organization.UpdateOrgInfoDTO;
import com.extracme.nevmp.model.OrgInfo;
import com.extracme.nevmp.service.OrganizationService;
import com.extracme.nevmp.vo.organization.DeleteOrgInfoVO;
import com.extracme.nevmp.vo.organization.GetOrgInfoDetailVO;
import com.extracme.nevmp.vo.organization.SaveOrgInfoVO;
import com.extracme.nevmp.vo.organization.SearchOrgInfoVO;
import com.extracme.nevmp.vo.organization.UpdateOrgInfoVO;

import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @Description
 */
@Authorize
@RestController
@RequestMapping("organization")
public class OrganizationController {


    @Autowired
    private OrganizationService organizationService;

    /**
     * 企业下拉列表框
     * @return
     */
    @ApiOperation(value = "汽车生产企业下拉框")
    @Authorize(accessAuthorize = false)
    @GetMapping("/comboOrganization")
    ComboInfoBO<String, String> comboOrganization(){
        return organizationService.comboOrgInfo();
    }


    @ApiOperation(value = "机构下拉框, type: 0: 管理机构 1：车企  4：桩企")
    @Authorize(accessAuthorize = false)
    @GetMapping("/comboAllOrganization")
    ComboInfoBO<String, String> comboOrgInfo(@RequestParam String type){
        return organizationService.getOrgInfoByKind(type);
    }


    @ApiOperation(value = "查询企业列表")
    @PostMapping("/searchOrgInfo")
    PageInfoBO<OrgInfo> searchOrgInfo(@RequestBody SearchOrgInfoVO searchOrgInfoVO, @SessionAttribute("user") UserSession user){
        //机构权限判断
        SearchOrgInfoDTO searchOrgInfoDTO = ConvertUtil.convert(searchOrgInfoVO, SearchOrgInfoDTO.class);
        return organizationService.searchOrgInfo(searchOrgInfoDTO);
    }

    @ApiOperation(value = "新增企业信息")
    @PostMapping("/saveOrgInfo")
    BaseResponse saveOrgInfo(@RequestBody SaveOrgInfoVO saveOrgInfoVO, @SessionAttribute("user") UserSession user){
        SaveOrgInfoDTO saveOrgInfoDTO = ConvertUtil.normalConvert(saveOrgInfoVO, SaveOrgInfoDTO.class);
        saveOrgInfoDTO.setCreatedUserId(user.getUserId());
        saveOrgInfoDTO.setCreatedUserName(user.getUserName());
        return organizationService.saveOrgInfo(saveOrgInfoDTO);
    }

    @ApiOperation(value = "修改企业信息")
    @PostMapping("/updateOrgInfo")
    BaseResponse updateOrgInfo(@RequestBody UpdateOrgInfoVO updateOrgInfoVO, @SessionAttribute("user") UserSession user){
        UpdateOrgInfoDTO updateOrgInfoDTO = ConvertUtil.normalConvert(updateOrgInfoVO, UpdateOrgInfoDTO.class);
        updateOrgInfoDTO.setUpdatedUserId(user.getUserId());
        updateOrgInfoDTO.setUpdatedUserName(user.getUserName());
        return organizationService.updateOrgInfo(updateOrgInfoDTO);
    }

    @ApiOperation(value = "获取企业信息详情")
    @PostMapping("/getOrgInfoDetail")
    OrgInfoDTO getOrgInfoDetail(@RequestBody GetOrgInfoDetailVO getOrgInfoDetailVO){
        GetOrgInfoDetailDTO getOrgInfoDetailDTO = new GetOrgInfoDetailDTO();
        getOrgInfoDetailDTO.setOrgId(getOrgInfoDetailVO.getOrgId());
        return organizationService.getOrgInfoDetail(getOrgInfoDetailDTO);
    }

    @ApiOperation(value = "获取企业信息详情")
    @PostMapping("/deleteOrgInfo")
    BaseResponse deleteOrgInfo(@RequestBody DeleteOrgInfoVO deleteOrgInfoVO){
        return organizationService.deleteOrgInfo(deleteOrgInfoVO.getOrgId());
    }


}
