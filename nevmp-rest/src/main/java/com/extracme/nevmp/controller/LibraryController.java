package com.extracme.nevmp.controller;

import com.extracme.nevmp.auth.UserSession;
import com.extracme.nevmp.dto.library.*;
import com.extracme.nevmp.enums.OrgKindEnum;
import com.extracme.nevmp.error.ServiceException;
import com.extracme.nevmp.service.library.LibraryService;
import com.extracme.nevmp.vo.library.QueryApplyDetailVO;
import com.extracme.nevmp.vo.library.QueryApplyProcessVO;
import com.extracme.nevmp.vo.library.SearchApplyVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 */
@RestController
@RequestMapping("library")
public class LibraryController {

    @Autowired
    private LibraryService libraryService;

    @PostMapping("/searchApply")
    public SearchApplyResponse searchApply(@RequestBody SearchApplyVO searchApplyVO){
        List<String> itemCodes = new ArrayList<>();
        itemCodes.add(searchApplyVO.getItemCode());
        QueryItemsApplyListResponse response = libraryService.queryItemsApplyList(itemCodes, searchApplyVO.getStatus(), searchApplyVO.getPageSize());
        return new SearchApplyResponse(response.getData().getApply());
    }

    @PostMapping("/queryApplyProcess")
    public QueryApplyProcessResponse queryApplyProcess(@RequestBody QueryApplyProcessVO queryApplyProcessVO){
        GetApplyProcessResponse response = libraryService.getApplyProcess(queryApplyProcessVO.getApplyNo());
        //转化返回结果
        return new QueryApplyProcessResponse(response.getData());
    }

    @PostMapping("/queryApplyDetail")
    public QueryApplyDetailResponse queryApplyDetail(@RequestBody QueryApplyDetailVO queryApplyDetailVO, @SessionAttribute("user") UserSession user){
        //校验登录人的机构信息
        if(!OrgKindEnum.MANAGER.getValue().equals(user.getOrgKind())){
            throw new ServiceException("您无权进行该查询操作");
        }

        QueryApplyDetailResponse response;
        GetApplyBaseResponse getApplyBaseResponse = libraryService.getApplyBase(queryApplyDetailVO.getApplyNo());
        if(getApplyBaseResponse.getData() != null && getApplyBaseResponse.getData().size() > 0){
            response = new QueryApplyDetailResponse(getApplyBaseResponse.getData().get(0));
        }else{
            response = new QueryApplyDetailResponse();
        }
        return response;
    }


    public void queryLibraryApplyCount(){

    }

}
