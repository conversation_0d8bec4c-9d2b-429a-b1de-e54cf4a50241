
package com.extracme.nevmp.controller;

import com.extracme.nevmp.api.BaseResponse;
import com.extracme.nevmp.auth.Authorize;
import com.extracme.nevmp.auth.RequestLock;
import com.extracme.nevmp.auth.UserSession;
import com.extracme.nevmp.converter.ConvertUtil;
import com.extracme.nevmp.dto.common.PageInfoBO;
import com.extracme.nevmp.dto.owner.AbnormalQualificationResponse;
import com.extracme.nevmp.dto.owner.QueryOwnerBaseQualificationResponse;
import com.extracme.nevmp.dto.owner.QueryOwnerSocialDetailDTO;
import com.extracme.nevmp.dto.owner.QueryOwnerSocialDetailResponse;
import com.extracme.nevmp.dto.qualification.owner.*;
import com.extracme.nevmp.enums.OrgKindEnum;
import com.extracme.nevmp.error.ServiceException;
import com.extracme.nevmp.model.OwnerQualification;
import com.extracme.nevmp.service.qualification.OwnerQualificationService;
import com.extracme.nevmp.vo.owner.QueryOwnerSocialDetailVO;
import com.extracme.nevmp.vo.qualification.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Description 用户资质管理
 */
@Slf4j
@Authorize
@RestController
@RequestMapping("/ownerQualification")
@Validated
@Api(value = "用户购车资质管理")
public class OwnerQualificationController {

    @Autowired
    private OwnerQualificationService ownerQualificationService;

    @PostMapping("/searchOwnerQualification")
    @ApiOperation(value = "查询意向用户列表（经信委）", httpMethod = "POST")
    public PageInfoBO<OwnerQualification> searchOwnerQualification(@RequestBody @Validated SearchOwnerQualificationVO searchOwnerQualificationVO){
        SearchOwnerQualificationDTO searchOwnerQualificationDTO = ConvertUtil.convert(searchOwnerQualificationVO, SearchOwnerQualificationDTO.class);
        if (searchOwnerQualificationDTO.getApplyTimeEnd() != null) {
            searchOwnerQualificationDTO.setApplyTimeEnd(new Date(searchOwnerQualificationDTO.getApplyTimeEnd().getTime() + TimeUnit.DAYS.toMillis(1)));
        }
        if (searchOwnerQualificationDTO.getReviewTimeEnd() != null) {
            searchOwnerQualificationDTO.setReviewTimeEnd(new Date(searchOwnerQualificationDTO.getReviewTimeEnd().getTime() + TimeUnit.DAYS.toMillis(1)));
        }

        //以旧换新材料审核
        if (searchOwnerQualificationDTO.getExchangeApplyTimeEnd() != null) {
            searchOwnerQualificationDTO.setExchangeApplyTimeEnd(new Date(searchOwnerQualificationDTO.getExchangeApplyTimeEnd().getTime() + TimeUnit.DAYS.toMillis(1)));
        }
        if (searchOwnerQualificationDTO.getExchangeReviewTimeEnd() != null) {
            searchOwnerQualificationDTO.setExchangeReviewTimeEnd(new Date(searchOwnerQualificationDTO.getExchangeReviewTimeEnd().getTime() + TimeUnit.DAYS.toMillis(1)));
        }

        return ownerQualificationService.searchOwnerQualification(searchOwnerQualificationDTO);
    }

    @PostMapping("/getOwnerQualificationDetail")
    @ApiOperation(value = "获取意向用户详细信息",  httpMethod = "POST")
    public OwnerQualificationDetailDTO getOwnerQualificationDetail(@RequestBody @Validated GetOwnerQualificationDetailVO getOwnerQualificationDetailVO){
        return ownerQualificationService.getOwnerQualificationDetail(getOwnerQualificationDetailVO.getId());
    }

    @PostMapping("/queryOwnerQualificationDetail")
    @ApiOperation(value = "查询意向用户详细信息",  httpMethod = "POST")
    public OwnerQualificationDetailDTO queryOwnerQualificationDetail(@RequestBody @Validated QueryOwnerQualificationDetailVO queryOwnerQualificationDetailVO, @SessionAttribute("user") UserSession user) {
        log.info("查询意向用户详细信息:" + queryOwnerQualificationDetailVO.getAuthId() +"-" + user.getOrgName());
        return ownerQualificationService.queryOwnerQualificationDetail(queryOwnerQualificationDetailVO.getAuthType(), queryOwnerQualificationDetailVO.getAuthId());
    }

    @PostMapping("/queryOwnerExchangeQualification")
    @ApiOperation(value = "查询以旧换新资格详情",  httpMethod = "POST")
    public OwnerQualificationDetailDTO queryOwnerExchangeQualification(@RequestBody @Validated QueryOwnerExchangeQualificationDetailVO queryOwnerExchangeQualificationDetailVO){
        return ownerQualificationService.queryOwnerExchangeQualification(queryOwnerExchangeQualificationDetailVO.getAuthType(), queryOwnerExchangeQualificationDetailVO.getAuthId(),
                queryOwnerExchangeQualificationDetailVO.getExchangeVehicleNo());
    }



    @PostMapping("/reconsiderFile")
    @ApiOperation(value = "上传复核材料",  httpMethod = "POST")
    public BaseResponse reconsiderFile(@RequestBody @Validated ReconsiderFileVO reconsiderFileVO, @SessionAttribute("user") UserSession user) {
        ReconsiderFileDTO reconsiderFileDTO = new ReconsiderFileDTO();
        reconsiderFileDTO.setId(reconsiderFileVO.getId());
        reconsiderFileDTO.setReconsiderationFileCopy(reconsiderFileVO.getReconsiderationFileCopy());
        reconsiderFileDTO.setOperatorId(user.getUserId());
        reconsiderFileDTO.setOperatorName(user.getUserName());
        reconsiderFileDTO.setReconsiderOrgName(user.getOrgName());
        return ownerQualificationService.reconsiderFile(reconsiderFileDTO);
    }
    @Deprecated
    @PostMapping("/reconsiderCreditQualification")
    @ApiOperation(value = "上传信用证明材料",  httpMethod = "POST")
    public BaseResponse reconsiderCreditQualification(@RequestBody @Validated ReconsiderFileVO reconsiderFileVO , @SessionAttribute("user") UserSession user){
//        ReconsiderFileDTO reconsiderFileDTO = new ReconsiderFileDTO();
//        reconsiderFileDTO.setId(reconsiderFileVO.getId());
//        reconsiderFileDTO.setReconsiderationFileCopy(reconsiderFileVO.getReconsiderationFileCopy());
//        reconsiderFileDTO.setOperatorId(user.getUserId());
//        reconsiderFileDTO.setOperatorName(user.getUserName());
//        return ownerQualificationService.reconsiderCreditQualification(reconsiderFileDTO);
        return new BaseResponse();
    }

    @RequestLock
    @PostMapping("/approveOwnerQualification")
    @ApiOperation(value = "意向用户审核通过",  httpMethod = "POST")
    public BaseResponse approveOwnerQualification(@RequestBody @Validated ApproveOwnerQualificationVO approveOwnerQualificationVO, @SessionAttribute("user") UserSession user){
        ApproveOwnerQualificationDTO approveOwnerQualificationDTO = new ApproveOwnerQualificationDTO();
        approveOwnerQualificationDTO.setId(approveOwnerQualificationVO.getId());
        approveOwnerQualificationDTO.setOperatorId(user.getUserId());
        approveOwnerQualificationDTO.setOperatorName(user.getUserName());
        return ownerQualificationService.approveOwnerQualification(approveOwnerQualificationDTO);
    }

    @RequestLock
    @PostMapping("/denyOwnerQualification")
    @ApiOperation(value = "意向用户审核拒绝",  httpMethod = "POST")
    public BaseResponse denyOwnerQualification(@RequestBody @Validated DenyOwnerQualificationVO denyOwnerQualificationVO, @SessionAttribute("user") UserSession user){
        DenyOwnerQualificationDTO denyOwnerQualificationDTO = new DenyOwnerQualificationDTO();
        denyOwnerQualificationDTO.setId(denyOwnerQualificationVO.getId());
        denyOwnerQualificationDTO.setReason(denyOwnerQualificationVO.getReason());
        denyOwnerQualificationDTO.setOperatorId(user.getUserId());
        denyOwnerQualificationDTO.setOperatorName(user.getOrgId());
        return ownerQualificationService.denyOwnerQualification(denyOwnerQualificationDTO);
    }


    @PostMapping("/reconsiderExchangeFile")
    @ApiOperation(value = "上传以旧换新证明材料",  httpMethod = "POST")
    public BaseResponse reconsiderExchangeFile(@RequestBody @Validated ReconsiderFileVO reconsiderFileVO, @SessionAttribute("user") UserSession user) {
        ReconsiderFileDTO reconsiderFileDTO = new ReconsiderFileDTO();
        reconsiderFileDTO.setId(reconsiderFileVO.getId());
        reconsiderFileDTO.setReconsiderationFileCopy(reconsiderFileVO.getReconsiderationFileCopy());
        reconsiderFileDTO.setOperatorId(user.getUserId());
        reconsiderFileDTO.setOperatorName(user.getUserName());
        reconsiderFileDTO.setReconsiderOrgName(user.getOrgName());
        ownerQualificationService.reconsiderExchangeFile(reconsiderFileDTO);
        return new BaseResponse();
    }

    @RequestLock
    @PostMapping("/approveExchangeQualification")
    @ApiOperation(value = "以旧换新材料审核通过",  httpMethod = "POST")
    public BaseResponse approveExchangeQualification(@RequestBody @Validated ApproveOwnerQualificationVO approveOwnerQualificationVO, @SessionAttribute("user") UserSession user){
        ApproveOwnerQualificationDTO approveOwnerQualificationDTO = new ApproveOwnerQualificationDTO();
        approveOwnerQualificationDTO.setId(approveOwnerQualificationVO.getId());
        approveOwnerQualificationDTO.setOperatorId(user.getUserId());
        approveOwnerQualificationDTO.setOperatorName(user.getUserName());
        ownerQualificationService.approveExchangeQualification(approveOwnerQualificationDTO);
        return new BaseResponse();
    }

    @RequestLock
    @PostMapping("/denyExchangeQualification")
    @ApiOperation(value = "以旧换新材料审核拒绝",  httpMethod = "POST")
    public BaseResponse denyExchangeQualification(@RequestBody @Validated DenyOwnerQualificationVO denyOwnerQualificationVO, @SessionAttribute("user") UserSession user){
        DenyOwnerQualificationDTO denyOwnerQualificationDTO = new DenyOwnerQualificationDTO();
        denyOwnerQualificationDTO.setId(denyOwnerQualificationVO.getId());
        denyOwnerQualificationDTO.setReason(denyOwnerQualificationVO.getReason());
        denyOwnerQualificationDTO.setOperatorId(user.getUserId());
        denyOwnerQualificationDTO.setOperatorName(user.getOrgId());
        ownerQualificationService.denyExchangeQualification(denyOwnerQualificationDTO);
        return new BaseResponse();
    }

    @RequestLock
    @PostMapping("/invalidExchangeQualification")
    @ApiOperation(value = "失效以旧换新材料资格",  httpMethod = "POST")
    public BaseResponse invalidExchangeQualification(@RequestBody InvalidOwnerQualificationVO invalidOwnerQualificationVO, @SessionAttribute("user") UserSession user){
        InvalidOwnerQualificationDTO invalidOwnerQualificationDTO = new InvalidOwnerQualificationDTO();
        invalidOwnerQualificationDTO.setId(invalidOwnerQualificationVO.getId());
        invalidOwnerQualificationDTO.setReason(invalidOwnerQualificationVO.getReason());
        invalidOwnerQualificationDTO.setOperatorId(user.getUserId());
        invalidOwnerQualificationDTO.setOperatorName(user.getUserName());
        ownerQualificationService.invalidExchangeQualification(invalidOwnerQualificationDTO);
        return new BaseResponse();
    }


    @RequestLock
    @PostMapping(value = "/saveForeignerQualificationInfo")
    @ApiOperation(value = "添加外籍意向用户信息",  httpMethod = "POST")
    public BaseResponse saveForeignerQualificationInfo(@RequestBody SaveForeignerQualificationInfoVO saveForeignerQualificationInfoVO, @SessionAttribute("user") UserSession user){
        SaveForeignerQualificationInfoDTO saveForeignerQualificationInfoDTO = ConvertUtil.convert(saveForeignerQualificationInfoVO, SaveForeignerQualificationInfoDTO.class);
        saveForeignerQualificationInfoDTO.setCreatedUserId(user.getUserId());
        saveForeignerQualificationInfoDTO.setCreatedUserName(user.getUserName());
        saveForeignerQualificationInfoDTO.setCreatedUserOrgName(user.getOrgName());
        return ownerQualificationService.saveForeignerQualificationInfo(saveForeignerQualificationInfoDTO);
    }

    @RequestLock
    @PostMapping(value = "/saveMilitaryQualificationInfo")
    @ApiOperation(value = "添加军官意向用户信息",  httpMethod = "POST")
    public BaseResponse saveMilitaryQualificationInfo(@RequestBody SaveMilitaryQualificationInfoVO saveMilitaryQualificationInfoVO, @SessionAttribute("user") UserSession user){
        SaveMilitaryQualificationInfoDTO saveMilitaryQualificationInfoDTO = ConvertUtil.convert(saveMilitaryQualificationInfoVO, SaveMilitaryQualificationInfoDTO.class);
        saveMilitaryQualificationInfoDTO.setCreatedUserId(user.getUserId());
        saveMilitaryQualificationInfoDTO.setCreatedUserName(user.getUserName());
        saveMilitaryQualificationInfoDTO.setCreatedUserOrgName(user.getOrgName());
        return ownerQualificationService.saveMilitaryQualificationInfo(saveMilitaryQualificationInfoDTO);
    }



    @RequestLock
    @PostMapping(value = "/invalidOwnerQualification")
    @ApiOperation(value = "失效意向用户",  httpMethod = "POST")
    public BaseResponse invalidOwnerQualification(@RequestBody InvalidOwnerQualificationVO invalidOwnerQualificationVO, @SessionAttribute("user") UserSession user){
        InvalidOwnerQualificationDTO invalidOwnerQualificationDTO = new InvalidOwnerQualificationDTO();
        invalidOwnerQualificationDTO.setId(invalidOwnerQualificationVO.getId());
        invalidOwnerQualificationDTO.setReason(invalidOwnerQualificationVO.getReason());
        invalidOwnerQualificationDTO.setOperatorId(user.getUserId());
        invalidOwnerQualificationDTO.setOperatorName(user.getUserName());
        return ownerQualificationService.invalidOwnerQualification(invalidOwnerQualificationDTO);
    }

    @RequestLock
    @PostMapping(value = "/batchInvalidOwnerQualification")
    @ApiOperation(value = "失效意向用户",  httpMethod = "POST")
    public BaseResponse batchInvalidOwnerQualification(@RequestBody BatchInvalidOwnerQualificationVO batchInvalidOwnerQualificationVO, @SessionAttribute("user") UserSession user){
        BatchInvalidOwnerQualificationDTO batchInvalidOwnerQualificationDTO = new BatchInvalidOwnerQualificationDTO();
        batchInvalidOwnerQualificationDTO.setIds(batchInvalidOwnerQualificationVO.getIds());
        batchInvalidOwnerQualificationDTO.setReason(batchInvalidOwnerQualificationVO.getReason());
        batchInvalidOwnerQualificationDTO.setOperatorId(user.getUserId());
        batchInvalidOwnerQualificationDTO.setOperatorName(user.getUserName());
        return ownerQualificationService.batchInvalidOwnerQualification(batchInvalidOwnerQualificationDTO);
    }

    @PostMapping(value = "/searchAbnormalQualification")
    @ApiOperation(value = "查询异常用户资质",  httpMethod = "POST")
    public AbnormalQualificationResponse searchAbnormalQualification(@RequestBody SearchAbnormalQualificationVO searchAbnormalQualificationVO, @SessionAttribute("user") UserSession user){
        SearchAbnormalQualificationDTO searchAbnormalQualificationDTO = ConvertUtil.convert(searchAbnormalQualificationVO, SearchAbnormalQualificationDTO.class);
        searchAbnormalQualificationDTO.setOrgId(user.getOrgId());
        return ownerQualificationService.searchAbnormalQualification(searchAbnormalQualificationDTO);
    }

    @RequestLock
    @Authorize(loginAuthorize = false)
    @GetMapping("/dealOmitQualificationApply")
    @ApiOperation(value = "手动触发处理遗漏的购车资格办件",  httpMethod = "GET")
    public BaseResponse dealOmitQualificationApply(){
        return ownerQualificationService.dealOmitQualificationApply();
    }


    @PostMapping("/queryOwnerBaseQualification")
    @ApiOperation(value = "查询用户基础资质信息",  httpMethod = "POST")
    public QueryOwnerBaseQualificationResponse queryOwnerBaseQualification(@RequestBody QueryOwnerBaseQualificationVO queryOwnerBaseQualificationVO){
        return ownerQualificationService.queryOwnerBaseQualification(queryOwnerBaseQualificationVO.getName(), queryOwnerBaseQualificationVO.getAuthType(),queryOwnerBaseQualificationVO.getAuthId());
    }



    @PostMapping("/queryOwnerSocialDetail")
    @ApiOperation(value = "按照指定时间查询用户社保信息",  httpMethod = "POST")
    public QueryOwnerSocialDetailResponse queryOwnerSocialDetail(@RequestBody QueryOwnerSocialDetailVO queryOwnerSocialDetailVO, @SessionAttribute("user") UserSession user){
        //校验登录人的机构信息
        if(!OrgKindEnum.MANAGER.getValue().equals(user.getOrgKind())){
            throw new ServiceException("您无权进行该查询操作");
        }

        QueryOwnerSocialDetailDTO queryOwnerSocialDetailDTO = ConvertUtil.convert(queryOwnerSocialDetailVO, QueryOwnerSocialDetailDTO.class);
        return ownerQualificationService.queryOwnerSocialDetail(queryOwnerSocialDetailDTO);
    }
}
