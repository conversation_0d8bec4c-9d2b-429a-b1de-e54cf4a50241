package com.extracme.nevmp.controller;

import com.extracme.nevmp.api.BaseResponse;
import com.extracme.nevmp.auth.Authorize;
import com.extracme.nevmp.auth.RequestLock;
import com.extracme.nevmp.auth.UserSession;
import com.extracme.nevmp.converter.ConvertUtil;
import com.extracme.nevmp.dto.charge.QueryChargePhotoDTO;
import com.extracme.nevmp.dto.common.FileInfoBO;
import com.extracme.nevmp.dto.common.PageInfoBO;
import com.extracme.nevmp.dto.gov.traffic.OwnerVehicleInfoDTO;
import com.extracme.nevmp.dto.owner.*;
import com.extracme.nevmp.dto.owner.secondhand.SearchSecondHandVehicleAppliedOwnerDTO;
import com.extracme.nevmp.dto.owner.secondhand.SecondHandVehicleAppliedOwnerDTO;
import com.extracme.nevmp.dto.owner.secondhand.OwnerUnbindDetailResponse;
import com.extracme.nevmp.enums.OrgKindEnum;
import com.extracme.nevmp.error.ServiceException;
import com.extracme.nevmp.model.UploadFileNotes;
import com.extracme.nevmp.model.UserOperateLog;
import com.extracme.nevmp.service.owner.OwnerService;
import com.extracme.nevmp.utils.CommonUtil;
import com.extracme.nevmp.utils.DateUtil;
import com.extracme.nevmp.vo.charge.QueryBusinessBatchPurchaseReportVO;
import com.extracme.nevmp.vo.charge.QueryChargePhotoVO;
import com.extracme.nevmp.vo.common.SingleId;
import com.extracme.nevmp.vo.owner.*;
import com.extracme.nevmp.vo.owner.secondhand.ApproveSecondHandVehicleOwnerUnbindVO;
import com.extracme.nevmp.vo.owner.secondhand.DenySecondHandVehicleOwnerUnbindVO;
import com.extracme.nevmp.vo.owner.secondhand.QuerySecondHandVehicleOwnerUnbindDetailVO;
import com.extracme.nevmp.vo.owner.secondhand.SearchSecondHandVehicleAppliedOwnerVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Description 购车人 服务
 */
@EnableAsync
@Validated
@RestController
@Authorize()
@RequestMapping("owner")
public class OwnerController {

    @Autowired
    private OwnerService ownerService;


    @PostMapping("/queryOwnerInfoDetail")
    @ApiOperation(value = "查询充电桩确认详情", httpMethod = "POST")
    public QueryOwnerInfoDetailResponse queryOwnerInfoDetail(@RequestBody QueryOwnerInfoDetailVO queryOwnerInfoDetailVO, @SessionAttribute("user") UserSession user) {
        QueryOwnerInfoDetailDTO queryOwnerInfoDetailDTO = new QueryOwnerInfoDetailDTO();
        queryOwnerInfoDetailDTO.setId(queryOwnerInfoDetailVO.getId());
        queryOwnerInfoDetailDTO.setOrgId(user.getOrgId());
        return ownerService.queryOwnerInfoDetail(queryOwnerInfoDetailDTO);
    }

    @PostMapping("/searchOwnerInfo")
    @ApiOperation(value = "查询充电条件确认列表", httpMethod = "POST")
    public PageInfoBO<OwnerInfoDTO> searchOwnerInfo(@RequestBody SearchOwnerInfoVO searchOwnerInfoVO, @SessionAttribute("user") UserSession user) {
        SearchOwnerInfoDTO searchOwnerInfoDTO = ConvertUtil.convert(searchOwnerInfoVO, SearchOwnerInfoDTO.class);
        if(searchOwnerInfoVO.getCreatedTimeStart() != null){
            searchOwnerInfoDTO.setCreatedTimeStart( DateUtil.format(searchOwnerInfoVO.getCreatedTimeStart(), DateUtil.DATE_TYPE1));
        }
        if (searchOwnerInfoVO.getCreatedTimeEnd() != null) {
            searchOwnerInfoDTO.setCreatedTimeEnd(DateUtil.format(new Date(searchOwnerInfoVO.getCreatedTimeEnd().getTime() + TimeUnit.DAYS.toMillis(1)),DateUtil.DATE_TYPE1));
        }
        if (searchOwnerInfoVO.getApplyTimeEnd() != null) {
            searchOwnerInfoDTO.setApplyTimeEnd(new Date(searchOwnerInfoVO.getApplyTimeEnd().getTime() + TimeUnit.DAYS.toMillis(1)));
        }
        if (searchOwnerInfoVO.getReviewTimeEnd() != null) {
            searchOwnerInfoDTO.setReviewTimeEnd(new Date(searchOwnerInfoVO.getReviewTimeEnd().getTime() + TimeUnit.DAYS.toMillis(1)));
        }
        searchOwnerInfoDTO.setOrgId(user.getOrgId());
        return ownerService.searchOwnerInfo(searchOwnerInfoDTO);
    }

    @RequestLock
    @Authorize(loginAuthorize = false)
    @PostMapping("/exportOwnerInfo")
    @ApiOperation(value = "导出充电条件确认列表", httpMethod = "POST")
    public ResponseEntity<byte[]> exportOwnerInfo(@RequestBody SearchOwnerInfoVO searchOwnerInfoVO, @SessionAttribute("user") UserSession user) {
        ExportOwnerInfoDTO exportOwnerInfoDTO = ConvertUtil.convert(searchOwnerInfoVO, ExportOwnerInfoDTO.class);
        if(searchOwnerInfoVO.getCreatedTimeStart() != null){
            exportOwnerInfoDTO.setCreatedTimeStart( DateUtil.format(searchOwnerInfoVO.getCreatedTimeStart(), DateUtil.DATE_TYPE1));
        }
        if (searchOwnerInfoVO.getCreatedTimeEnd() != null) {
            exportOwnerInfoDTO.setCreatedTimeEnd(DateUtil.format(new Date(searchOwnerInfoVO.getCreatedTimeEnd().getTime() + TimeUnit.DAYS.toMillis(1)),DateUtil.DATE_TYPE1));
        }
        if (searchOwnerInfoVO.getApplyTimeEnd() != null) {
            exportOwnerInfoDTO.setApplyTimeEnd(new Date(searchOwnerInfoVO.getApplyTimeEnd().getTime() + TimeUnit.DAYS.toMillis(1)));
        }
        if (searchOwnerInfoVO.getReviewTimeEnd() != null) {
            exportOwnerInfoDTO.setReviewTimeEnd(new Date(searchOwnerInfoVO.getReviewTimeEnd().getTime() + TimeUnit.DAYS.toMillis(1)));
        }
        exportOwnerInfoDTO.setOrgId(user.getOrgId());
        exportOwnerInfoDTO.setOperatorId(user.getUserId());
        exportOwnerInfoDTO.setOperatorName(user.getUserName());
        byte[] bytes =  ownerService.exportOwnerInfo(exportOwnerInfoDTO);

        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Disposition", "attachment;filename=" + System.currentTimeMillis() + ".xlsx");
        headers.add("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
        return new ResponseEntity<>(bytes, headers, HttpStatus.OK);
    }

    @PostMapping("/searchOwnerConfirmList")
    @ApiOperation(value = "查询充电条件确认-待主体确认列表", httpMethod = "POST")
    public PageInfoBO<OwnerConfirmInfoDTO> searchOwnerConfirmList(@RequestBody SearchOwnerConfirmListVO searchOwnerConfirmListVO, @SessionAttribute("user") UserSession user) {
        SearchOwnerConfirmListDTO searchOwnerConfirmListDTO = ConvertUtil.convert(searchOwnerConfirmListVO, SearchOwnerConfirmListDTO.class);
        searchOwnerConfirmListDTO.setOrgId(user.getOrgId());
        return ownerService.searchOwnerConfirmList(searchOwnerConfirmListDTO);
    }

    @PostMapping("/searchAppliedOwnerInfo")
    @ApiOperation(value = "查询已提交审核的充电条件确认列表", httpMethod = "POST")
    public PageInfoBO<OwnerInfoDTO> searchAppliedOwnerInfo(@RequestBody SearchAppliedOwnerInfoVO searchAppliedOwnerInfoVO) {
        SearchAppliedOwnerInfoDTO searchAppliedOwnerInfoDTO = ConvertUtil.convert(searchAppliedOwnerInfoVO, SearchAppliedOwnerInfoDTO.class);
        if (searchAppliedOwnerInfoDTO.getApplyTimeEnd() != null) {
            searchAppliedOwnerInfoDTO.setApplyTimeEnd(new Date(searchAppliedOwnerInfoDTO.getApplyTimeEnd().getTime() + TimeUnit.DAYS.toMillis(1)));
        }
        if (searchAppliedOwnerInfoDTO.getReviewTimeEnd() != null) {
            searchAppliedOwnerInfoDTO.setReviewTimeEnd(new Date(searchAppliedOwnerInfoDTO.getReviewTimeEnd().getTime() + TimeUnit.DAYS.toMillis(1)));
        }
        return ownerService.searchAppliedOwnerInfo(searchAppliedOwnerInfoDTO);
    }

    @RequestLock
    @PostMapping("/importOwnerInfo")
    @ApiOperation(value = "导入充电条件确认信息", httpMethod = "POST")
    public BaseResponse importOwnerInfo(@RequestParam(value = "file") MultipartFile multipartFile, @SessionAttribute("user") UserSession user) {
        ImportOwnerInfoDTO importOwnerInfoDTO = new ImportOwnerInfoDTO();
        importOwnerInfoDTO.setMultipartFile(multipartFile);
        importOwnerInfoDTO.setOrgId(user.getOrgId());
        importOwnerInfoDTO.setOrgName(user.getOrgName());
        importOwnerInfoDTO.setOperateUserId(user.getUserId());
        importOwnerInfoDTO.setOperateUserName(user.getUserName());
        return ownerService.importOwnerInfo(importOwnerInfoDTO);
    }

    @Authorize(accessAuthorize = false)
    @PostMapping("/searchOwnerUploadFileNotes")
    @ApiOperation(value = "查询充电条件信息导入日志", httpMethod = "POST")
    public PageInfoBO<UploadFileNotes> searchOwnerUploadFileNotes(@RequestBody @Valid SearchOwnerUploadFileNotesVO searchOwnerUploadFileNotesVO, @SessionAttribute("user") UserSession user) {
        SearchOwnerUploadFileNotesDTO searchOwnerUploadFileNotesDTO = ConvertUtil.convert(searchOwnerUploadFileNotesVO, SearchOwnerUploadFileNotesDTO.class);
        //根据机构进行查询，统一机构内，日志数据共享
        searchOwnerUploadFileNotesDTO.setOrgId(user.getOrgId());
        return ownerService.searchOwnerUploadFileNotes(searchOwnerUploadFileNotesDTO);
    }


    @RequestLock
    @Authorize(accessAuthorize = false)
    @PostMapping("/importChargeConfirmScanningCopy")
    @ApiOperation(value = "上传用户承诺书扫描件", httpMethod = "POST")
    public BaseResponse importChargeConfirmScanningCopy(@RequestParam("file") MultipartFile file, @SessionAttribute("user") UserSession user) {
        ImportChargeConfirmScanningCopyDTO importChargeConfirmScanningCopyDTO = new ImportChargeConfirmScanningCopyDTO();
        importChargeConfirmScanningCopyDTO.setMultipartFile(file);
        importChargeConfirmScanningCopyDTO.setOrgId(user.getOrgId());
        importChargeConfirmScanningCopyDTO.setUpdatedUserId(user.getUserId());
        importChargeConfirmScanningCopyDTO.setUpdatedUserName(user.getUserName());
        return ownerService.importChargeConfirmScanningCopy(importChargeConfirmScanningCopyDTO);
    }

    @RequestLock
    @Authorize(accessAuthorize = false)
    @PostMapping("/importChargePhotoScanningCopy")
    @ApiOperation(value = "上传充电桩车位照片", httpMethod = "POST")
    public BaseResponse importChargePhotoScanningCopy(@RequestParam("file") MultipartFile file, @SessionAttribute("user") UserSession user){
        ImportChargePhotoScanningCopyDTO importChargePhotoScanningCopyDTO =  new ImportChargePhotoScanningCopyDTO();
        importChargePhotoScanningCopyDTO.setMultipartFile(file);
        importChargePhotoScanningCopyDTO.setOrgId(user.getOrgId());
        importChargePhotoScanningCopyDTO.setUpdatedUserId(user.getUserId());
        importChargePhotoScanningCopyDTO.setUpdatedUserName(user.getUserName());
        return ownerService.importChargePhotoScanningCopy(importChargePhotoScanningCopyDTO);
    }

    @PostMapping("/searchChargeConfirmUploadFileNotes")
    @ApiOperation(value = "查询用户承诺书导入日志", httpMethod = "POST")
    public PageInfoBO<UploadFileNotes> searchChargeConfirmUploadFileNotes(@RequestBody @Valid SearchChargeConfirmUploadFileNotesVO searchChargeConfirmUploadFileNotesVO, @SessionAttribute("user") UserSession user) {
        SearchChargeConfirmUploadFileNotesDTO searchChargeConfirmUploadFileNotesDTO = ConvertUtil.convert(searchChargeConfirmUploadFileNotesVO, SearchChargeConfirmUploadFileNotesDTO.class);
        //根据机构进行查询，统一机构内，日志数据共享
        searchChargeConfirmUploadFileNotesDTO.setOrgId(user.getOrgId());
        return ownerService.searchChargeConfirmUploadFileNotes(searchChargeConfirmUploadFileNotesDTO);
    }

    @PostMapping("/searchChargePhotoUploadFileNotes")
    @ApiOperation(value = "查询用户充电照片导入日志", httpMethod = "POST")
    public PageInfoBO<UploadFileNotes> searchChargePhotoUploadFileNotes(@RequestBody @Valid SearchChargePhotoUploadFileNotesVO searchChargePhotoUploadFileNotesVO,@SessionAttribute("user") UserSession user){
        SearchChargePhotoUploadFileNotesDTO searchChargePhotoUploadFileNotesDTO = ConvertUtil.convert(searchChargePhotoUploadFileNotesVO, SearchChargePhotoUploadFileNotesDTO.class);
        searchChargePhotoUploadFileNotesDTO.setOrgId(user.getOrgId());
        return ownerService.searchChargePhotoUploadFileNotes(searchChargePhotoUploadFileNotesDTO);
    }

    @RequestLock
    @PostMapping("/rollbackOwnerInfo")
    @ApiOperation(value = "撤销充电条件确认审核", httpMethod = "POST")
    public BaseResponse rollbackOwnerInfo(@RequestBody RollbackOwnerInfoVO rollbackOwnerInfoVO, @SessionAttribute("user") UserSession user) {
        RollbackOwnerInfoDTO rollbackOwnerInfoDTO = new RollbackOwnerInfoDTO();
        rollbackOwnerInfoDTO.setId(rollbackOwnerInfoVO.getId());
        rollbackOwnerInfoDTO.setReason(rollbackOwnerInfoVO.getReason());
        rollbackOwnerInfoDTO.setOrgId(user.getOrgId());
        rollbackOwnerInfoDTO.setUpdatedUserId(user.getUserId());
        rollbackOwnerInfoDTO.setUpdatedUserName(user.getUserName());
        return ownerService.rollbackOwnerInfo(rollbackOwnerInfoDTO);

    }
    @RequestLock
    @PostMapping("/queryOwnerVehicleInfo")
    @ApiOperation(value = "查询用户名下车辆列表-数据资源平台", httpMethod = "POST")
    public PageInfoBO<OwnerVehicleInfoDTO> queryOwnerVehicleInfo(@RequestBody QueryOwnerVehicleInfoVO queryOwnerVehicleInfoVO, @SessionAttribute("user") UserSession user){
        if(!OrgKindEnum.MANAGER.getValue().equals(user.getOrgKind())){
            throw new ServiceException("您无权进行该查询操作");
        }
        return ownerService.queryOwnerVehicleInfo(queryOwnerVehicleInfoVO.getAuthId());
    }

    @RequestLock
    @PostMapping("/unbindOwnerInfo")
    @ApiOperation(value = "解绑用户信息", httpMethod = "POST")
    public BaseResponse unbindOwnerInfo(@RequestBody UnbindOwnerInfoVO unbindOwnerInfoVO, @SessionAttribute("user") UserSession user) {
        UnbindOwnerInfoDTO unbindOwnerInfoDTO = new UnbindOwnerInfoDTO();
        unbindOwnerInfoDTO.setId(unbindOwnerInfoVO.getId());
        unbindOwnerInfoDTO.setOrgId(user.getOrgId());
        unbindOwnerInfoDTO.setUpdatedUserId(user.getUserId());
        unbindOwnerInfoDTO.setUpdatedUserName(user.getUserName());
        return ownerService.unbindOwnerInfo(unbindOwnerInfoDTO);
    }


    @RequestLock
    @PostMapping("/batchUnbindOwnerInfo")
    @ApiOperation(value = "批量解绑用户信息", httpMethod = "POST")
    public BaseResponse batchUnbindOwnerInfo(@RequestBody BatchUnbindOwnerInfoVO batchUnbindOwnerInfoVO, @SessionAttribute("user") UserSession user) {
        BatchUnbindOwnerInfoDTO batchUnbindOwnerInfoDTO = new BatchUnbindOwnerInfoDTO();
        batchUnbindOwnerInfoDTO.setIds(batchUnbindOwnerInfoVO.getIds());
        batchUnbindOwnerInfoDTO.setOrgId(user.getOrgId());
        batchUnbindOwnerInfoDTO.setUpdatedUserId(user.getUserId());
        batchUnbindOwnerInfoDTO.setUpdatedUserName(user.getUserName());
        return ownerService.batchUnbindOwnerInfo(batchUnbindOwnerInfoDTO);
    }

    @RequestLock
    @PostMapping("/recoverBindOwnerInfo")
    @ApiOperation(value = "恢复绑定用户信息", httpMethod = "POST")
    public BaseResponse recoverBindOwnerInfo(@RequestBody RecoverBindOwnerInfoVO recoverBindOwnerInfoVO,@SessionAttribute("user") UserSession user){
        RecoverBindOwnerInfoDTO recoverBindOwnerInfoDTO = new RecoverBindOwnerInfoDTO();
        recoverBindOwnerInfoDTO.setId(recoverBindOwnerInfoVO.getId());
        recoverBindOwnerInfoDTO.setOrgId(user.getOrgId());
        recoverBindOwnerInfoDTO.setUpdatedUserId(user.getUserId());
        recoverBindOwnerInfoDTO.setUpdatedUserName(user.getUserName());
        return ownerService.recoverBindOwnerInfo(recoverBindOwnerInfoDTO);
    }

    @RequestLock
    @PostMapping("/queryOwnerVehicles")
    @ApiOperation(value = "查询用户名下车辆列表", httpMethod = "POST")
    public QueryOwnerVehiclesDTO queryOwnerVehicles(@RequestBody QueryOwnerVehiclesVO queryOwnerVehiclesVO){
         return ownerService.queryOwnerVehicles(queryOwnerVehiclesVO.getId());
    }



    @RequestLock
    @PostMapping("/save")
    @ApiOperation(value = "保存充电条件确认信息", httpMethod = "POST")
    public BaseResponse save(@RequestBody SaveOwnerInfoVO saveOwnerInfoVO, @SessionAttribute("user") UserSession user) {
        SaveOwnerInfoDTO saveOwnerInfoDTO = ConvertUtil.convert(saveOwnerInfoVO, SaveOwnerInfoDTO.class);
        saveOwnerInfoDTO.setOrgId(user.getOrgId());
        saveOwnerInfoDTO.setCreatedUserId(user.getUserId());
        saveOwnerInfoDTO.setCreatedUserName(user.getUserName());
        return ownerService.save(saveOwnerInfoDTO);
    }

    @RequestLock
    @PostMapping("/saveExchange")
    public BaseResponse saveExchange(@RequestBody SaveOwnerExchangeVO saveOwnerExchangeVO, @SessionAttribute("user") UserSession user){
        if(!OrgKindEnum.MANAGER.getValue().equals(user.getOrgKind())){
            throw new ServiceException("您无权进行该查询操作");
        }

        SaveOwnerExchangeDTO saveOwnerExchangeDTO = ConvertUtil.convert(saveOwnerExchangeVO, SaveOwnerExchangeDTO.class);
        saveOwnerExchangeDTO.setOrgId(user.getOrgId());
        saveOwnerExchangeDTO.setCreatedUserId(user.getUserId());
        saveOwnerExchangeDTO.setCreatedUserName(user.getUserName());
        return ownerService.saveExchange(saveOwnerExchangeDTO);
    }


    @PostMapping("/getOwnerInfoDetail")
    @ApiOperation(value = "获取充电条件确认详情", httpMethod = "POST")
    public GetOwnerInfoDetailResponse getOwnerInfoDetail(@RequestBody GetOwnerInfoDetailVO getOwnerInfoDetailVO, @SessionAttribute("user") UserSession user) {
        GetOwnerInfoDetailDTO getOwnerInfoDetailDTO = new GetOwnerInfoDetailDTO();
        getOwnerInfoDetailDTO.setId(getOwnerInfoDetailVO.getId());
        getOwnerInfoDetailDTO.setOrgId(user.getOrgId());
        return ownerService.getOwnerInfoDetail(getOwnerInfoDetailDTO);
    }

    @RequestLock
    @PostMapping("/update")
    @ApiOperation(value = "更新充电条件确认信息", httpMethod = "POST")
    public BaseResponse update(@RequestBody UpdateOwnerInfoVO updateOwnerInfoVO, @SessionAttribute("user") UserSession user) {
        UpdateOwnerInfoDTO updateOwnerInfoDTO = ConvertUtil.normalConvert(updateOwnerInfoVO, UpdateOwnerInfoDTO.class);
        updateOwnerInfoDTO.setUpdatedUserId(user.getUserId());
        updateOwnerInfoDTO.setUpdatedUserName(user.getUserName());
        return ownerService.update(updateOwnerInfoDTO);
    }

    @RequestLock
    @PostMapping("/delete")
    @ApiOperation(value = "删除充电条件确认信息", httpMethod = "POST")
    public BaseResponse delete(@RequestBody DeleteOwnerInfoVO deleteOwnerInfoVO, @SessionAttribute("user") UserSession user) {
        DeleteOwnerInfoDTO deleteOwnerInfoDTO = new DeleteOwnerInfoDTO();
        deleteOwnerInfoDTO.setId(deleteOwnerInfoVO.getId());
        deleteOwnerInfoDTO.setOrgId(user.getOrgId());
        deleteOwnerInfoDTO.setUpdatedUserId(user.getUserId());
        deleteOwnerInfoDTO.setUpdatedUserName(user.getUserName());
        return ownerService.delete(deleteOwnerInfoDTO);
    }

    @RequestLock
    @PostMapping("/cancel")
    @ApiOperation(value = "撤销充电条件确认申请-待主体确认状态", httpMethod = "POST")
    public BaseResponse cancel(@RequestBody CancelOwnerInfoVO cancelOwnerInfoVO, @SessionAttribute("user") UserSession user) {
        CancelOwnerInfoDTO cancelOwnerInfoDTO = new CancelOwnerInfoDTO();
        cancelOwnerInfoDTO.setId(cancelOwnerInfoVO.getId());
        cancelOwnerInfoDTO.setCancelReason(cancelOwnerInfoVO.getCancelReason());
        cancelOwnerInfoDTO.setUpdatedUserId(user.getUserId());
        cancelOwnerInfoDTO.setUpdatedUserName(user.getUserName());
        return ownerService.cancel(cancelOwnerInfoDTO);
    }

    @RequestLock
    @PostMapping("/approve")
    @ApiOperation(value = "充电条件确认信息审核通过", httpMethod = "POST")
    public BaseResponse approve(@RequestBody @Valid ApproveOwnerInfoVO approveOwnerInfoVO, @SessionAttribute("user") UserSession user) {
        ApproveOwnerInfoDTO approveOwnerInfoDTO = new ApproveOwnerInfoDTO();
        approveOwnerInfoDTO.setIds(approveOwnerInfoVO.getIds());
        approveOwnerInfoDTO.setUpdatedUserId(user.getUserId());
        approveOwnerInfoDTO.setUpdatedUserName(user.getUserName());
        return ownerService.approve(approveOwnerInfoDTO);
    }

    @RequestLock
    @PostMapping("/deny")
    @ApiOperation(value = "充电条件确认信息审核拒绝", httpMethod = "POST")
    public BaseResponse deny(@RequestBody @Valid DenyOwnerInfoVO denyOwnerInfoVO, @SessionAttribute("user") UserSession user) {
        DenyOwnerInfoDTO denyOwnerInfoDTO = new DenyOwnerInfoDTO();
        denyOwnerInfoDTO.setIds(denyOwnerInfoVO.getIds());
        denyOwnerInfoDTO.setReason(denyOwnerInfoVO.getReason());
        denyOwnerInfoDTO.setUpdatedUserId(user.getUserId());
        denyOwnerInfoDTO.setUpdatedUserName(user.getUserName());
        return ownerService.deny(denyOwnerInfoDTO);
    }

    @RequestLock
    @PostMapping("/applyToOwnerConfirm")
    @ApiOperation(value = "提交充电条件确认信息至主体", httpMethod = "POST")
    public BaseResponse applyToOwnerConfirm(@RequestBody ApplyToOwnerConfirmVO applyToOwnerConfirmVO, @SessionAttribute("user") UserSession user) {
        ApplyToOwnerConfirmDTO applyToOwnerConfirmDTO = new ApplyToOwnerConfirmDTO();
        applyToOwnerConfirmDTO.setIds(applyToOwnerConfirmVO.getIds());
        applyToOwnerConfirmDTO.setUpdatedUserId(user.getUserId());
        applyToOwnerConfirmDTO.setUpdatedUserName(user.getUserName());
        return ownerService.applyToOwnerConfirm(applyToOwnerConfirmDTO);
    }

    @RequestLock
    @PostMapping("/applyToReview")
    @ApiOperation(value = "充电桩确认信息提交审批", httpMethod = "POST")
    public BaseResponse applyToReview(@RequestBody ApplyToReviewVO applyToReviewVO, @SessionAttribute("user") UserSession user) {
        ApplyToReviewDTO applyToReviewDTO = new ApplyToReviewDTO();
        applyToReviewDTO.setIds(applyToReviewVO.getIds());
        applyToReviewDTO.setUpdatedUserId(user.getUserId());
        applyToReviewDTO.setUpdatedUserName(user.getUserName());
        return ownerService.applyToReview(applyToReviewDTO);
    }

    @ApiOperation(value = "受理一网通办充电桩承诺办件", httpMethod = "POST")
    public BaseResponse acceptConfirmApply(@RequestParam("applyNo") String applyNo) {
        return ownerService.acceptConfirmApply(applyNo);
    }


    @RequestLock
    @PostMapping("/applyCancel")
    @ApiOperation(value = "申请撤销充电条件确认信息-已审核通过", httpMethod = "POST")
    public BaseResponse applyCancel(@RequestBody ApplyCancelOwnerInfoVO applyCancelOwnerInfoVO) {
        return new BaseResponse();
    }

    @PostMapping("/searchOwnerOperateLog")
    @ApiOperation(value = "查询充电条件确认信息操作日志", httpMethod = "POST")
    public PageInfoBO<UserOperateLog> searchOwnerOperateLog(@RequestBody SearchOwnerOperateLogVO searchOwnerOperateLogVO, @SessionAttribute("user") UserSession user) {
        SearchOwnerOperateLogDTO searchOwnerOperateLogDTO = new SearchOwnerOperateLogDTO();
        searchOwnerOperateLogDTO.setId(searchOwnerOperateLogVO.getId());
        searchOwnerOperateLogDTO.setOrgId(user.getOrgId());
        return ownerService.searchOwnerOperateLog(searchOwnerOperateLogDTO);
    }

    @PostMapping("/searchSelfCheckingInfo")
    @ApiOperation(value = "查询自查反馈表列表", httpMethod = "POST")
    public PageInfoBO<SelfCheckingInfoDTO> searchSelfCheckingInfo(@RequestBody SearchSelfCheckingInfoVO searchSelfCheckingInfoVO, @SessionAttribute("user") UserSession user){
        SearchSelfCheckingInfoDTO searchSelfCheckingInfoDTO = ConvertUtil.convert(searchSelfCheckingInfoVO, SearchSelfCheckingInfoDTO.class);
        searchSelfCheckingInfoDTO.setOrgId(user.getOrgId());
        return ownerService.searchSelfCheckingInfo(searchSelfCheckingInfoDTO);
    }

    /**
     * 提交自查反馈表申请
     * @return
     */
    public BaseResponse submitSelfCheckingFileApply(){
        return null;
    }

    /**
     * @return
     */
    public BaseResponse approveSelfCheckingFile(){
        return null;
    }

    /**
     *
     * @return
     */
    public BaseResponse denySelfCheckingFile(){
        return null;
    }


    @PostMapping("/searchSecondHandVehicleAppliedOwnerList")
    @ApiOperation(value = "查询二手车申请中的充电桩确认列表", httpMethod = "POST")
    public PageInfoBO<SecondHandVehicleAppliedOwnerDTO> searchSecondHandVehicleAppliedOwnerList(@RequestBody SearchSecondHandVehicleAppliedOwnerVO searchSecondHandVehicleAppliedOwnerVO) {
        SearchSecondHandVehicleAppliedOwnerDTO searchSecondHandVehicleAppliedOwnerDTO = ConvertUtil.convert(searchSecondHandVehicleAppliedOwnerVO, SearchSecondHandVehicleAppliedOwnerDTO.class);
        searchSecondHandVehicleAppliedOwnerDTO.setOffset(CommonUtil.getOffset(searchSecondHandVehicleAppliedOwnerVO.getPageNum(), searchSecondHandVehicleAppliedOwnerVO.getPageSize()));
        searchSecondHandVehicleAppliedOwnerDTO.setLimit(searchSecondHandVehicleAppliedOwnerVO.getPageSize());
        if (searchSecondHandVehicleAppliedOwnerVO.getAuthKind() != null) {
            searchSecondHandVehicleAppliedOwnerDTO.setAuthKind(searchSecondHandVehicleAppliedOwnerVO.getAuthKind().toString());
        }
        return ownerService.searchSecondHandVehicleAppliedOwnerList(searchSecondHandVehicleAppliedOwnerDTO);
    }

    @Authorize(accessAuthorize = false)
    @PostMapping("/queryChargeConfirmCopy")
    @ApiOperation(value = "查询充电桩证明材料", httpMethod = "POST")
    public FileInfoBO queryChargeConfirmCopy(@RequestBody QueryChargePhotoVO queryChargePhotoVO) {
        QueryChargePhotoDTO queryChargePhotoDTO = new QueryChargePhotoDTO();
        queryChargePhotoDTO.setOwnerId(queryChargePhotoVO.getOwnerId());
        return ownerService.queryChargeConfirmCopy(queryChargePhotoDTO);
    }

    @Authorize(accessAuthorize = false)
    @PostMapping("/queryChargePhotoCopy")
    @ApiOperation(value = "查询充电桩图片", httpMethod = "POST")
    public FileInfoBO queryChargePhotoCopy(@RequestBody @Validated QueryChargePhotoVO queryChargePhotoVO) {
        QueryChargePhotoDTO queryChargePhotoDTO = new QueryChargePhotoDTO();
        queryChargePhotoDTO.setOwnerId(queryChargePhotoVO.getOwnerId());
        return ownerService.queryChargePhotoCopy(queryChargePhotoDTO);
    }


    @Authorize(accessAuthorize = false)
    @PostMapping("/queryBusinessBatchPurchaseReport")
    @ApiOperation(value = "查询批量购车报告", httpMethod = "POST")
    public FileInfoBO queryBusinessBatchPurchaseReport(@RequestBody @Validated QueryBusinessBatchPurchaseReportVO queryBusinessBatchPurchaseReportVO){
        return ownerService.queryBusinessBatchPurchaseReport(queryBusinessBatchPurchaseReportVO.getOwnerId());
    }

    @Authorize(accessAuthorize = false)
    @PostMapping("/syncChargePhoto")
    @Deprecated
    @ApiOperation(value = "同步充电桩照片", httpMethod = "POST")
    public BaseResponse syncChargePhoto(@RequestBody SyncChargePhotoVO syncChargePhotoVO){
        return ownerService.syncChargePhoto(syncChargePhotoVO.getId());
    }

    @Authorize(accessAuthorize = false)
    @PostMapping("/getTrafficPushLog")
    @ApiOperation(value = "交委推送日志", httpMethod = "POST")
    public BaseResponse getTrafficPushLog(@RequestBody @Validated SingleId<Long> id) {
        return ownerService.getTrafficPushLog(id.getId());
    }

    @Authorize(accessAuthorize = false)
    @PostMapping("/importSecondHandVehicleCheckInfo")
    @ApiOperation(value = "导入二手车核对信息", httpMethod = "POST")
    public ResponseEntity<byte[]> importSecondHandVehicleCheckInfo(@RequestParam(value = "file") MultipartFile multipartFile,@SessionAttribute("user") UserSession user) {
        ImportOwnerInfoDTO importOwnerInfoDTO = new ImportOwnerInfoDTO();
        importOwnerInfoDTO.setMultipartFile(multipartFile);
        importOwnerInfoDTO.setOrgId(user.getOrgId());
        importOwnerInfoDTO.setOrgName(user.getOrgName());
        importOwnerInfoDTO.setOperateUserId(user.getUserId());
        importOwnerInfoDTO.setOperateUserName(user.getUserName());
        byte[] bytes =  ownerService.importSecondHandVehicleCheckInfo(importOwnerInfoDTO);
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Disposition", "attachment;filename=" + System.currentTimeMillis() + ".xlsx");
        headers.add("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
        return new ResponseEntity<>(bytes, headers, HttpStatus.OK);
    }


    //--------------------------------------------------------------
    //  二手车解绑接口底层调用与新车逻辑相同 但是暴露不同接口，以兼容更多的权限访问
    //--------------------------------------------------------------
    @PostMapping("/querySecondHandVehicleOwnerUnbindDetail")
    @ApiOperation(value = "查询二手车解绑详情", httpMethod = "POST")
    public OwnerUnbindDetailResponse querySecondHandVehicleOwnerUnbindDetail(
            @RequestBody @Validated QuerySecondHandVehicleOwnerUnbindDetailVO querySecondHandVehicleOwnerUnbindDetailVO){
        return ownerService.queryOwnerUnbindDetail(querySecondHandVehicleOwnerUnbindDetailVO.getId());
    }

    @PostMapping("/approveSecondHandVehicleOwnerUnbind")
    @ApiOperation(value = "同意二手车充电条件解绑", httpMethod = "POST")
    public BaseResponse approveSecondHandVehicleOwnerUnbind(@SessionAttribute("user") UserSession user,
            @RequestBody @Validated ApproveSecondHandVehicleOwnerUnbindVO approveSecondHandVehicleOwnerUnbindVO){
        return ownerService.approveOwnerUnbind(approveSecondHandVehicleOwnerUnbindVO.getId()
                , user.getUserId(), user.getUserName());
    }

    @PostMapping("/denySecondHandVehicleOwnerUnbind")
    @ApiOperation(value = "拒绝二手车充电条件解绑", httpMethod = "POST")
    public BaseResponse denySecondHandVehicleOwnerUnbind(@SessionAttribute("user") UserSession user,
            @RequestBody @Validated DenySecondHandVehicleOwnerUnbindVO denySecondHandVehicleOwnerUnbindVO){
        return ownerService.denyOwnerUnbind(denySecondHandVehicleOwnerUnbindVO.getId()
                , denySecondHandVehicleOwnerUnbindVO.getReason(), user.getUserId(), user.getUserName());
    }

    //--------------------------------------------------------------
    //  新车解绑接口底层调用与二手车逻辑相同 但是暴露不同接口，以兼容更多的权限访问
    //--------------------------------------------------------------
    @PostMapping("/queryOwnerUnbindDetail")
    @ApiOperation(value = "查询新车解绑详情", httpMethod = "POST")
    public OwnerUnbindDetailResponse queryOwnerUnbindDetail(
            @RequestBody @Validated QuerySecondHandVehicleOwnerUnbindDetailVO querySecondHandVehicleOwnerUnbindDetailVO){
        return ownerService.queryOwnerUnbindDetail(querySecondHandVehicleOwnerUnbindDetailVO.getId());
    }

    @PostMapping("/approveOwnerUnbind")
    @ApiOperation(value = "同意新车充电条件解绑", httpMethod = "POST")
    public BaseResponse approveOwnerUnbind(@SessionAttribute("user") UserSession user
            ,@RequestBody @Validated ApproveSecondHandVehicleOwnerUnbindVO approveSecondHandVehicleOwnerUnbindVO){
        return ownerService.approveOwnerUnbind(approveSecondHandVehicleOwnerUnbindVO.getId()
                , user.getUserId(), user.getUserName());
    }

    @PostMapping("/denyOwnerUnbind")
    @ApiOperation(value = "拒绝新车充电条件解绑", httpMethod = "POST")
    public BaseResponse denyOwnerUnbind(@SessionAttribute("user") UserSession user
            ,@RequestBody @Validated DenySecondHandVehicleOwnerUnbindVO denySecondHandVehicleOwnerUnbindVO){
        return ownerService.denyOwnerUnbind(denySecondHandVehicleOwnerUnbindVO.getId()
                , denySecondHandVehicleOwnerUnbindVO.getReason(), user.getUserId(), user.getUserName());
    }


    //--------------------------------------------------------------
    //  根据用户查询系统数据，需要严格的访问权限限制
    //--------------------------------------------------------------

    @PostMapping("/queryOwnerSystemDetail")
    @ApiOperation(value = "查询用户系统数据", httpMethod = "POST")
    public QueryOwnerSystemDetailResponse queryOwnerSystemDetail(@RequestBody QueryOwnerSystemDetailVO queryOwnerSystemDetailVO, @SessionAttribute("user") UserSession user){
        //校验登录人的机构信息
        if(!OrgKindEnum.MANAGER.getValue().equals(user.getOrgKind())){
            throw new ServiceException("您无权进行该查询操作");
        }
        //转换参数
        QueryOwnerSystemDetailDTO queryOwnerSystemDetailDTO = new QueryOwnerSystemDetailDTO();
        queryOwnerSystemDetailDTO.setAuthId(queryOwnerSystemDetailVO.getAuthId());
        queryOwnerSystemDetailDTO.setAuthType(queryOwnerSystemDetailVO.getAuthType());
        queryOwnerSystemDetailDTO.setName(queryOwnerSystemDetailVO.getName());
        return ownerService.queryOwnerSystemDetail(queryOwnerSystemDetailDTO);
    }
}
