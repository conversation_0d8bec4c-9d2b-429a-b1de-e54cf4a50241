package com.extracme.nevmp.controller;

import com.extracme.nevmp.api.BaseResponse;
import com.extracme.nevmp.auth.Authorize;
import com.extracme.nevmp.auth.UserSession;
import com.extracme.nevmp.converter.ConvertUtil;
import com.extracme.nevmp.dto.common.PageInfoBO;
import com.extracme.nevmp.dto.qualification.owner.ExpireOwnerQualificationSpecialDTO;
import com.extracme.nevmp.dto.qualification.owner.SaveOwnerQualificationSpecialInfoDTO;
import com.extracme.nevmp.dto.qualification.owner.SearchOwnerQualificationSpecialDTO;
import com.extracme.nevmp.model.OwnerQualificationSpecial;
import com.extracme.nevmp.service.qualification.OwnerQualificationSpecialService;
import com.extracme.nevmp.vo.qualification.ExpireOwnerQualificationSpecialVO;
import com.extracme.nevmp.vo.qualification.SaveOwnerQualificationSpecialInfoVO;
import com.extracme.nevmp.vo.qualification.SearchOwnerQualificationSpecialVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Authorize
@RestController
@RequestMapping("/ownerQualificationSpecial")
@Validated
@Api(value = "特殊人才购车资质管理")
public class OwnerQualificationSpecialController {

    @Autowired
    private OwnerQualificationSpecialService ownerQualificationSpecialService;

    @PostMapping("/searchOwnerQualificationSpecial")
    @ApiOperation(value = "查询特殊人才信息列表", httpMethod = "POST")
    public PageInfoBO<OwnerQualificationSpecial> searchOwnerQualificationSpecial(@Validated @RequestBody SearchOwnerQualificationSpecialVO searchOwnerQualificationSpecialVO){
        SearchOwnerQualificationSpecialDTO searchOwnerQualificationSpecialDTO = ConvertUtil.convert(searchOwnerQualificationSpecialVO, SearchOwnerQualificationSpecialDTO.class);
        return ownerQualificationSpecialService.searchOwnerQualificationSpecial(searchOwnerQualificationSpecialDTO);
    }

    @PostMapping("/saveOwnerQualificationSpecialInfo")
    @ApiOperation(value = "保存特殊人才信息", httpMethod = "POST")
    public BaseResponse saveOwnerQualificationSpecialInfo(@Validated @RequestBody SaveOwnerQualificationSpecialInfoVO saveOwnerQualificationSpecialInfoVO, @SessionAttribute("user") UserSession user){
        SaveOwnerQualificationSpecialInfoDTO saveOwnerQualificationSpecialInfoDTO = ConvertUtil.convert(saveOwnerQualificationSpecialInfoVO, SaveOwnerQualificationSpecialInfoDTO.class);
        saveOwnerQualificationSpecialInfoDTO.setOperatorId(user.getUserId());
        saveOwnerQualificationSpecialInfoDTO.setOperatorName(user.getUserName());
        return  ownerQualificationSpecialService.saveOwnerQualificationSpecialInfo(saveOwnerQualificationSpecialInfoDTO);
    }

    @PostMapping("/expireOwnerQualificationSpecial")
    @ApiOperation(value = "失效特殊人才信息", httpMethod = "POST")
    public BaseResponse expireOwnerQualificationSpecial(@Validated @RequestBody ExpireOwnerQualificationSpecialVO expireOwnerQualificationSpecialVO,  @SessionAttribute("user") UserSession user){
        ExpireOwnerQualificationSpecialDTO expireOwnerQualificationSpecialDTO = ConvertUtil.convert(expireOwnerQualificationSpecialVO, ExpireOwnerQualificationSpecialDTO.class);
        expireOwnerQualificationSpecialDTO.setOperatorId(user.getUserId());
        expireOwnerQualificationSpecialDTO.setOperatorName(user.getUserName());
        return ownerQualificationSpecialService.expireOwnerQualificationSpecial(expireOwnerQualificationSpecialDTO);
    }
}
