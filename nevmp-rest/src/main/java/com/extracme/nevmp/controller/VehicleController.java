package com.extracme.nevmp.controller;

import com.extracme.nevmp.api.BaseResponse;
import com.extracme.nevmp.auth.Authorize;
import com.extracme.nevmp.auth.RequestLock;
import com.extracme.nevmp.auth.UserSession;
import com.extracme.nevmp.converter.ConvertUtil;
import com.extracme.nevmp.dto.ExportVehicleInfoDTO;
import com.extracme.nevmp.dto.common.FileInfoBO;
import com.extracme.nevmp.dto.common.PageInfoBO;
import com.extracme.nevmp.dto.vehicle.*;
import com.extracme.nevmp.model.UploadFileNotes;
import com.extracme.nevmp.model.UserOperateLog;
import com.extracme.nevmp.service.vehicle.VehicleService;
import com.extracme.nevmp.vo.vehicle.*;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> @Description 车辆 服务
 */
@Authorize
@RestController
@RequestMapping("vehicle")
@Slf4j
@Validated
public class VehicleController {

    @Autowired
    private VehicleService vehicleService;

    /**
     * 导入车辆信息
     */
    @RequestLock
    @PostMapping("/importVehicleInfo")
    public BaseResponse importVehicleInfo(@RequestParam(value = "file") MultipartFile multipartFile, @SessionAttribute("user") UserSession user){
        ImportVehicleInfoDTO importVehicleInfoDTO = new ImportVehicleInfoDTO();
        importVehicleInfoDTO.setMultipartFile(multipartFile);
        importVehicleInfoDTO.setOrgId(user.getOrgId());
        importVehicleInfoDTO.setOrgName(user.getOrgName());
        importVehicleInfoDTO.setOperateUserId(user.getUserId());
        importVehicleInfoDTO.setOperateUserName(user.getUserName());
        return vehicleService.importVehicleInfo(importVehicleInfoDTO);
    }

    /**
     * 查询车辆信息导入日志
     * @return
     */
    @PostMapping("/searchVehicleUploadFileNotes")
    public PageInfoBO<UploadFileNotes> searchVehicleUploadFileNotes(@RequestBody @Valid SearchVehicleUploadFileNotesVO searchVehicleUploadFileNotesVO, @SessionAttribute("user") UserSession user){
        SearchVehicleUploadFileNotesDTO searchVehicleUploadFileNotesDTO = ConvertUtil.convert(searchVehicleUploadFileNotesVO, SearchVehicleUploadFileNotesDTO.class);
        //根据机构进行查询，统一机构内，日志数据共享
        searchVehicleUploadFileNotesDTO.setOrgId(user.getOrgId());
        return vehicleService.searchVehicleUploadFileNotes(searchVehicleUploadFileNotesDTO);
    }

    /**
     * 查看车辆合格证导入日志
     * @param searchVehicleUploadFileNotesVO
     * @param user
     * @return
     */
    @PostMapping("/searchCertNoUploadFileNotes")
    public PageInfoBO<UploadFileNotes> searchCertNoUploadFileNotes(@RequestBody @Valid SearchCertNoUploadFileNotesVO searchVehicleUploadFileNotesVO, @SessionAttribute("user") UserSession user){
        SearchCertNoUploadFileNotesDTO searchCertNoUploadFileNotesDTO = ConvertUtil.convert(searchVehicleUploadFileNotesVO, SearchCertNoUploadFileNotesDTO.class);
        //根据机构进行查询，统一机构内，日志数据共享
        searchCertNoUploadFileNotesDTO.setOrgId(user.getOrgId());
        return vehicleService.searchCertNoUploadFileNotes(searchCertNoUploadFileNotesDTO);
    }

    @RequestLock
    @PostMapping("/exportVehicleInfo")
    @ApiOperation(value = "导出车辆信息", httpMethod = "POST")
    public ResponseEntity<byte[]> exportVehicleInfo(@RequestBody @Valid SearchVehicleInfoVO searchVehicleInfoVO, @SessionAttribute("user") UserSession user){
        ExportVehicleInfoDTO exportVehicleInfoDTO = ConvertUtil.convert(searchVehicleInfoVO, ExportVehicleInfoDTO.class);

        exportVehicleInfoDTO.setOperatorId(user.getUserId());
        exportVehicleInfoDTO.setOperatorName(user.getUserName());
        exportVehicleInfoDTO.setOrgId(user.getOrgId());
        byte[] bytes = vehicleService.exportVehicleInfo(exportVehicleInfoDTO);

        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Disposition", "attachment;filename=" + System.currentTimeMillis() + ".xlsx");
        headers.add("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
        return new ResponseEntity<byte[]>(bytes, headers, HttpStatus.OK);
    }

    /**
     * 上传合格证扫描件（需要兼容历史格式）
     */
    @RequestLock
    @PostMapping("/importCertNoScanningCopy")
    public BaseResponse importCertNoScanningCopy(@RequestParam("file") MultipartFile file, @SessionAttribute("user") UserSession user){
        ImportCertNoScanningCopyDTO importCertNoScanningCopyDTO = new ImportCertNoScanningCopyDTO();
        importCertNoScanningCopyDTO.setMultipartFile(file);
        importCertNoScanningCopyDTO.setOrgId(user.getOrgId());
        importCertNoScanningCopyDTO.setUpdatedUserId(user.getUserId());
        importCertNoScanningCopyDTO.setUpdatedUserName(user.getUserName());
        return vehicleService.importCertNoScanningCopy(importCertNoScanningCopyDTO);
    }

    /**
     * 查询合格证扫描件照片
     * @param queryCertNoScanningCopyVO
     * @param user
     * @return
     */
    @Authorize(accessAuthorize = false)
    @PostMapping("/queryCertNoScanningCopy")
    public FileInfoBO queryCertNoScanningCopy(@RequestBody @Valid QueryCertNoScanningCopyVO queryCertNoScanningCopyVO, @SessionAttribute("user") UserSession user){
        QueryCertNoScanningCopyDTO queryCertNoScanningCopyDTO = new QueryCertNoScanningCopyDTO();
        queryCertNoScanningCopyDTO.setVehicleId(queryCertNoScanningCopyVO.getVehicleId());
        queryCertNoScanningCopyDTO.setOrgId(user.getOrgId());
        return vehicleService.queryCertNoScanningCopy(queryCertNoScanningCopyDTO);
    }

    /**
     * 生成登记号
     */
    @RequestLock
    @PostMapping("/generateRegNo")
    public BaseResponse generateRegNo(@RequestBody @Valid GenerateRegNoVO generateRegNoVO, @SessionAttribute("user") UserSession user){
        GenerateRegNoDTO generateRegNoDTO  = ConvertUtil.convert(generateRegNoVO, GenerateRegNoDTO.class);
        generateRegNoDTO.setOrgId(user.getOrgId());
        generateRegNoDTO.setUpdatedUser(user.getUserId());
        generateRegNoDTO.setUpdatedUserName(user.getUserName());
        return vehicleService.generateRegNo(generateRegNoDTO);
    }

    /**
     * 删除车辆数据
     */
    @RequestLock
    @PostMapping("/delete")
    public BaseResponse delete(@RequestBody @Valid DeleteVehicleInfoVO deleteVehicleInfoVO, @SessionAttribute("user") UserSession user){
        DeleteVehicleInfoDTO deleteVehicleInfoDTO = new DeleteVehicleInfoDTO();
        deleteVehicleInfoDTO.setId(deleteVehicleInfoVO.getId());
        deleteVehicleInfoDTO.setOrgId(user.getOrgId());
        deleteVehicleInfoDTO.setUpdatedUser(user.getUserId());
        deleteVehicleInfoDTO.setUpdatedUserName(user.getUserName());
        return vehicleService.delete(deleteVehicleInfoDTO);
    }

    /**
     * 保存车辆数据
     * @return
     */
    @RequestLock
    @PostMapping("/save")
    public BaseResponse save(@RequestBody @Valid SaveVehicleInfoVO saveVehicleInfoVO, @SessionAttribute("user") UserSession user){
        SaveVehicleInfoDTO saveVehicleInfoDTO = ConvertUtil.normalConvert(saveVehicleInfoVO, SaveVehicleInfoDTO.class);
        saveVehicleInfoDTO.setOrgId(user.getOrgId());
        saveVehicleInfoDTO.setOrgName(user.getOrgName());
        saveVehicleInfoDTO.setCreatedUser(user.getUserId());
        saveVehicleInfoDTO.setCreatedUserName(user.getUserName());
        return vehicleService.save(saveVehicleInfoDTO);
    }

    /**
     * 获取车辆详情
     * @param getVehicleInfoDetailVO
     * @param user
     * @return
     */
    @Authorize(accessAuthorize = false)
    @PostMapping("/getVehicleInfoDetail")
    public VehicleInfoDetailDTO getVehicleInfoDetail(@RequestBody @Valid GetVehicleInfoDetailVO getVehicleInfoDetailVO, @SessionAttribute("user") UserSession user){
        GetVehicleInfoDetailDTO getVehicleInfoDetailDTO = new GetVehicleInfoDetailDTO();
        getVehicleInfoDetailDTO.setId(getVehicleInfoDetailVO.getId());
        getVehicleInfoDetailDTO.setOrgId(user.getOrgId());
        return vehicleService.getVehicleInfoDetail(getVehicleInfoDetailDTO);
    }

    /**
     * 更新车辆数据
     */
    @RequestLock
    @PostMapping("/update")
    public BaseResponse update(@RequestBody @Valid UpdateVehicleInfoVO updateVehicleInfoVO, @SessionAttribute("user") UserSession user){
        UpdateVehicleInfoDTO updateVehicleInfoDTO = ConvertUtil.normalConvert(updateVehicleInfoVO,UpdateVehicleInfoDTO.class);
        updateVehicleInfoDTO.setOrgId(user.getOrgId());
        updateVehicleInfoDTO.setUpdatedUser(user.getUserId());
        updateVehicleInfoDTO.setUpdatedUserName(user.getUserName());
        return vehicleService.update(updateVehicleInfoDTO);
    }

    @PostMapping("/searchVehicleInfo")
    @ApiOperation(value = "查询车辆列表", httpMethod = "POST")
    public PageInfoBO<VehicleInfoDTO> searchVehicleInfo(@RequestBody @Valid SearchVehicleInfoVO searchVehicleInfoVO,@SessionAttribute("user") UserSession user){
        SearchVehicleInfoDTO searchVehicleInfoDTO = ConvertUtil.convert(searchVehicleInfoVO, SearchVehicleInfoDTO.class);
        searchVehicleInfoDTO.setOrgId(user.getOrgId());
        //时间转化
        if(searchVehicleInfoVO.getApplyTimeEnd() != null){
            searchVehicleInfoDTO.setApplyTimeEnd(new Date(searchVehicleInfoVO.getApplyTimeEnd().getTime() + TimeUnit.DAYS.toMillis(1)));
        }
        if(searchVehicleInfoVO.getApproveTimeEnd() != null){
            searchVehicleInfoDTO.setApproveTimeEnd(new Date(searchVehicleInfoVO.getApproveTimeEnd().getTime() + TimeUnit.DAYS.toMillis(1)));
        }
        return vehicleService.searchVehicleInfo(searchVehicleInfoDTO);
    }


    @PostMapping("/searchAppliedVehicleInfo")
    @ApiOperation(value = "查询已申请的车辆列表", httpMethod = "POST")
    public PageInfoBO<VehicleInfoDTO> searchAppliedVehicleInfo(@RequestBody @Valid SearchAppliedVehicleInfoVO searchAppliedVehicleInfoVO){
        SearchVehicleInfoDTO searchVehicleInfoDTO = ConvertUtil.convert(searchAppliedVehicleInfoVO, SearchVehicleInfoDTO.class);
        //时间转化
        if(searchAppliedVehicleInfoVO.getApplyTimeEnd() != null){
            searchVehicleInfoDTO.setApplyTimeEnd(new Date(searchAppliedVehicleInfoVO.getApplyTimeEnd().getTime() + TimeUnit.DAYS.toMillis(1)));
        }
        if(searchAppliedVehicleInfoVO.getApproveTimeEnd() != null){
            searchVehicleInfoDTO.setApproveTimeEnd(new Date(searchAppliedVehicleInfoVO.getApproveTimeEnd().getTime() + TimeUnit.DAYS.toMillis(1)));
        }
        return vehicleService.searchAppliedVehicleInfo(searchVehicleInfoDTO);
    }

    /**
     * 通过车辆信息
     */
    @RequestLock
    @PostMapping("/approve")
    public BaseResponse approve(@RequestBody @Valid ApproveVehicleInfoVO approveVehicleInfoVO,  @SessionAttribute("user") UserSession user){
        ApproveVehicleInfoDTO approveVehicleInfoDTO = ConvertUtil.convert(approveVehicleInfoVO, ApproveVehicleInfoDTO.class);
        approveVehicleInfoDTO.setUpdatedUser(user.getUserId());
        approveVehicleInfoDTO.setUpdatedUserName(user.getUserName());
        return vehicleService.approve(approveVehicleInfoDTO);
    }

    /**
     * 拒绝车辆信息
     */
    @RequestLock
    @PostMapping("/deny")
    public BaseResponse deny(@RequestBody @Valid DenyVehicleInfoVO denyVehicleInfoVO, @SessionAttribute("user") UserSession user){
        DenyVehicleInfoDTO denyVehicleInfoDTO = new DenyVehicleInfoDTO();
        denyVehicleInfoDTO.setIds(denyVehicleInfoVO.getIds());
        denyVehicleInfoDTO.setReason(denyVehicleInfoVO.getReason());
        denyVehicleInfoDTO.setUpdatedUser(user.getUserId());
        denyVehicleInfoDTO.setUpdatedUserName(user.getUserName());
        return vehicleService.deny(denyVehicleInfoDTO);
    }

    /**
     * 申请车辆信息
     */
    @RequestLock
    @PostMapping("/apply")
    public BaseResponse apply(@RequestBody @Valid ApplyVehicleInfoVO applyVehicleInfoVO, @SessionAttribute("user") UserSession user){
        ApplyVehicleInfoDTO applyVehicleInfoDTO = new ApplyVehicleInfoDTO();
        applyVehicleInfoDTO.setIds(applyVehicleInfoVO.getIds());
        applyVehicleInfoDTO.setOrgId(user.getOrgId());
        applyVehicleInfoDTO.setUpdatedUser(user.getUserId());
        applyVehicleInfoDTO.setUpdatedUserName(user.getUserName());
        return vehicleService.apply(applyVehicleInfoDTO);
    }

    /**
     * 撤销
     * @return
     */
    @RequestLock
    @PostMapping("/cancel")
    public BaseResponse cancel(@RequestBody @Valid CancelVehicleInfoVO cancelVehicleInfoVO, @SessionAttribute("user") UserSession user){
        CancelVehicleInfoDTO cancelVehicleInfoDTO = new CancelVehicleInfoDTO();
        cancelVehicleInfoDTO.setId(cancelVehicleInfoVO.getId());
        cancelVehicleInfoDTO.setOrgId(user.getOrgId());
        cancelVehicleInfoDTO.setUpdatedUser(user.getUserId());
        cancelVehicleInfoDTO.setUpdatedUserName(user.getUserName());
        return vehicleService.cancel(cancelVehicleInfoDTO);
    }


    /**
     * 撤销车辆审核（在还没有进行绑定时，可以对操作进行撤销）
     * @return
     */
    @RequestLock
    @PostMapping("/cancelReview")
    public BaseResponse cancelReview(@RequestBody CancelReviewVO cancelReviewVO, @SessionAttribute("user") UserSession user){
        CancelReviewDTO cancelReviewDTO = new CancelReviewDTO();
        cancelReviewDTO.setId(cancelReviewVO.getId());
        cancelReviewDTO.setOrgId(user.getOrgId());
        cancelReviewDTO.setUpdatedUserId(user.getUserId());
        cancelReviewDTO.setUpdatedUserName(user.getUserName());
        return vehicleService.cancelReview(cancelReviewDTO);
    }

    /**
     * 查询车辆操作日志
     * @param searchVehicleOperateLogVO
     * @param user
     * @return
     */
    @PostMapping("/searchVehicleOperateLog")
    public PageInfoBO<UserOperateLog> searchVehicleOperateLog(@RequestBody @Valid SearchVehicleOperateLogVO searchVehicleOperateLogVO, @SessionAttribute("user") UserSession user){
        SearchVehicleOperateLogDTO searchVehicleOperateLogDTO = new SearchVehicleOperateLogDTO();
        searchVehicleOperateLogDTO.setId(searchVehicleOperateLogVO.getId());
        searchVehicleOperateLogDTO.setOrgId(user.getOrgId());
        return vehicleService.searchVehicleOperateLog(searchVehicleOperateLogDTO);
    }
}
