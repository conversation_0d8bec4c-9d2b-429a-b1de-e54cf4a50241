package com.extracme.nevmp.controller;

import com.extracme.nevmp.api.BaseResponse;
import com.extracme.nevmp.api.ResultCode;
import com.extracme.nevmp.auth.Authorize;
import com.extracme.nevmp.auth.Sessions;
import com.extracme.nevmp.auth.UserSession;
import com.extracme.nevmp.converter.ConvertUtil;
import com.extracme.nevmp.dto.CreateAccountDTO;
import com.extracme.nevmp.dto.account.ChangePasswordDTO;
import com.extracme.nevmp.dto.account.PowerResponse;
import com.extracme.nevmp.model.Syresource;
import com.extracme.nevmp.model.Syuser;
import com.extracme.nevmp.service.account.AccountService;
import com.extracme.nevmp.vo.CreateAccountVO;
import com.extracme.nevmp.vo.account.ChangePasswordVO;
import com.extracme.nevmp.vo.account.LoginVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 */
@RestController
@RequestMapping("account")
public class AccountController {

    @Autowired
    private AccountService accountService;

    @PostMapping("/login")
    public BaseResponse login(@RequestBody LoginVO loginVO, HttpServletRequest request, HttpServletResponse response){
        Syuser user = accountService.getAccount(loginVO.getLoginName(), loginVO.getPassword());
        if(user != null){
            List<Syresource> resources =  accountService.getAccountResources(user.getId());
            Sessions.loginUser(user, resources, response);
            return new BaseResponse();
        }else{
            return new BaseResponse("用户名或密码错误", ResultCode.FAILURE);
        }
    }

    @PostMapping("/logout")
    public BaseResponse logout(HttpServletRequest request, HttpServletResponse response){
        Sessions.logout(request, response);
        return new BaseResponse();
    }

    @PostMapping("/changePassword")
    public BaseResponse changePassword(@RequestBody ChangePasswordVO changePasswordVO, HttpServletRequest request, HttpServletResponse response){
        ChangePasswordDTO changePasswordDTO = ConvertUtil.convert(changePasswordVO,ChangePasswordDTO.class);
        accountService.changePassword(changePasswordDTO);
        Sessions.logout(request, response);
        return new BaseResponse();
    }

    @GetMapping("/getPower")
    @Authorize(accessAuthorize = false)
    public PowerResponse getPower(@SessionAttribute("user") UserSession user){
        PowerResponse response =  accountService.getPower(user.getUserId());
        response.setOrgName(user.getOrgName());
        response.setUserName(user.getUserName());
        return response;
    }


    @PostMapping("/createAccount")
    public BaseResponse createAccount(@RequestBody CreateAccountVO createAccountVO, @SessionAttribute("user") UserSession userSession) {
        CreateAccountDTO createAccountDTO = new CreateAccountDTO();
        BeanUtils.copyProperties(createAccountVO, createAccountDTO);
        return accountService.createAccount(createAccountDTO, userSession.getUserId());
    }
}
