package com.extracme.nevmp.controller;

import java.util.Date;
import java.util.concurrent.TimeUnit;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.SessionAttribute;
import org.springframework.web.multipart.MultipartFile;

import com.extracme.nevmp.api.BaseResponse;
import com.extracme.nevmp.auth.Authorize;
import com.extracme.nevmp.auth.RequestLock;
import com.extracme.nevmp.auth.UserSession;
import com.extracme.nevmp.converter.ConvertUtil;
import com.extracme.nevmp.dto.common.FileInfoBO;
import com.extracme.nevmp.dto.common.PageInfoBO;
import com.extracme.nevmp.dto.owner.ImportOwnerInfoDTO;
import com.extracme.nevmp.dto.owner.ImportSubsidyRemarkDTO;
import com.extracme.nevmp.dto.owner.subsidy.ApproveOwnerSubsidyInfoDTO;
import com.extracme.nevmp.dto.owner.subsidy.CancelOwnerSubsidyInfoDTO;
import com.extracme.nevmp.dto.owner.subsidy.DenyOwnerSubsidyInfoDTO;
import com.extracme.nevmp.dto.owner.subsidy.ExportSubsidyDetailDTO;
import com.extracme.nevmp.dto.owner.subsidy.GetOwnerSubsidyInfoDetailDTO;
import com.extracme.nevmp.dto.owner.subsidy.ImportFileCopyDTO;
import com.extracme.nevmp.dto.owner.subsidy.OwnerSubsidyInfoDTO;
import com.extracme.nevmp.dto.owner.subsidy.OwnerSubsidyInfoDetailDTO;
import com.extracme.nevmp.dto.owner.subsidy.SearchAppliedOwnerSubsidyInfoDTO;
import com.extracme.nevmp.dto.owner.subsidy.SearchOwnerSubsidyInfoDTO;
import com.extracme.nevmp.dto.owner.subsidy.SearchSubsidyUploadDataNotesDTO;
import com.extracme.nevmp.dto.owner.subsidy.SearchSubsidyUploadFileNotesDTO;
import com.extracme.nevmp.dto.owner.subsidy.SubmitOwnerSubsidyInfoDTO;
import com.extracme.nevmp.dto.owner.subsidy.UpdateOwnerSubsidyInfoDTO;
import com.extracme.nevmp.enums.FileTypeEnum;
import com.extracme.nevmp.error.ServiceException;
import com.extracme.nevmp.model.UploadFileNotes;
import com.extracme.nevmp.service.owner.OwnerSubsidyService;
import com.extracme.nevmp.vo.owner.subsidy.ApproveOwnerSubsidyInfoVO;
import com.extracme.nevmp.vo.owner.subsidy.CancelOwnerSubsidyInfoVO;
import com.extracme.nevmp.vo.owner.subsidy.DenyOwnerSubsidyInfoVO;
import com.extracme.nevmp.vo.owner.subsidy.GetOwnerSubsidyInfoDetailVO;
import com.extracme.nevmp.vo.owner.subsidy.QueryAuthCopyVO;
import com.extracme.nevmp.vo.owner.subsidy.QueryInvoiceCopyVO;
import com.extracme.nevmp.vo.owner.subsidy.QueryOrgCodeCopyVO;
import com.extracme.nevmp.vo.owner.subsidy.QueryVehicleLicenseCopyVO;
import com.extracme.nevmp.vo.owner.subsidy.SearchAppliedOwnerSubsidyInfoVO;
import com.extracme.nevmp.vo.owner.subsidy.SearchOwnerSubsidyInfoVO;
import com.extracme.nevmp.vo.owner.subsidy.SearchSubsidyUploadDataNotesVO;
import com.extracme.nevmp.vo.owner.subsidy.SearchSubsidyUploadFileNotesVO;
import com.extracme.nevmp.vo.owner.subsidy.SubmitOwnerSubsidyInfoVO;
import com.extracme.nevmp.vo.owner.subsidy.UpdateOwnerSubsidyInfoVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @Description
 */
@Authorize(accessAuthorize = false)
@RestController
@RequestMapping("/ownerSubsidy")
@Validated
@Api(value = "用户补贴管理")
public class OwnerSubsidyController {

    @Autowired
    private OwnerSubsidyService ownerSubsidyService;


    @PostMapping("/searchOwnerSubsidyInfo")
    @ApiOperation(value = "查询补贴列表", httpMethod = "POST")
    public PageInfoBO<OwnerSubsidyInfoDTO> searchOwnerSubsidyInfo(@RequestBody SearchOwnerSubsidyInfoVO searchOwnerSubsidyInfoVO, @SessionAttribute("user") UserSession user){
        SearchOwnerSubsidyInfoDTO searchOwnerSubsidyInfoDTO = ConvertUtil.convert(searchOwnerSubsidyInfoVO, SearchOwnerSubsidyInfoDTO.class);
        //设置登录机构
        searchOwnerSubsidyInfoDTO.setOrgId(user.getOrgId());
        //时间转化
        if(searchOwnerSubsidyInfoDTO.getSubsidyApplyDateEnd() != null){
            searchOwnerSubsidyInfoDTO.setSubsidyApplyDateEnd(new Date(searchOwnerSubsidyInfoVO.getSubsidyApplyDateEnd().getTime() + TimeUnit.DAYS.toMillis(1)));
        }
        if(searchOwnerSubsidyInfoDTO.getInvoiceDateEnd() != null){
            searchOwnerSubsidyInfoDTO.setInvoiceDateEnd(new Date(searchOwnerSubsidyInfoVO.getInvoiceDateEnd().getTime() + TimeUnit.DAYS.toMillis(1)));
        }
        if(searchOwnerSubsidyInfoDTO.getVehicleLicenseDateEnd() != null){
            searchOwnerSubsidyInfoDTO.setVehicleLicenseDateEnd(new Date(searchOwnerSubsidyInfoVO.getVehicleLicenseDateEnd().getTime() + TimeUnit.DAYS.toMillis(1)));
        }
        return ownerSubsidyService.searchOwnerSubsidyInfo(searchOwnerSubsidyInfoDTO);
    }

    @PostMapping("/searchAppliedOwnerSubsidyInfo")
    @ApiOperation(value = "查询补贴列表-经信委", httpMethod = "POST")
    public PageInfoBO<OwnerSubsidyInfoDTO> searchAppliedOwnerSubsidyInfo(@RequestBody SearchAppliedOwnerSubsidyInfoVO searchAppliedOwnerSubsidyInfoVO, @SessionAttribute("user") UserSession user){
        SearchAppliedOwnerSubsidyInfoDTO searchAppliedOwnerSubsidyInfoDTO = ConvertUtil.convert(searchAppliedOwnerSubsidyInfoVO, SearchAppliedOwnerSubsidyInfoDTO.class);
       //时间转化
        if(searchAppliedOwnerSubsidyInfoDTO.getSubsidyApplyDateEnd() != null){
            searchAppliedOwnerSubsidyInfoDTO.setSubsidyApplyDateEnd(new Date(searchAppliedOwnerSubsidyInfoDTO.getSubsidyApplyDateEnd().getTime() + TimeUnit.DAYS.toMillis(1)));
        }
        if(searchAppliedOwnerSubsidyInfoDTO.getInvoiceDateEnd() != null){
            searchAppliedOwnerSubsidyInfoDTO.setInvoiceDateEnd(new Date(searchAppliedOwnerSubsidyInfoDTO.getInvoiceDateEnd().getTime() + TimeUnit.DAYS.toMillis(1)));
        }
        if(searchAppliedOwnerSubsidyInfoDTO.getVehicleLicenseDateEnd() != null){
            searchAppliedOwnerSubsidyInfoDTO.setVehicleLicenseDateEnd(new Date(searchAppliedOwnerSubsidyInfoDTO.getVehicleLicenseDateEnd().getTime() + TimeUnit.DAYS.toMillis(1)));
        }
        return ownerSubsidyService.searchAppliedOwnerSubsidyInfo(searchAppliedOwnerSubsidyInfoDTO);
    }

    @RequestLock
    @PostMapping("/exportSubsidyDetail")
    @ApiOperation(value = "导出补贴清算", httpMethod = "POST")
    public ResponseEntity<byte[]> exportSubsidyDetail(@RequestBody SearchAppliedOwnerSubsidyInfoVO searchAppliedOwnerSubsidyInfoVO){
        ExportSubsidyDetailDTO exportSubsidyDetailDTO = ConvertUtil.convert(searchAppliedOwnerSubsidyInfoVO, ExportSubsidyDetailDTO.class);
        //时间转化
        if(exportSubsidyDetailDTO.getSubsidyApplyDateEnd() != null){
            exportSubsidyDetailDTO.setSubsidyApplyDateEnd(new Date(exportSubsidyDetailDTO.getSubsidyApplyDateEnd().getTime() + TimeUnit.DAYS.toMillis(1)));
        }
        if(exportSubsidyDetailDTO.getInvoiceDateEnd() != null){
            exportSubsidyDetailDTO.setInvoiceDateEnd(new Date(exportSubsidyDetailDTO.getInvoiceDateEnd().getTime() + TimeUnit.DAYS.toMillis(1)));
        }
        if(exportSubsidyDetailDTO.getVehicleLicenseDateEnd() != null){
            exportSubsidyDetailDTO.setVehicleLicenseDateEnd(new Date(exportSubsidyDetailDTO.getVehicleLicenseDateEnd().getTime() + TimeUnit.DAYS.toMillis(1)));
        }
        byte[] bytes = ownerSubsidyService.exportSubsidyDetail(exportSubsidyDetailDTO);
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Disposition", "attachment;filename=" + System.currentTimeMillis() + ".xlsx");
        headers.add("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
        return new ResponseEntity<>(bytes, headers, HttpStatus.OK);
    }

    @RequestLock
    @PostMapping("/exportsSubsidyInfo")
    @ApiOperation(value = "导出补贴明细", httpMethod = "POST")
    public BaseResponse exportsSubsidyInfo(){
        return new BaseResponse();
    }


    @PostMapping("/getOwnerSubsidyInfoDetail")
    @ApiOperation(value = "获取补贴信息", httpMethod = "POST")
    public OwnerSubsidyInfoDetailDTO getOwnerSubsidyInfoDetail(@RequestBody GetOwnerSubsidyInfoDetailVO getOwnerSubsidyInfoDetailVO, @SessionAttribute("user") UserSession user){
        GetOwnerSubsidyInfoDetailDTO getOwnerSubsidyInfoDetailDTO = new GetOwnerSubsidyInfoDetailDTO();
        getOwnerSubsidyInfoDetailDTO.setId(getOwnerSubsidyInfoDetailVO.getId());
        getOwnerSubsidyInfoDetailDTO.setOrgId(user.getOrgId());
        return ownerSubsidyService.getOwnerSubsidyInfoDetail(getOwnerSubsidyInfoDetailDTO);
    }

    @RequestLock
    @PostMapping("/updateOwnerSubsidyInfo")
    @ApiOperation(value = "更新补贴信息", httpMethod = "POST")
    public BaseResponse updateOwnerSubsidyInfo(@RequestBody UpdateOwnerSubsidyInfoVO updateOwnerSubsidyInfoVO, @SessionAttribute("user") UserSession user){
        UpdateOwnerSubsidyInfoDTO updateOwnerSubsidyInfoDTO = ConvertUtil.convert(updateOwnerSubsidyInfoVO, UpdateOwnerSubsidyInfoDTO.class);
        updateOwnerSubsidyInfoDTO.setUpdatedUserId(user.getUserId());
        updateOwnerSubsidyInfoDTO.setUpdatedUserName(user.getUserName());
        updateOwnerSubsidyInfoDTO.setOrgId(user.getOrgId());
        return ownerSubsidyService.updateOwnerSubsidyInfo(updateOwnerSubsidyInfoDTO);
    }

    @RequestLock
    @PostMapping("/importOwnerSubsidyInfo")
    @ApiOperation(value = "批量导入补贴信息", httpMethod = "POST")
    public BaseResponse importOwnerSubsidyInfo(@RequestParam(value = "file") MultipartFile multipartFile, @SessionAttribute("user") UserSession user){
        ImportOwnerInfoDTO importOwnerInfoDTO = new ImportOwnerInfoDTO();
        importOwnerInfoDTO.setMultipartFile(multipartFile);
        importOwnerInfoDTO.setOrgId(user.getOrgId());
        importOwnerInfoDTO.setOrgName(user.getOrgName());
        importOwnerInfoDTO.setOperateUserId(user.getUserId());
        importOwnerInfoDTO.setOperateUserName(user.getUserName());
        return ownerSubsidyService.importOwnerSubsidyInfo(importOwnerInfoDTO);
    }

    @RequestLock
    @PostMapping("/importSubsidyRemark")
    @ApiOperation(value = "导入补贴标识", httpMethod = "POST")
    public BaseResponse importSubsidyRemark(@RequestParam("file") MultipartFile multipartFile, @SessionAttribute("user") UserSession user){
        ImportSubsidyRemarkDTO importSubsidyRemarkDTO = new ImportSubsidyRemarkDTO();
        importSubsidyRemarkDTO.setMultipartFile(multipartFile);
        importSubsidyRemarkDTO.setOrgId(user.getOrgId());
        importSubsidyRemarkDTO.setOrgName(user.getOrgName());
        importSubsidyRemarkDTO.setOperateUserId(user.getUserId());
        importSubsidyRemarkDTO.setOperateUserName(user.getUserName());
        return ownerSubsidyService.importSubsidyRemark(importSubsidyRemarkDTO);
    }

    @RequestLock
    @PostMapping("/submit")
    @ApiOperation(value = "提交补贴信息", httpMethod = "POST")
    public BaseResponse submit(@RequestBody SubmitOwnerSubsidyInfoVO submitOwnerSubsidyInfoVO, @SessionAttribute("user") UserSession user){
        SubmitOwnerSubsidyInfoDTO submitOwnerSubsidyInfoDTO = new SubmitOwnerSubsidyInfoDTO();
        submitOwnerSubsidyInfoDTO.setIds(submitOwnerSubsidyInfoVO.getIds());
        submitOwnerSubsidyInfoDTO.setUpdatedUserId(user.getUserId());
        submitOwnerSubsidyInfoDTO.setUpdatedUserName(user.getUserName());
        return ownerSubsidyService.submit(submitOwnerSubsidyInfoDTO);
    }

    @RequestLock
    @PostMapping("/importAuthCopy")
    @ApiOperation(value = "导入证件复印件", httpMethod = "POST")
    public BaseResponse importAuthCopy(@RequestParam("file") MultipartFile file, @SessionAttribute("user") UserSession user){
        return ownerSubsidyService.importAuthCopy(copyFile(file,user));
    }

    @PostMapping("/queryAuthCopy")
    @ApiOperation(value = "查询证件复印件", httpMethod = "POST")
    public FileInfoBO queryAuthCopy(@RequestBody QueryAuthCopyVO queryAuthCopyVO){
        return ownerSubsidyService.queryAuthCopy(queryAuthCopyVO.getId());
    }

    @RequestLock
    @PostMapping("/importVehicleLicenseCopy")
    @ApiOperation(value = "导入行驶证扫描件", httpMethod = "POST")
    public BaseResponse importVehicleLicenseCopy(@RequestParam("file") MultipartFile file, @SessionAttribute("user") UserSession user){
        return ownerSubsidyService.importVehicleLicenseCopy(copyFile(file,user));
    }

    @PostMapping("/queryVehicleLicenseCopy")
    @ApiOperation(value = "查询行驶证扫描件", httpMethod = "POST")
    public FileInfoBO queryVehicleLicenseCopy(@RequestBody QueryVehicleLicenseCopyVO queryVehicleLicenseCopyVO){
        return ownerSubsidyService.queryVehicleLicenseCopy(queryVehicleLicenseCopyVO.getId());
    }

    @RequestLock
    @PostMapping("/importInvoiceCopy")
    @ApiOperation(value = "导入购车发票扫描件", httpMethod = "POST")
    public BaseResponse importInvoiceCopy(@RequestParam("file") MultipartFile file, @SessionAttribute("user") UserSession user){
        return ownerSubsidyService.importInvoiceCopy(copyFile(file,user));
    }






    @PostMapping("/queryInvoiceCopy")
    @ApiOperation(value = "查询购车发票扫描件", httpMethod = "POST")
    public FileInfoBO queryInvoiceCopy(@RequestBody QueryInvoiceCopyVO queryInvoiceCopyVO){
        return ownerSubsidyService.queryInvoiceCopy(queryInvoiceCopyVO.getId());
    }

    @RequestLock
    @PostMapping("/importOrgCodeCopy")
    @ApiOperation(value = "导入社会信用代码证", httpMethod = "POST")
    public BaseResponse importOrgCodeCopy(@RequestParam("file") MultipartFile file, @SessionAttribute("user") UserSession user){
        return ownerSubsidyService.importOrgCodeCopy(copyFile(file,user));
    }

    @PostMapping("/queryOrgCodeCopy")
    @ApiOperation(value = "查询社会信用代码证", httpMethod = "POST")
    public FileInfoBO queryOrgCodeCopy(@RequestBody QueryOrgCodeCopyVO queryOrgCodeCopyVO){
        return ownerSubsidyService.queryOrgCodeCopy(queryOrgCodeCopyVO.getId());
    }

    @RequestLock
    @PostMapping("/approve")
   @ApiOperation(value = "补贴审核通过", httpMethod = "POST")
    public BaseResponse approve(@RequestBody ApproveOwnerSubsidyInfoVO approveOwnerSubsidyInfoVO, @SessionAttribute("user") UserSession user){
       ApproveOwnerSubsidyInfoDTO approveOwnerSubsidyInfoDTO = new ApproveOwnerSubsidyInfoDTO();
       approveOwnerSubsidyInfoDTO.setIds(approveOwnerSubsidyInfoVO.getIds());
       approveOwnerSubsidyInfoDTO.setUpdatedUserId(user.getUserId());
       approveOwnerSubsidyInfoDTO.setUpdatedUserName(user.getUserName());
        return ownerSubsidyService.approve(approveOwnerSubsidyInfoDTO);
    }

    @RequestLock
    @PostMapping("/deny")
    @ApiOperation(value = "补贴审核拒绝", httpMethod = "POST")
    public BaseResponse deny(@RequestBody DenyOwnerSubsidyInfoVO denyOwnerSubsidyInfoVO, @SessionAttribute("user") UserSession user){
        DenyOwnerSubsidyInfoDTO denyOwnerSubsidyInfoDTO = new DenyOwnerSubsidyInfoDTO();
        denyOwnerSubsidyInfoDTO.setIds(denyOwnerSubsidyInfoVO.getIds());
        denyOwnerSubsidyInfoDTO.setReason(denyOwnerSubsidyInfoVO.getReason());
        denyOwnerSubsidyInfoDTO.setUpdatedUserId(user.getUserId());
        denyOwnerSubsidyInfoDTO.setUpdatedUserName(user.getUserName());
        return ownerSubsidyService.deny(denyOwnerSubsidyInfoDTO);
    }

    @RequestLock
    @PostMapping("/cancel")
    @ApiOperation(value = "撤销补贴审核", httpMethod = "POST")
    public BaseResponse cancel(@RequestBody CancelOwnerSubsidyInfoVO cancelOwnerSubsidyInfoVO, @SessionAttribute("user") UserSession user){
        CancelOwnerSubsidyInfoDTO cancelOwnerSubsidyInfoDTO = new CancelOwnerSubsidyInfoDTO();
        cancelOwnerSubsidyInfoDTO.setId(cancelOwnerSubsidyInfoVO.getId());
        cancelOwnerSubsidyInfoDTO.setUpdatedUserId(user.getUserId());
        cancelOwnerSubsidyInfoDTO.setUpdatedUserName(user.getUserName());
        return ownerSubsidyService.cancel(cancelOwnerSubsidyInfoDTO);
    }

    @Authorize(accessAuthorize = false)
    @PostMapping("/searchSubsidyUploadDataNotes")
    @ApiOperation(value = "查询补贴数据导入日志", httpMethod = "POST")
    public PageInfoBO<UploadFileNotes> searchSubsidyUploadDataNotes(@RequestBody SearchSubsidyUploadDataNotesVO searchSubsidyUploadDataNotesVO, @SessionAttribute("user") UserSession user){
        SearchSubsidyUploadDataNotesDTO searchSubsidyUploadDataNotesDTO = ConvertUtil.convert(searchSubsidyUploadDataNotesVO, SearchSubsidyUploadDataNotesDTO.class);
        searchSubsidyUploadDataNotesDTO.setOrgId(user.getOrgId());
        return ownerSubsidyService.searchSubsidyUploadDataNotes(searchSubsidyUploadDataNotesDTO);
    }

    @Authorize(accessAuthorize = false)
    @PostMapping("/searchUploadFileNotes")
    @ApiOperation(value = "查询补贴扫描件导入日志", httpMethod = "POST")
    public PageInfoBO<UploadFileNotes> searchSubsidyUploadFileNotes(@RequestBody @Valid SearchSubsidyUploadFileNotesVO searchSubsidyUploadFileNotesVO, @SessionAttribute("user") UserSession user){
        SearchSubsidyUploadFileNotesDTO searchSubsidyUploadFileNotesDTO = ConvertUtil.convert(searchSubsidyUploadFileNotesVO, SearchSubsidyUploadFileNotesDTO.class);
        //根据机构进行查询，统一机构内，日志数据共享
        searchSubsidyUploadFileNotesDTO.setOrgId(user.getOrgId());

        switch (searchSubsidyUploadFileNotesVO.getSubsidyFileType()){
            case 6:
                searchSubsidyUploadFileNotesDTO.setSubsidyFileType(FileTypeEnum.AUTH_COPY);
                break;
            case 7:
                searchSubsidyUploadFileNotesDTO.setSubsidyFileType(FileTypeEnum.INVOICE_COPY);
                break;
            case 8:
                searchSubsidyUploadFileNotesDTO.setSubsidyFileType(FileTypeEnum.ORG_CODE_COPY);
                break;
            case 9:
                searchSubsidyUploadFileNotesDTO.setSubsidyFileType(FileTypeEnum.VEHICLE_LICENSE_COPY);
                break;
            default:
                throw new ServiceException("参数异常");
        }
        return ownerSubsidyService.searchSubsidyUploadFileNotes(searchSubsidyUploadFileNotesDTO);
    }

    //设置备注
    public BaseResponse setSubsidyRemark(){

        return new BaseResponse();
    }

    private ImportFileCopyDTO copyFile(MultipartFile file,UserSession user){
        ImportFileCopyDTO importFileCopyDTO = new ImportFileCopyDTO();
        importFileCopyDTO.setMultipartFile(file);
        importFileCopyDTO.setOrgId(user.getOrgId());
        importFileCopyDTO.setUpdatedUserId(user.getUserId());
        importFileCopyDTO.setUpdatedUserName(user.getUserName());
        return importFileCopyDTO;
    }
}
