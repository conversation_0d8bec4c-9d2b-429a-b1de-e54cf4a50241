package com.extracme.nevmp.controller;

import com.extracme.nevmp.auth.Authorize;
import com.extracme.nevmp.service.VehicleModelExamineService;
import com.extracme.nevmp.vo.SearchVehicleModelVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Description 车辆审核相关controller
 */
@Authorize
@RestController
@RequestMapping("/vehicleModelExamine")
public class VehicleModelExamineController {

    @Autowired
    private VehicleModelExamineService vehicleModelExamineService;

    @GetMapping("/test")
    public void test(){
        vehicleModelExamineService.test();
    }

    @PostMapping("/searchVehicleModel")
    public void searchVehicleModel(@RequestBody SearchVehicleModelVO searchVehicleModelVO){

    }


}
