package com.extracme.nevmp.controller;

import com.danga.MemCached.MemCachedClient;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * <AUTHOR>
 * @date 2020/11/27
 */
@RestController
public class TestConnController {

    private MemCachedClient memCachedClient;

    @Autowired
    private SqlSessionTemplate sqlSessionTemplate;

    @RequestMapping("testconnection")
    public String testConnection() throws SQLException {
        memCachedClient = new MemCachedClient();
        memCachedClient.stats();
        SqlSessionFactory sqlSessionFactory = sqlSessionTemplate.getSqlSessionFactory();
        SqlSession sqlSession = sqlSessionFactory.openSession(true);
        Connection connection = sqlSession.getConnection();
        PreparedStatement preparedStatement = connection.prepareStatement("select now()");
        ResultSet resultSet = preparedStatement.executeQuery();
        if (resultSet.next()) {
            System.out.println(resultSet.getTimestamp(1));
        }
        return "success";
    }
}
