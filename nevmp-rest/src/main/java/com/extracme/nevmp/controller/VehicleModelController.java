package com.extracme.nevmp.controller;

import com.extracme.nevmp.auth.Authorize;
import com.extracme.nevmp.auth.UserSession;
import com.extracme.nevmp.converter.ConvertUtil;
import com.extracme.nevmp.dto.common.ComboInfoBO;
import com.extracme.nevmp.dto.vehicle.model.ComboVehicleModelDTO;
import com.extracme.nevmp.enums.OrgKindEnum;
import com.extracme.nevmp.service.VehicleModelService;
import com.extracme.nevmp.vo.ComboVehicleModelVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Description
 */
@Authorize
@RestController
@RequestMapping("vehicleModel")
public class VehicleModelController {

    @Autowired
    private VehicleModelService vehicleModelService;

    /**
     * 车型下拉框
     * @return
     */
    @Authorize(accessAuthorize = false)
    @PostMapping("/comboVehicleModel")
    public ComboInfoBO<String,String> comboVehicleModel(@RequestBody ComboVehicleModelVO comboVehicleModelVO,@SessionAttribute("user") UserSession user){
        ComboVehicleModelDTO comboVehicleModelDTO = ConvertUtil.convert(comboVehicleModelVO, ComboVehicleModelDTO.class);
        //校验登录人的机构信息
        if(!OrgKindEnum.MANAGER.getValue().equals(user.getOrgKind())){
            comboVehicleModelDTO.setOrgId(user.getOrgId());
        }
        return vehicleModelService.comboVehicleModel(comboVehicleModelDTO);
    }
}
