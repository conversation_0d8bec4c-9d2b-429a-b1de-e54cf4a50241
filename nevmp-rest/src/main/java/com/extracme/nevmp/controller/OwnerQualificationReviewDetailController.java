package com.extracme.nevmp.controller;

import com.extracme.nevmp.api.BaseResponse;
import com.extracme.nevmp.auth.Authorize;
import com.extracme.nevmp.auth.UserSession;
import com.extracme.nevmp.converter.ConvertUtil;
import com.extracme.nevmp.dto.CreditDenyDTO;
import com.extracme.nevmp.service.qualification.CreditService;
import com.extracme.nevmp.vo.CreditDenyVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2020/11/5
 */
@Authorize
@RestController
@RequestMapping("ownerReviewDetail")
@Api("用户资质审核子项")
public class OwnerQualificationReviewDetailController {

    @Autowired
    private CreditService creditService;

    @PostMapping("creditDeny")
    @ApiOperation("信用审核拒绝")
    public BaseResponse creditDeny(@RequestBody @Validated CreditDenyVO creditDenyVO, @SessionAttribute("user") UserSession userSession) {
        CreditDenyDTO creditDenyDTO = ConvertUtil.convert(creditDenyVO, CreditDenyDTO.class);
        creditDenyDTO.setUserId(userSession.getUserId());
        creditDenyDTO.setUserName(userSession.getUserName());
        return creditService.creditDeny(creditDenyDTO);
    }

}
