package com.extracme.nevmp.controller;

import com.extracme.nevmp.auth.Authorize;
import com.extracme.nevmp.dto.common.ComboInfoBO;
import com.extracme.nevmp.service.common.CommonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 通用服务
 * <AUTHOR>
 */
@RestController
@RequestMapping("common")
@Validated
public class CommonController {

    @Autowired
    private CommonService commonService;

    @Authorize(accessAuthorize = false)
    @GetMapping("/comboCountry")
    public ComboInfoBO<String, String> comboCountry() {
        return commonService.comboCountry();
    }

}
