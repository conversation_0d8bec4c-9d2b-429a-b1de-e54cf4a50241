package com.extracme.nevmp.controller;

import com.extracme.nevmp.api.BaseResponse;
import com.extracme.nevmp.auth.Authorize;
import com.extracme.nevmp.auth.UserSession;
import com.extracme.nevmp.converter.ConvertUtil;
import com.extracme.nevmp.dto.common.PageInfoBO;
import com.extracme.nevmp.dto.gov.traffic.NewEnergyCarAudit;
import com.extracme.nevmp.dto.gov.traffic.NewEnergyCarAuditCancel;
import com.extracme.nevmp.dto.owner.traffic.QueryTrafficPushWarnDTO;
import com.extracme.nevmp.dto.TrafficRePushDTO;
import com.extracme.nevmp.dto.owner.traffic.TrafficPushWarnBO;
import com.extracme.nevmp.service.owner.OwnerService;
import com.extracme.nevmp.service.traffic.impl.TrafficPushServiceImpl;
import com.extracme.nevmp.utils.CommonUtil;
import com.extracme.nevmp.vo.QueryTrafficPushWarnVO;
import com.extracme.nevmp.vo.TrafficRePushVO;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2020/11/5
 */
@Authorize
@RestController
@Api("交委推送")
@RequestMapping("trafficPush")
public class TrafficPushController {

    @Autowired
    private OwnerService ownerService;

    @Autowired
    private TrafficPushServiceImpl trafficPushService;



    @PostMapping("rePush")
    public BaseResponse rePush(@RequestBody @Validated TrafficRePushVO trafficRePushVO) {
        TrafficRePushDTO trafficRePushDTO = ConvertUtil.convert(trafficRePushVO, TrafficRePushDTO.class);
        return ownerService.rePush(trafficRePushDTO);
    }

    @PostMapping("trafficPushWarn")
    public PageInfoBO<TrafficPushWarnBO> trafficPushWarn(@RequestBody QueryTrafficPushWarnVO queryTrafficPushWarnVO, @SessionAttribute("user") UserSession user) {
        QueryTrafficPushWarnDTO queryTrafficPushWarnDTO = new QueryTrafficPushWarnDTO();
        queryTrafficPushWarnDTO.setOrgId(user.getOrgId());
        queryTrafficPushWarnDTO.setLimit(queryTrafficPushWarnVO.getPageSize());
        queryTrafficPushWarnDTO.setOffset(CommonUtil.getOffset(queryTrafficPushWarnVO.getPageNum(), queryTrafficPushWarnVO.getPageSize()));
        return ownerService.trafficPushWarn(queryTrafficPushWarnDTO);
    }


    @Authorize(accessAuthorize = false)
    @PostMapping("trafficCancelNewVehicle")
    public void trafficCancelNewVehicle(@RequestBody NewEnergyCarAuditCancel newEnergyCarAuditCancel){
        trafficPushService.cancelNewEnergyCarAuditRstAndQuotaAudit(newEnergyCarAuditCancel);
    }

    @Authorize(accessAuthorize = false)
    @PostMapping("trafficSendNewVehicleOnlyPush")
    public void trafficSendNewVehicleOnlyPush(@RequestBody NewEnergyCarAudit newEnergyCarAudit) throws Exception {
        newEnergyCarAudit.setValidDate("2015-01-01");
        trafficPushService.syncNewEnergyCarAuditRstOnlyPush(newEnergyCarAudit);
    }

}
