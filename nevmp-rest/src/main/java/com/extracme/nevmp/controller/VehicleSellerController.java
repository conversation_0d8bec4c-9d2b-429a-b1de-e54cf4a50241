package com.extracme.nevmp.controller;

import com.extracme.nevmp.api.BaseResponse;
import com.extracme.nevmp.auth.Authorize;
import com.extracme.nevmp.auth.UserSession;
import com.extracme.nevmp.converter.ConvertUtil;
import com.extracme.nevmp.dto.RenewVehicleSellerDTO;
import com.extracme.nevmp.dto.common.ComboInfoBO;
import com.extracme.nevmp.dto.common.FileInfoBO;
import com.extracme.nevmp.dto.common.PageInfoBO;
import com.extracme.nevmp.dto.vehicle.seller.*;
import com.extracme.nevmp.enums.OrgKindEnum;
import com.extracme.nevmp.service.vehicle.VehicleDealerService;
import com.extracme.nevmp.utils.CommonUtil;
import com.extracme.nevmp.vo.RenewVehicleSellerVO;
import com.extracme.nevmp.vo.common.SingelStrId;
import com.extracme.nevmp.vo.vehicle.seller.*;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Authorize
@RestController
    @RequestMapping("vehicleSeller")
public class VehicleSellerController {

    @Autowired
    private VehicleDealerService vehicleDealerService;


    @Authorize(accessAuthorize = false)
    @GetMapping("/comboVehicleSeller")
    public ComboInfoBO<String, String> comboVehicleSeller(@SessionAttribute("user")UserSession userSession) {
        String orgId;
        if(!OrgKindEnum.MANAGER.getValue().equals(userSession.getOrgKind())){
            orgId  = userSession.getOrgId();
        }else{
            orgId = null;
        }
        return vehicleDealerService.comboVehicleDealer(orgId);
    }


    /**
     * 保存经销商信息
     * @param saveVehicleSellerVO
     * @param userSession
     * @return
     */
    @PostMapping("/saveVehicleSeller")
    public BaseResponse saveVehicleSeller(@RequestBody @Valid SaveVehicleSellerVO saveVehicleSellerVO, @SessionAttribute("user")UserSession userSession) {
        SaveVehicleSellerDTO saveVehicleSellerDTO = ConvertUtil.convert(saveVehicleSellerVO, SaveVehicleSellerDTO.class);
        saveVehicleSellerDTO.setDealerName(saveVehicleSellerDTO.getDealerName().trim());
        saveVehicleSellerDTO.setOrgId(userSession.getOrgId());
        saveVehicleSellerDTO.setOrgName(userSession.getOrgName());
        saveVehicleSellerDTO.setUserId(userSession.getUserId());
        saveVehicleSellerDTO.setUserName(userSession.getUserName());
        return vehicleDealerService.saveVehicleSeller(saveVehicleSellerDTO);
    }

    /**
     * 获取经销商信息
     * @param sellerId
     * @param userSession
     * @return
     */
    @PostMapping("/getVehicleSeller")
    public BaseResponse getVehicleSeller(@RequestBody SingelStrId sellerId, @SessionAttribute("user")UserSession userSession) {
        return vehicleDealerService.getVehicleSeller(sellerId.getId(),  userSession.getOrgId());
    }

    /**
     * 更新经销商信息
     * @param updateVehicleSellerVO
     * @param userSession
     * @return
     */
    @PostMapping("/updateVehicleSeller")
    public BaseResponse updateVehicleSeller(@RequestBody @Validated UpdateVehicleSellerVO updateVehicleSellerVO, @SessionAttribute("user")UserSession userSession) {
        UpdateVehicleSellerDTO updateVehicleSellerDTO = ConvertUtil.convert(updateVehicleSellerVO, UpdateVehicleSellerDTO.class);
        updateVehicleSellerDTO.setDealerName(updateVehicleSellerDTO.getDealerName().trim());
        updateVehicleSellerDTO.setOrgId(userSession.getOrgId());
        updateVehicleSellerDTO.setOrgName(userSession.getOrgName());
        updateVehicleSellerDTO.setUserId(userSession.getUserId());
        updateVehicleSellerDTO.setUserName(userSession.getUserName());

        return vehicleDealerService.updateVehicleSeller(updateVehicleSellerDTO);
    }

    /**
     * 删除经销商信息
     * @param deleteVehicleSellerVO
     * @return
     */
    @PostMapping("/deleteVehicleSeller")
    public BaseResponse deleteVehicleSeller(@RequestBody @Validated DeleteVehicleSellerVO deleteVehicleSellerVO, @SessionAttribute("user") UserSession userSession) {
        DeleteVehicleSellerDTO deleteVehicleSellerDTO = new DeleteVehicleSellerDTO();
        deleteVehicleSellerDTO.setDealerId(deleteVehicleSellerVO.getDealerId());
        deleteVehicleSellerDTO.setOrgId(userSession.getOrgId());
        deleteVehicleSellerDTO.setUserId(userSession.getUserId());
        deleteVehicleSellerDTO.setUserName(userSession.getUserName());
        return vehicleDealerService.deleteVehicleSeller(deleteVehicleSellerDTO);
    }

    /**
     * 查询经销商信息
     * @param searchVehicleSellerVO
     * @return
     */
    @PostMapping("/searchVehicleSeller")
    public PageInfoBO searchVehicleSeller(@RequestBody SearchVehicleSellerVO searchVehicleSellerVO, @SessionAttribute("user") UserSession userSession) {
        SearchVehicleSellerDTO searchVehicleSellerDTO = ConvertUtil.convert(searchVehicleSellerVO, SearchVehicleSellerDTO.class);
        searchVehicleSellerDTO.setOrgId(userSession.getOrgId());
        searchVehicleSellerDTO.setOffset(CommonUtil.getOffset(searchVehicleSellerVO.getPageNum(), searchVehicleSellerVO.getPageSize()));
        searchVehicleSellerDTO.setLimit(searchVehicleSellerVO.getPageSize());
        return vehicleDealerService.searchVehicleSeller(searchVehicleSellerDTO);
    }

    /**
     * 提交经销商信息
     * @param multiVehicleSellerIdVO
     * @param userSession
     * @return
     */
    @PostMapping("/submitVehicleSeller")
    public BaseResponse submitVehicleSeller(@RequestBody @Validated MultiVehicleSellerIdVO multiVehicleSellerIdVO, @SessionAttribute("user") UserSession userSession) {
        MultiVehicleSellerIdDTO multiVehicleSellerIdDTO = new MultiVehicleSellerIdDTO();
        multiVehicleSellerIdDTO.setIds(multiVehicleSellerIdVO.getIds());
        multiVehicleSellerIdDTO.setUserId(userSession.getUserId());
        multiVehicleSellerIdDTO.setUserName(userSession.getUserName());
        multiVehicleSellerIdDTO.setOrgId(userSession.getOrgId());
        multiVehicleSellerIdDTO.setOrgName(userSession.getOrgName());
        return vehicleDealerService.submitVehicleSeller(multiVehicleSellerIdDTO);
    }

    @ApiOperation("经销商续期")
    @PostMapping("/renewVehicleSeller")
    @Authorize(accessAuthorize = false)
    public BaseResponse renewVehicleSeller(@RequestBody @Validated RenewVehicleSellerVO renewVehicleSellerVO, @SessionAttribute("user") UserSession userSession) {
        RenewVehicleSellerDTO renewVehicleSellerDTO = ConvertUtil.convert(renewVehicleSellerVO, RenewVehicleSellerDTO.class);
        renewVehicleSellerDTO.setUserId(userSession.getUserId());
        renewVehicleSellerDTO.setUserName(userSession.getUserName());
        renewVehicleSellerDTO.setOrgId(userSession.getOrgId());
        return vehicleDealerService.renewVehicleSeller(renewVehicleSellerDTO);
    }


    /**
     * 审批通过
     * @param multiVehicleSellerIdVO
     * @param userSession
     * @return
     */
    @PostMapping("/approve")
    public BaseResponse approveVehicleSeller(@RequestBody @Validated MultiVehicleSellerIdVO multiVehicleSellerIdVO, @SessionAttribute("user") UserSession userSession) {
        MultiVehicleSellerIdDTO multiVehicleSellerIdDTO = new MultiVehicleSellerIdDTO();
        multiVehicleSellerIdDTO.setIds(multiVehicleSellerIdVO.getIds());
        multiVehicleSellerIdDTO.setUserId(userSession.getUserId());
        multiVehicleSellerIdDTO.setUserName(userSession.getUserName());
        return vehicleDealerService.approveVehicleSeller(multiVehicleSellerIdDTO);
    }

    /**
     * 审批拒绝
     * @param denyVehicleSellerIdVO
     * @param userSession
     * @return
     */
    @PostMapping("/denyVehicleSeller")
    public BaseResponse denyVehicleSeller(@RequestBody @Validated DenyVehicleSellerIdVO denyVehicleSellerIdVO, @SessionAttribute("user") UserSession userSession) {
        MultiVehicleSellerIdDTO multiVehicleSellerIdDTO = new MultiVehicleSellerIdDTO();
        multiVehicleSellerIdDTO.setIds(denyVehicleSellerIdVO.getIds());
        multiVehicleSellerIdDTO.setUserId(userSession.getUserId());
        multiVehicleSellerIdDTO.setUserName(userSession.getUserName());
        return vehicleDealerService.denyVehicleSeller(multiVehicleSellerIdDTO, denyVehicleSellerIdVO.getCancelReason());
    }

    /**
     * 失效
     * @param multiVehicleSellerIdVO
     * @param userSession
     * @return
     */
    @PostMapping("invalidVehicleSeller")
    public BaseResponse invalidVehicleSeller(@RequestBody @Validated MultiVehicleSellerIdVO multiVehicleSellerIdVO, @SessionAttribute("user") UserSession userSession) {
        MultiVehicleSellerIdDTO multiVehicleSellerIdDTO = new MultiVehicleSellerIdDTO();
        multiVehicleSellerIdDTO.setIds(multiVehicleSellerIdVO.getIds());
        multiVehicleSellerIdDTO.setUserId(userSession.getUserId());
        multiVehicleSellerIdDTO.setUserName(userSession.getUserName());
        return vehicleDealerService.invalidVehicleSeller(multiVehicleSellerIdDTO);
    }

    /**
     * 查询审批数据
     * @param searchApproveVehicleSellerVO
     * @return
     */
    @PostMapping("searchApproveVehicleSeller")
    public BaseResponse searchApproveVehicleSeller(@RequestBody SearchApproveVehicleSellerVO searchApproveVehicleSellerVO) {
        SearchApproveVehicleSellerDTO searchApproveVehicleSellerDTO = ConvertUtil.convert(searchApproveVehicleSellerVO, SearchApproveVehicleSellerDTO.class);
        searchApproveVehicleSellerDTO.setOffset(CommonUtil.getOffset(searchApproveVehicleSellerVO.getPageNum(), searchApproveVehicleSellerVO.getPageSize()));
        searchApproveVehicleSellerDTO.setLimit(searchApproveVehicleSellerVO.getPageSize());
        return vehicleDealerService.searchApproveVehicleSeller(searchApproveVehicleSellerDTO);

    }


    @Authorize(accessAuthorize = false)
    @PostMapping("/querySellerConfirmCopy")
    @ApiOperation(value = "查询经销商证明", httpMethod = "POST")
    public FileInfoBO querySellerConfirmCopy(@RequestBody QuerySellerConfirmVO querySellerConfirmVO) {
        QuerySellerConfirmDTO querySellerConfirmDTO = new QuerySellerConfirmDTO();
        querySellerConfirmDTO.setDealerId(querySellerConfirmVO.getDealerId());
        return vehicleDealerService.querySellerConfirmCopy(querySellerConfirmDTO);
    }

}
