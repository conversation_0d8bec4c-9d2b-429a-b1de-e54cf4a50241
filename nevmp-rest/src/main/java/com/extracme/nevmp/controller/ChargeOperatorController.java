package com.extracme.nevmp.controller;

import com.extracme.nevmp.api.BaseResponse;
import com.extracme.nevmp.auth.Authorize;
import com.extracme.nevmp.auth.UserSession;
import com.extracme.nevmp.converter.ConvertUtil;
import com.extracme.nevmp.dto.DeleteChargeBuildInfoDTO;
import com.extracme.nevmp.dto.charge.*;
import com.extracme.nevmp.dto.common.ComboInfoBO;
import com.extracme.nevmp.dto.common.PageInfoBO;
import com.extracme.nevmp.model.ChargeBuildInfo;
import com.extracme.nevmp.service.charger.ChargeOperatorService;
import com.extracme.nevmp.vo.DeleteChargeBuildInfoVO;
import com.extracme.nevmp.vo.charge.GetChargeBuildInfoDetailVO;
import com.extracme.nevmp.vo.charge.SavePrivateChargeBuildInfoVO;
import com.extracme.nevmp.vo.charge.SearchChargeBuildInfoVO;
import com.extracme.nevmp.vo.charge.UpdateChargeBuildInfoVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @Description 充电桩建设运营单位管理
 */
@RestController
@RequestMapping("chargeOperator")
@Validated
@Authorize
public class ChargeOperatorController {

    @Autowired
    private ChargeOperatorService chargeOperatorService;

    /**
     * 查询充电桩列表
     * @return
     */
    @PostMapping("/searchChargeBuildInfo")
    public PageInfoBO<ChargeBuildInfo> searchChargeBuildInfo(@RequestBody @Valid SearchChargeBuildInfoVO searchChargeBuildInfoVO, @SessionAttribute("user") UserSession user){
        SearchChargeBuildInfoDTO searchChargeBuildInfoDTO = ConvertUtil.convert(searchChargeBuildInfoVO, SearchChargeBuildInfoDTO.class);
        searchChargeBuildInfoDTO.setOrgId(user.getOrgId());
        return chargeOperatorService.searchChargeBuildInfo(searchChargeBuildInfoDTO);
    }


    /**
     * 保存私有充电桩建设信息
     */
    @PostMapping("/savePrivateChargeBuildInfo")
    public BaseResponse savePrivateChargeBuildInfo(@RequestBody @Valid SavePrivateChargeBuildInfoVO savePrivateChargeBuildInfoVO, @SessionAttribute("user") UserSession user){
        SavePrivateChargeBuildInfoDTO savePrivateChargeBuildInfoDTO = ConvertUtil.normalConvert(savePrivateChargeBuildInfoVO,SavePrivateChargeBuildInfoDTO.class);
        savePrivateChargeBuildInfoDTO.setOrgId(user.getOrgId());
        savePrivateChargeBuildInfoDTO.setCreatedUserId(user.getUserId());
        savePrivateChargeBuildInfoDTO.setCreatedUserName(user.getUserName());
        return chargeOperatorService.savePrivateChargeBuildInfo(savePrivateChargeBuildInfoDTO);
    }

    /**
     * 获取充电桩建设信息详情
     * @return
     */
    @PostMapping("/getChargeBuildInfoDetail")
    public ChargeBuildInfoDetailDTO getChargeBuildInfoDetail(@RequestBody GetChargeBuildInfoDetailVO getChargeBuildInfoDetailVO, @SessionAttribute("user") UserSession user){
        GetChargeBuildInfoDetailDTO getChargeBuildInfoDetailDTO = new GetChargeBuildInfoDetailDTO();
        getChargeBuildInfoDetailDTO.setChargeBuildSeq(getChargeBuildInfoDetailVO.getChargeBuildSeq());
        getChargeBuildInfoDetailDTO.setOrgId(user.getOrgId());
        return chargeOperatorService.getChargeBuildInfoDetail(getChargeBuildInfoDetailDTO);
    }

    /**
     * 更新私有充电桩建设信息
     */
    @PostMapping("/updateChargeOperator")
    public BaseResponse updateChargeOperator(@RequestBody UpdateChargeBuildInfoVO updateChargeBuildInfoVO, @SessionAttribute("user") UserSession user){
        UpdateChargeBuildInfoDTO updateChargeBuildInfoDTO = new UpdateChargeBuildInfoDTO();
        BeanUtils.copyProperties(updateChargeBuildInfoVO, updateChargeBuildInfoDTO);
        updateChargeBuildInfoDTO.setOrgId(user.getOrgId());
        updateChargeBuildInfoDTO.setUpdatedUserId(user.getUserId());
        updateChargeBuildInfoDTO.setUpdatedUserName(user.getUserName());
        return chargeOperatorService.updateChargeOperator(updateChargeBuildInfoDTO);
    }

    /**
     * 删除充电桩信息
     * @param deleteChargeBuildInfoVO
     * @param user
     * @return
     */
    @PostMapping("/deleteChargeOperator")
    public BaseResponse deleteChargeOperator(@RequestBody @Validated DeleteChargeBuildInfoVO deleteChargeBuildInfoVO, @SessionAttribute("user") UserSession user) {
        DeleteChargeBuildInfoDTO deleteChargeBuildInfoDTO = ConvertUtil.normalConvert(deleteChargeBuildInfoVO, DeleteChargeBuildInfoDTO.class);
        return chargeOperatorService.deleteChargeOperator(deleteChargeBuildInfoDTO);
    }


    /**
     * 充电桩运营单位下拉框
     * @return
     */
    @Authorize(accessAuthorize = false)
    @GetMapping("/comboChargeOperator")
    ComboInfoBO<String, String> comboChargeOperator(){
        return chargeOperatorService.comboChargeOperator();
    }
}
