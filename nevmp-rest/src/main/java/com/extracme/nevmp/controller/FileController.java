package com.extracme.nevmp.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.SessionAttribute;
import org.springframework.web.multipart.MultipartFile;

import com.extracme.nevmp.auth.Authorize;
import com.extracme.nevmp.auth.RequestLock;
import com.extracme.nevmp.auth.UserSession;
import com.extracme.nevmp.dto.common.FileInfoBO;
import com.extracme.nevmp.dto.common.FileInfoDTO;
import com.extracme.nevmp.dto.file.FileResponse;
import com.extracme.nevmp.enums.FileTypeEnum;
import com.extracme.nevmp.mapper.OwnerInfoMapper;
import com.extracme.nevmp.model.OwnerInfo;
import com.extracme.nevmp.service.FileService;

import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @Description 文件相关服务
 */
@Authorize
@RestController
@RequestMapping(value = "file")
public class FileController {

    @Autowired
    private FileService fileService;
    @Autowired
    private OwnerInfoMapper ownerInfoMapper;

    @ApiOperation(value = "上传文件",
            notes = "fileType: 1:用户资质证明材料, 2:军官证明材料, 3:车辆合格证扫描件, 4:充电桩承诺书, 5:独立计量表扫描件, " +
                    "6:证件扫描件, 7:购车发票扫描件, 8:社会信用代码证扫描件, 9:行驶证扫描件, 10:充电桩照片, 11:经销商授权证明, " +
                    "12: 经销商续期授权证明, 13:特殊人才证明, 20:车辆信息导入, 21:充电条件确认信息导入, 30:充电桩自查反馈表,  31: 充电桩铭牌 40:充电桩建设信息导入, " +
                    "50:车型申请书, 51:车企车型证明材料, 52:数据中心车型证明材料, 53:车型评审报告, 54:车型质量抽查表, 55:车型参数表")
    @RequestLock
    @Authorize(accessAuthorize = false)
    @RequestMapping(value = "upload", method = RequestMethod.POST)
    public FileResponse upload(@RequestParam("file") MultipartFile file, @RequestParam("type") Integer fileType, @SessionAttribute("user") UserSession user) {
        return fileService.upload(file, fileType, user.getUserId(), user.getOrgId());
    }


    @ApiOperation(value = "获取文件",
            notes = "fileType: 1:用户资质证明材料, 2:军官证明材料, 3:车辆合格证扫描件, 4:充电桩承诺书, 5:独立计量表扫描件, " +
                    "6:证件扫描件, 7:购车发票扫描件, 8:社会信用代码证扫描件, 9:行驶证扫描件, 10:充电桩照片, 11:经销商授权证明, " +
                    "12: 经销商续期授权证明, 13:特殊人才证明, 20:车辆信息导入, 21:充电条件确认信息导入, 30:充电桩自查反馈表,  31: 充电桩铭牌 40:充电桩建设信息导入, " +
                    "50:车型申请书, 51:车企车型证明材料, 52:数据中心车型证明材料, 53:车型评审报告, 54:车型质量抽查表, 55:车型参数表")
    @Authorize(accessAuthorize = false)
    @GetMapping(value = "getFile")
    public FileInfoBO getFile(@RequestParam("fileType") Integer fileType, @RequestParam("id") String id) {
        FileTypeEnum fileTypeEnum = FileTypeEnum.parse(fileType);
        List<FileInfoDTO> fileInfo = null;
        if (Integer.valueOf(1).equals(fileTypeEnum.getRelationIdType())) {
            long relationId = Long.parseLong(id);
            fileInfo = fileService.getFileInfo(fileTypeEnum, relationId);
        } else if (Integer.valueOf(2).equals(fileTypeEnum.getRelationIdType())) {
            fileInfo = fileService.getFileInfo(fileTypeEnum, id);
        }
        FileInfoBO fileInfoBO = new FileInfoBO();
        fileInfoBO.setFileInfo(fileInfo);
        fileInfoBO.setInfoMap(getMap(fileTypeEnum, id));
        return fileInfoBO;
    }


    public Map<String, Object> getMap(FileTypeEnum fileTypeEnum, String id) {
        Map<String, Object> map = null;
        switch (fileTypeEnum) {
            case SELF_CHECK_CHARGE_NAMEPLATE:
                map = getChargeNamePlateMap(id);
                break;
            default:
        }
        return map;
    }

    private Map<String, Object> getChargeNamePlateMap(String id) {
        Long aLong = Long.valueOf(id);
        Optional<OwnerInfo> ownerInfoOptional = ownerInfoMapper.selectByPrimaryKey(aLong);
        Map<String, Object> map = new HashMap<>();
        map.put("充电桩编号", ownerInfoOptional.get().getChargeSeq());
        map.put("充电桩建设运营单位", ownerInfoOptional.get().getChargeOperatorName());
        return map;
    }

}
