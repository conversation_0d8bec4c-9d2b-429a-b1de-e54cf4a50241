package com.extracme.nevmp.error;

import com.extracme.nevmp.api.BaseResponse;
import com.extracme.nevmp.api.ResultCode;
import com.extracme.nevmp.auth.UserSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @Description 异常处理转化类
 */
@RestControllerAdvice
public class RestExceptionTranslator {

    Logger logger = LoggerFactory.getLogger(RestExceptionTranslator.class);

    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public BaseResponse handleError(HttpServletRequest request, HttpRequestMethodNotSupportedException e){
        final Object user = request.getSession().getAttribute("user");
        if(user == null){
            logger.error("Method Argument Not Valid：" + request.getRequestURI());
        }else{
            UserSession userSession = (UserSession) user;
            logger.error("Method Argument Not Valid：" + request.getRequestURI() +" ;操作账号:" +userSession.getUserName() + "," + userSession.getOrgName());
        }
        return BaseResponse
                .builder()
                .resultCode(ResultCode.PARAM_VALID_ERROR)
                .message(e.getMessage())
                .build();
    }
}
