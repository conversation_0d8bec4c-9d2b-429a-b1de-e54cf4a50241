package com.extracme.nevmp;

import java.net.URLDecoder;
import java.util.Collection;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

import com.danga.MemCached.MemCachedClient;
import com.spring4all.swagger.EnableSwagger2Doc;

/**
 * <AUTHOR>
 */
@EnableSwagger2Doc
@SpringBootApplication
public class NevmpApplication {

	public static void main(String[] args) {
//        addShutdownHook();
		SpringApplication.run(NevmpApplication.class, args);
	}




	private static void addShutdownHook() {
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            MemCachedClient memCachedClient = null;
            try {
                System.out.println("==========shutdown hook===========");
                memCachedClient = new MemCachedClient();
                Map<String, Map<String, String>> stringMapMap = memCachedClient.statsItems();
                Set<Map.Entry<String, Map<String, String>>> entries = stringMapMap.entrySet();
                Set<String> keys = new HashSet<>();
                for (Map.Entry<String, Map<String, String>> entry : entries) {
                    for (String str : entry.getValue().keySet()) {
                        Pattern pattern = Pattern.compile("items:(\\d+):");
                        Matcher matcher = pattern.matcher(str);
                        matcher.find();
                        String keyId = matcher.group(1);
                        keys.add(keyId);
                    }
                }
                for (String keyId : keys) {
                    Map<String, Map<String, String>> stringMapMap1 = memCachedClient.statsCacheDump(Integer.parseInt(keyId), 0);
                    Collection<Map<String, String>> values = stringMapMap1.values();
                    for (Map<String, String> value : values) {
                        Set<String> keySet = value.keySet();
                        for (String key : keySet) {
                            if (key.startsWith("LockKey_")) {
                                System.out.println("delete key: " + URLDecoder.decode(key));
                                memCachedClient.delete(URLDecoder.decode(key));
                            }
                        }
                    }
                }
            }catch (Exception e) {
                e.printStackTrace();
            }
        }));
    }
}
