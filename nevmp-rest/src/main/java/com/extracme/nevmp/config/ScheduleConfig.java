package com.extracme.nevmp.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;

import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 * @Description 由于定时任务默认采用单线程的方式执行，当一个任务执行时间过长时，会导致其他任务不执行
 * 所以需要采用该类转化成多线程的方式执行定时任务
 * 后期改成分布式定时后，可以剔除该类
 */

@Configuration
public class ScheduleConfig implements SchedulingConfigurer {
    @Override
    public void configureTasks(ScheduledTaskRegistrar taskRegistrar) {
        taskRegistrar.setScheduler(Executors.newScheduledThreadPool( 5));
    }
}
