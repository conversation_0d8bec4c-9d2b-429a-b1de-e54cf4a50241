package com.extracme.nevmp.config;

import com.danga.MemCached.SockIOPool;
import com.extracme.nevmp.auth.AuthorizeInterceptor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 * @Description
 */
@Configuration
public class NevmpConfig implements WebMvcConfigurer {

    /**
     * memcache bean
     * @param address memcache地址
     * @return
     */
    @Bean(initMethod = "initialize", destroyMethod = "shutDown")
    SockIOPool memcachePool(@Value("${memcache.address}") String address){
        SockIOPool pool = SockIOPool.getInstance();
        pool.setServers(new String[]{address});
        return pool;
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new AuthorizeInterceptor());
    }

}
