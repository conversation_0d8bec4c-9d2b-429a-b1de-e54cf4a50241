package com.extracme.nevmp.vo.charge;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class SearchChargeBuildInfoVO {

    /**
     * 充电桩编号
     */
    @ApiModelProperty(value="充电桩编号")
    private String chargeNo;

    /**
     * 充电桩照片
     */
    @ApiModelProperty(value="充电桩照片 1:未上传  2： 已上传 ")
    private Integer hashChargeBuildPicCopy;

    /**
     * 每页显示条数
     */
    @NotNull
    @ApiModelProperty(example="10",required = true)
    private Long pageSize;

    /**
     * 页码
     */
    @NotNull
    @ApiModelProperty(example="1",required = true)
    private Long pageNum;
}
