package com.extracme.nevmp.vo.charge;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class SavePrivateChargeBuildInfoVO {


    /** 投入运营时间 **/
    @ApiModelProperty(value="投入运营时间")
    private Date startTime;

    /**
     * 范围
     * 1: 外环外
     * 2: 外环到中环
     * 3: 中环到内环
     * 4: 内环内
     */
    @ApiModelProperty(value="范围 1: 外环外 2: 外环到中环 3: 中环到内环 4: 内环内")
    private Integer ranger;

    /**
     * 区域
     */
    @ApiModelProperty(value="区域")
    private String district;

    /**
     * 街道
     */
    @ApiModelProperty(value="街道")
    private String street;

    /**
     * 安装单独计量表
     * 1: 是
     * 0：否
     */
    @ApiModelProperty(value="安装单独计量表  1:是 0：否")
    private String installInstrument;

    /**
     * 充电设备类型
     * AC220 ： 220V交流
     * DC380 ： 380V直流
     * OTHER ： 其他
     */
    @ApiModelProperty(value="充电设备类型  AC220:220V交流 DC380：380V直流  OTHER：其他")
    private String chargeType;

    /**
     * 充电设施编号
     */
    @ApiModelProperty(value="充电设施编号")
    private String chargeNo;

    /**
     * 服务费
     */
    @ApiModelProperty(value="服务费")
    private String serviceCharge;

    /**
     * 充电桩生产单位
     */
    @ApiModelProperty(value="充电桩生产单位")
    private String chargeManuName;

    /**
     * 充电桩建设单位
     */
    @ApiModelProperty(value="充电桩建设单位")
    private String chargeBuildName;

    /**
     * 备注
     */
    @ApiModelProperty(value="备注")
    private String remark;
}
